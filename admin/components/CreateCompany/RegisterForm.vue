<template>
  <form class="flex flex-col gap-3" @submit="submit">
    <input-base
      v-if="!userExist"
      v-model="name"
      :error="errors['name']"
      label="Nome Completo"
    />
    <input-base
      v-model="CRP"
      :error="errors['CRP']"
      label="CRP"
      data-maska="##/######"
    />

    <input-base
      class="w-full"
      label="Telefone"
      placeholder="(00) 00000-0000"
      data-maska="(##) #####-####"
      v-model="phone"
    />
    <input-base
      v-if="!userHasEmail"
      v-model="email"
      :error="errors['email']"
      label="Email"
    />
    <!-- <input-base
      v-model="company_name"
      :error="errors['company_name']"
      label="Nome do seu negócio"
    /> -->

    <div class="flex gap-3">
      <input-base
        :error="errors['password']"
        v-model="password"
        name="password"
        label="Senha"
        type="password"
      />
      <input-base
        v-if="!userExist"
        :error="errors['password_confirmation']"
        v-model="password_confirmation"
        name="password_confirmation"
        label="Confirmar senha"
        type="password"
      />
    </div>

    <base-button :loading="loading" class="w-full my-5 text-lg"
      >Cadastrar</base-button
    >
    <div class="text-center">
      <div class="mb-3">
        Já possui acesso?
        <nuxt-link to="/login" class="text-primary">Faça login</nuxt-link>
      </div>
      <div>
        Deseja acessar como paciente?
        <nuxt-link to="/login" class="text-primary">Acesse por aqui</nuxt-link>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
import { useForm } from "vee-validate";
import { useToast } from "vue-toast-notification";
import * as yup from "yup";
import { api } from "~/server/api";

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
});
const userSearched = ref(false);
const userExist = ref(false);
const userHasEmail = ref(false);
const emit = defineEmits(["submit"]);
const validationSchema = computed(() => {
  return yup.object().shape({
    name: userExist.value
      ? yup.string()
      : yup.string().required("O nome é obrigatório"),
    CRP: yup.string().required("O CRP é obrigatório"),
    phone: yup.string().required("O telefone é obrigatório"),
    email: userHasEmail.value
      ? yup.string().email("O email deve ser válido")
      : yup
          .string()
          .required("O email é obrigatório")
          .email("O email deve ser válido"),
    password: yup
      .string()
      .required("A senha é obrigatória")
      .min(8, "A senha deve ter no mínimo 8 caracteres"),
    password_confirmation: userExist.value
      ? yup.string()
      : yup
          .string()
          .required("Campo obrigatório")
          .oneOf([yup.ref("password")], "A senha não coincide."),
    company_name: yup.string().required("O nome do negócio é obrigatório"),
  });
});

const { handleSubmit, defineField, errors } = useForm({
  validationSchema,
});

const [name] = defineField("name");
const [company_name] = defineField("company_name");
const [phone] = defineField("phone");
const [email] = defineField("email");
const [password] = defineField("password");
const [password_confirmation] = defineField("password_confirmation");
const [CRP] = defineField("CRP");
function handleFormSubmit(values) {
  emit("submit", { ...values, has_email: userHasEmail.value });
}
const toast = useToast();
const submit = handleSubmit(handleFormSubmit);
// async function searchUser() {
//   if (!phone.value) return;
//   try {
//     const { data } = await api.get("/check-user", {
//       params: { phone: phone.value },
//     });
//     userSearched.value = true;

//     if (data.user) {
//       userExist.value = true;
//       userHasEmail.value = data.user.hasEmail ? true : false;
//     } else {
//       userExist.value = false;
//       userHasEmail.value = false;
//     }
//   } catch (err) {
//     console.error(err);
//     toast.error("Erro ao buscar usuário");
//   } finally {
//   }
// }
watch(name, (val) => {
  company_name.value = val;
});
</script>
