import axios from "axios";
// import { api } from "..";

// server/api/manifest.js
export default defineEventHandler(async (event) => {
  // console.log(process.env.API_URL);
  // const slug = "empresa-modelo2";
  // const teste = api.get('/')
  const slug = getRouterParam(event, 'slug')
  const { data } = await axios(`${process.env.API_URL}/${slug}/manifest`);
  // const dynamicRel = "some-dynamic-value"; 
  // return {
  //   name: "Meu PWA Dinâmico",
  //   short_name: "PWA",
  //   start_url: "/",
  //   display: "standalone",
  //   theme_color: "#000000",
  //   rel: dynamicRel, // Propriedade dinâmica
  // };
  return data
});
