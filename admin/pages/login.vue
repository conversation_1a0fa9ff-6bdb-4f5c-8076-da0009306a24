<template>
  <div class="min-h-screen flex items-center justify-center bg-base-200">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-auto flex justify-center">
          <img class="h-12 w-auto" src="/icons/psi.png" alt="Logo" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Super Admin Login
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Acesso restrito para administradores do sistema
        </p>
      </div>

      <div class="bg-base-100 rounded-lg shadow-lg p-8">
        <form @submit.prevent="onSubmit" class="space-y-6">
          <div>
            <input-base type="email" label="Email" v-model="email" :error="errors.email" required />
          </div>

          <div>
            <input-base type="password" label="Senha" v-model="password" :error="errors.password" required />
          </div>

          <div v-if="loginError" class="alert alert-error">
            <ExclamationCircleIcon class="w-5 h-5" />
            <span>{{ loginError }}</span>
          </div>

          <div>
            <base-button type="submit" class="w-full" :loading="loading">
              Entrar
            </base-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ExclamationCircleIcon } from "@heroicons/vue/24/solid";
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import { useForm } from "vee-validate";
import * as yup from "yup";
import { useLoginStore } from "~/store/user";
import { useNavigationBottom } from "~/store/navigationBottom";

definePageMeta({
  layout: false
});

const toast = useToast();
const loading = ref(false);
const loginError = ref('');
const loginStore = useLoginStore();

const schema = yup.object({
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  password: yup.string().required("Senha é obrigatória")
});

const { handleSubmit, defineField, errors } = useForm({
  validationSchema: schema
});

const [email] = defineField("email");
const [password] = defineField("password");

const onSubmit = handleSubmit(async (values) => {
  try {
    loading.value = true;
    loginError.value = '';

    const { data } = await api.post('/login-super-admin', values);

    // Use the existing login store
    loginStore.login(data.token, true); // true indicates super admin
    const userData = {
      email: data.user.email,
      image: data.user.image || '',
      name: data.user.name,
      phone: data.user.phone || '',
      role: 'super_admin',
    };
    loginStore.setUser(userData);

    // Force navigation refresh
    const navigationBottom = useNavigationBottom();
    navigationBottom.forceRefresh();

    toast.success('Login realizado com sucesso!');

    // Redirect to analytics dashboard
    await navigateTo('/analytics');

  } catch (err) {
    console.error(err);
    const error = err as { response?: { status: number } };
    if (error.response?.status === 401 || error.response?.status === 403) {
      loginError.value = 'Credenciais inválidas ou acesso negado';
    } else {
      loginError.value = 'Erro interno do servidor';
    }
  } finally {
    loading.value = false;
  }
});

// Check if already logged in
onMounted(() => {
  if (loginStore.isLoggedIn) {
    navigateTo('/analytics');
  }
});
</script>
