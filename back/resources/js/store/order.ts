import { getServices } from "@/server/api";
import type { Order } from "@/utils/models";
import { defineStore } from "pinia";

const baseUrl = "/orders";
const { getAll, create, deleteById } = getServices(baseUrl);

export const useOrderStore = defineStore("orders", {
  state: () => {
    return {
      orders: [] as Order[],
      loading: false,
    };
  },
  actions: {
    async addService(order: Order) {
      this.loading = true;
      try {
        await create(order);
      } catch (err) {
        throw err;
      } finally {
        this.loading = false;
      }
    },
    async deleteService(orderId: number) {
      this.loading = true;
      try {
        await deleteById(orderId);
      } catch (err) {
        throw err;
      } finally {
        this.loading = false;
      }
    },
    async getAllServices() {
      this.loading = true;
      try {
        return await getAll();
      } catch (err) {
        throw err;
      } finally {
        this.loading = false;
      }
    },
  },
  persist: true,
});
