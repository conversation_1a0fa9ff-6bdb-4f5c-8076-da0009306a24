/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppLogo: typeof import('./resources/js/components/AppLogo.vue')['default']
    BaseButton: typeof import('./resources/js/components/BaseButton.vue')['default']
    BaseDialog: typeof import('./resources/js/components/BaseDialog.vue')['default']
    BottomNavigation: typeof import('./resources/js/components/BottomNavigation.vue')['default']
    Cart: typeof import('./resources/js/components/Cart.vue')['default']
    CartCard: typeof import('./resources/js/components/CartCard.vue')['default']
    Currency: typeof import('./resources/js/components/Currency.vue')['default']
    FinishRegisterForm: typeof import('./resources/js/components/finish-register/Form.vue')['default']
    InputBase: typeof import('./resources/js/components/InputBase.vue')['default']
    Loading: typeof import('./resources/js/components/Loading.vue')['default']
    LoginForm: typeof import('./resources/js/components/login/Form.vue')['default']
    OldInfos: typeof import('./resources/js/components/OldInfos.vue')['default']
    OrderCard: typeof import('./resources/js/components/OrderCard.vue')['default']
    ProfessionalCard: typeof import('./resources/js/components/professional/Card.vue')['default']
    QuestionModal: typeof import('./resources/js/components/QuestionModal.vue')['default']
    RegisterForm: typeof import('./resources/js/components/register/Form.vue')['default']
    ServiceCard: typeof import('./resources/js/components/service/Card.vue')['default']
    SideBar: typeof import('./resources/js/components/SideBar.vue')['default']
    Skeleton: typeof import('./resources/js/components/Skeleton.vue')['default']
    TheDrawer: typeof import('./resources/js/components/TheDrawer.vue')['default']
    TheHeader: typeof import('./resources/js/components/TheHeader.vue')['default']
  }
}
