{"_id": "b744ab54-d685-4b75-8175-e76c69ee0e1c", "colName": "Cha<PERSON>", "created": "2023-07-17T16:20:31.578Z", "sortNum": 10000, "folders": [], "requests": [{"_id": "f195f069-689c-441f-80d4-7fb53b724a6b", "colId": "b744ab54-d685-4b75-8175-e76c69ee0e1c", "containerId": "", "name": "localhost:3333/register", "url": "localhost:3333/register", "method": "POST", "sortNum": 10000, "created": "2023-07-17T16:20:31.596Z", "modified": "2023-07-17T16:20:31.597Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"name\": \"teste3\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123\"\n}", "form": []}, "tests": []}]}