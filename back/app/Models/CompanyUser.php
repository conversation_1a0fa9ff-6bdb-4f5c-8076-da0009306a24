<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyUser extends Model
{
    use HasFactory;

    protected $table = 'company_users';
    protected $hidden = [
        'company_id',
    ];
    protected $fillable = [
        'user_id',
        'company_id',
        'total_spent',
        'total_scheduled',
        'form_finished'
    ];
    protected $casts = [
        'total_spent' => 'decimal:2',
        'total_scheduled' => 'integer',
        'form_finished' => 'boolean',
    ];
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function company()
    {
        return $this->belongs(Company::class, 'company_user');
    }
}
