<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div id="landing-page" class="flex bg-base-100 px-0 h-full min-w-full flex-col text-accent-content">
            <div class="relative">
                <div class="w-full h-[17svh] sm:h-[25vh] relative">
                    <div class="carousel w-full h-full">
                        <img :src="company.banner" alt="Banner" class="w-full h-full object-fill" />
                        <!-- <div v-for="(image, i) in bannerImages" :id="`slide-banner-${i}`" :key="i"
                        class="carousel-item relative w-full h-full">
                        <div v-if="bannerImages.length > 1"
                            class="absolute left-5 right-5 top-1/2 flex -translate-y-1/2 transform justify-between">
                            <a :href="`#slide-banner-${calcNavPrev(i)}`" class="btn btn-circle btn-xs">❮</a>
                            <a :href="`#slide-banner-${calcNavNext(i)}`" class="btn btn-circle btn-xs">❯</a>
                        </div>
                    </div> -->
                    </div>
                </div>
                <section class="px-5 bg-base-100 shadow-md rounded-lg">
                    <div
                        class="text-lg leading-7 pb-1 sm:text-xl pl-2 mt-1 ml-20 sm:ml-36 font-semibold text-accent-content drop-shadow-lg">
                        <div v-if="company.solo_professional">
                            {{ professional.user.name }}
                        </div>
                        <div v-else>
                            {{ company.name }}
                        </div>
                        <div class="flex mt-1">
                            <a :href="`https://wa.me/55${company.whatsapp}`" v-if="company.whatsapp" target="_blank">
                                <img src="/icons/whatsapp.svg" class="w-7" alt="" />
                            </a>
                            <a :href="`https://www.instagram.com/${instagram}`" v-if="company.instagram"
                                target="_blank">
                                <img src="/icons/instagram.svg" class="w-7 ml-2" alt="" />
                            </a>
                            <!-- <div class="text-primary ml-auto">+ Informações</div> -->
                        </div>
                    </div>
                </section>
                <div
                    class="absolute bottom-0 sm:left-8 z-10 left-1 w-24 h-24 sm:w-28 sm:h-28 border-4 border-white rounded-full overflow-hidden bg-base-100">
                    <img v-if="company.solo_professional" :src="`${professional.user.profile_photo_path}`" class="w-full h-full" />
                    <AppLogo v-else class="w-full h-full object-scale-down" size="192" />
                </div>
            </div>
            <div class="px-3">
                <div class="font-semibold text-lg my-2">
                    Especialidades
                </div>
                <div class="flex gap-2 flex-wrap">
                    <div v-for="(item, index) in professional.specialties?.split(',')" :key="index"
                        class="badge text-ellipsis badge-primary gap-1 p-3">
                        {{ item }}
                    </div>
                </div>
                <div class="mt-5 font-semibold text-lg">Sobre</div>
                <div v-if="company.solo_professional">
                    {{ professional.about }}
                </div>
                <div v-else>
                    {{ company.about }}
                </div>
                <div class="mt-3 font-semibold text-lg flex justify-between items-center">
                    <span>Serviços</span>
                    <base-button color="primary" size="sm" @click="navigateToServices" class="text-xs">Ver todos</base-button>
                </div>
                <div class="w-full mt-5 pb-10 relative">
                    <div class="carousel w-full overflow-x-auto">
                        <div class="carousel-item w-full grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 justify-start gap-2">
                            <div v-for="(service, i) in topServices" :key="i"
                                class="w-full min-w-[140px] flex-shrink-0 text-center p-3 border rounded-lg shadow-sm bg-base-100">
                                <div class="text-lg font-semibold truncate">
                                    {{ service?.name }}
                                </div>
                                <div>
                                    {{ formatPrice(service?.price) }}
                                </div>
                                <div>
                                    Duração: {{ handleTime(service?.duration) }}
                                </div>
                                <base-button color="primary" @click="handleSchedule(service)" size="sm"
                                    class="mt-2 w-full">Agendar</base-button>
                            </div>
                            <!-- Add empty placeholder divs to ensure proper spacing when few items -->
                            <div v-if="topServices.length === 1" class="w-1/2 md:w-1/3 lg:w-1/4 min-w-[150px] flex-shrink-0 opacity-0"></div>
                        </div>
                    </div>
                    <div class="absolute flex justify-between transform -translate-y-1/2 -left-3 md:-left-2 md:-right-2 -right-3 top-20" v-if="topServices.length > 1">
                        <button @click="scrollCarousel('left')" class="btn btn-primary btn-circle btn-xs">❮</button>
                        <button @click="scrollCarousel('right')" class="btn btn-primary btn-circle btn-xs">❯</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- <BaseDialog v-model="selectVariant" class="text-black">
            <div v-for="(product, i) in selectedService?.products" :key="i">
                <div class="text-lg mb-2">{{ product.name }}</div>
                <div
                    class="flex items-center gap-1 mb-1"
                    v-for="(variant, k) in product.variants"
                    :key="k"
                >
                    <input
                        type="radio"
                        class="radio radio-primary radio-sm"
                        :name="`opt${i}`"
                        :value="variant.id"
                        v-model="selectedVariant"
                    />
                    <label class="" :for="variant.name">{{
                        variant.name
                    }}</label
                    ><br />
                </div>
            </div>
            <div class="flex mt-4">
                <div class="btn ml-auto btn-primary" @click="handleVariant">
                    Confirmar
                </div>
            </div>
        </BaseDialog> -->
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from "@/Layouts/AppLayout.vue";
import { handleTime } from "@/utils/date";
//import { router } from "@inertiajs/vue3";
import { api } from "@/server/api";
import formatPrice from "@/utils/formatPrice";
import { useSchedule } from "@/store/schedule";
import { router } from "@inertiajs/vue3";

const { filterProductsByGame, addService, addVariant } = useSchedule();

onMounted(async () => {
    const { data } = await api(`/${slug}/most-scheduled`);
    topServices.value = data;
});
const topServices = ref([]);
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
    professional: {
        type: Object,
    },
});
const selectVariant = ref();
const instagram = computed(() => {
    const ig = company.instagram
    if(ig[0] === '@') return ig.slice(1);
    return ig;
});
const showFullText = ref(false);
const maxAboutLength = 250;
const selectedVariant = ref();
const selectedService = ref();
const loading = ref(true);
const toggleText = () => {
    showFullText.value = !showFullText.value;
};
function handleVariant(e) {
    addService(selectedService.value);
    addVariant(selectedVariant.value);
    if (company.solo_professional) router.get(`/${slug}/horarios`);
    else router.get(`/${slug}/profissionais`);
}
function handleSchedule(service: Service) {
    selectedService.value = service;
    addService(service);
    if (company.solo_professional) router.get(`/${slug}/horarios`);
    else router.get(`/${slug}/profissionais`);
}

function navigateToServices() {
    router.get(`/${slug}/servicos`);
}

function scrollCarousel(direction: 'left' | 'right') {
    const carousel = document.querySelector('.carousel');
    if (!carousel) return;

    const scrollAmount = direction === 'left'
        ? -carousel.clientWidth / 2
        : carousel.clientWidth / 2;

    carousel.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
    });
}
const truncatedAbout = computed(() => {
    if (!company.value.about) {
        return "";
    }
    if (company.value.about.length > maxAboutLength) {
        return showFullText.value
            ? company.value.about
            : company.value.about.slice(0, maxAboutLength) + "...";
    }
    return company.value.about;
});

const bannerImages = computed(() => {
    const url = [
        "https://marketplace.canva.com/EAF_ZFGfAwE/1/0/1600w/canva-banner-para-twitch-montanha-vintage-retr%C3%B4-roxo-nqw7QjAVpKo.jpg",
    ];
    return url;
});

const calcNavPrev = (index: number) => {
    if (index === 0) {
        return `${bannerImages.value.length}`;
    }
    return `${index - 1}`;
};

const calcNavNext = (index: number) => {
    if (index === bannerImages.value.length) {
        return `0`;
    }
    return `${index + 1}`;
};

const businessHours = computed(() => {
    const dias = {
        sunday_time: "DOM",
        monday_time: "SEG",
        tuesday_time: "TER",
        wednesday_time: "QUA",
        thursday_time: "QUI",
        friday_time: "SEX",
        saturday_time: "SÁB",
    };

    const resultado = Object.entries(dias).map(([chave, dia]) => {
        const horario = company[chave];
        return {
            day: dia,
            hour: horario ? horario.replace("-", " às ") : "Fechado",
        };
    });

    return resultado;
});

onMounted(() => {
    console.log(company.logo);
});

const activeTab = ref<"contact" | "about" | "social">("contact");
</script>
