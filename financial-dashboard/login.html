<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Financial Analytics</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-form {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .login-form h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .login-btn:hover {
            background: #2980b9;
        }
        
        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        
        .loading {
            text-align: center;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <form class="login-form" id="login-form">
            <h1>Financial Analytics</h1>
            
            <div class="error-message" id="login-error"></div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn" id="login-btn">Login</button>
            
            <div class="loading" id="login-loading">
                <div class="spinner"></div>
                <p>Logging in...</p>
            </div>
        </form>
    </div>

    <script src="js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Redirect if already authenticated
            if (auth.isAuthenticated()) {
                window.location.href = 'index.html';
                return;
            }

            const loginForm = document.getElementById('login-form');
            const loginBtn = document.getElementById('login-btn');
            const loginLoading = document.getElementById('login-loading');
            const loginError = document.getElementById('login-error');

            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;

                // Show loading state
                loginBtn.disabled = true;
                loginLoading.style.display = 'block';
                loginError.style.display = 'none';

                try {
                    const result = await auth.login(email, password);
                    
                    if (result.success) {
                        window.location.href = 'index.html';
                    } else {
                        loginError.textContent = result.error;
                        loginError.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    loginError.textContent = 'An unexpected error occurred. Please try again.';
                    loginError.style.display = 'block';
                } finally {
                    loginBtn.disabled = false;
                    loginLoading.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
