import{_ as A}from"./BaseButton-CxEL1ccK.js";import{_ as C,a as j}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{c as B,_ as N,a as g}from"./BaseLayout--Fu-QUOh.js";import{o as _,c as P,a as o,l as U,q as $,r as a,h as E,j as S,w as h,b as r,d as w,u as m,N as x,B as T,z as y}from"./app-BkKCG4YJ.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";function z(t,d){return _(),P("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[o("path",{"fill-rule":"evenodd",d:"M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm10.72 4.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72H9a.75.75 0 0 1 0-1.5h10.94l-1.72-1.72a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])}const D={class:"p-2 md:p-12 h-full flex flex-col justify-center px-3"},M={class:"bg-base-200 overflow-y-auto rounded-lg w-full mx-auto text-white shadow-xl overflow-hidden flex flex-col sm:flex-row justify-center items-center md:max-w-96"},O={class:"w-full h-full flex flex-col gap-2 px-5 pt-5 flex-1"},R={for:"file",class:"cursor-pointer"},Z=["src"],q={class:"w-full pb-2"},K=U({__name:"perfil",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(t){const d=$.useToast(),n=a(!1),f=a(""),c=a(""),p=a(""),v=a("https://upload.wikimedia.org/wikipedia/commons/a/ad/Placeholder_no_text.svg");a(),a(""),a(""),a("");async function b(){n.value=!0;const{data:l}=await g.get("/profile");f.value=l.name,c.value=l.email,p.value=l.phone,l.profile_photo_path&&(v.value="/"+l.profile_photo_path),n.value=!1}async function V(){var l;n.value=!0;try{await g.put("/profile",{name:f.value,email:c.value,phone:p.value}),n.value=!1,d.success("Perfil Atualizado",{position:"top"})}catch(e){y.isAxiosError(e)&&d.error((l=e.response)==null?void 0:l.data.message,{position:"top"})}finally{n.value=!1}}async function k(l){var e;try{const s=l.target.files[0],i=new FileReader;i.readAsDataURL(s),i.onload=async()=>{v.value=i.result,await g.post("/update-photo",{image:i.result})}}catch(s){y.isAxiosError(s)&&d.error((e=s.response)==null?void 0:e.data.message,{position:"top"})}finally{n.value=!1}}return E(()=>{b()}),(l,e)=>{const s=j,i=A;return _(),S(T(t.slug?C:N),{slug:t.slug,company:t.company,professional:t.professional},{default:h(()=>[o("div",D,[o("div",M,[o("div",O,[o("div",null,[o("label",R,[o("img",{class:"w-24 h-24 object-cover rounded-full mx-auto",src:v.value,alt:"Profile image"},null,8,Z),o("input",{id:"file",type:"file",class:"hidden",onChange:k},null,32)])]),r(s,{class:"w-full",label:"Nome",placeholder:"name",type:"text",modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=u=>f.value=u)},null,8,["modelValue"]),r(s,{class:"w-full",label:"email",placeholder:"email",type:"email",modelValue:c.value,"onUpdate:modelValue":e[1]||(e[1]=u=>c.value=u)},null,8,["modelValue"]),r(s,{class:"w-full",label:"Telefone",placeholder:"phone",type:"phone","data-maska":"(##) #####-####",modelValue:p.value,"onUpdate:modelValue":e[2]||(e[2]=u=>p.value=u)},null,8,["modelValue"]),r(i,{loading:n.value,onClick:V,class:"mt-2 w-full btn btn-primary"},{default:h(()=>e[4]||(e[4]=[w(" Salvar Alterações ")])),_:1},8,["loading"]),o("div",q,[r(i,{onClick:e[3]||(e[3]=()=>{m(B)().logout(t.slug),t.slug?m(x).get(`/${t.slug}/login`):m(x).get("/login")}),class:"text-base-content w-full btn-ghost"},{default:h(()=>[e[5]||(e[5]=w(" Sair da conta ")),r(m(z),{class:"w-6 ml-1"})]),_:1})])])])])]),_:1},8,["slug","company","professional"])}}});export{K as default};
