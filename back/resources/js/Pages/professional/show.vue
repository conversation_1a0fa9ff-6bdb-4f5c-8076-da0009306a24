<template>
    <div class="min-h-screen bg-base-100">
        <header class="bg-base-100 shadow-md">
            <div class="container mx-auto px-4 py-4 flex justify-between items-center">
                <div class="text-2xl font-bold text-primary">Psy +</div>
                <nav>
                    <a href="/" class="btn btn-outline btn-sm mr-2">Voltar</a>
                    <a href="#" class="btn btn-primary btn-sm">Para Profissionais</a>
                </nav>
            </div>
        </header>

        <div class="container mx-auto px-4 py-8">
            <div class="bg-base-100 shadow-lg rounded-lg overflow-hidden">
                <!-- Cabeçalho do perfil -->
                <div class="bg-primary/10 p-6">
                    <div class="flex flex-col md:flex-row items-center md:items-start">
                        <div class="avatar mb-4 md:mb-0 md:mr-6">
                            <div class="w-32 h-32 rounded-full">
                                <img :src="professional.user.profile_photo_url || '/placeholder-avatar.jpg'" alt="Avatar" />
                            </div>
                        </div>
                        <div class="text-center md:text-left">
                            <h1 class="text-2xl font-bold">{{ professional.user.name }}</h1>
                            <p class="text-gray-600 mt-1">Psicólogo(a)</p>
                            
                            <div class="mt-4 flex flex-wrap gap-2 justify-center md:justify-start">
                                <button class="btn btn-primary">Agendar Consulta</button>
                                <a :href="`https://wa.me/55${professional.user.phone}`" target="_blank" class="btn btn-outline">
                                    Contato
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Conteúdo do perfil -->
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Coluna da esquerda -->
                        <div class="md:col-span-2 space-y-6">
                            <div>
                                <h2 class="text-xl font-bold mb-3">Sobre</h2>
                                <p>{{ professional.about || 'Nenhuma informação disponível.' }}</p>
                            </div>
                            
                            <div>
                                <h2 class="text-xl font-bold mb-3">Especialidades</h2>
                                <div class="flex flex-wrap gap-2">
                                    <span v-for="(specialty, index) in specialtiesList" :key="index" 
                                        class="badge badge-primary p-3">
                                        {{ specialty }}
                                    </span>
                                </div>
                            </div>
                            
                            <div>
                                <h2 class="text-xl font-bold mb-3">Público Atendido</h2>
                                <div class="flex flex-wrap gap-2">
                                    <span v-for="(type, index) in publicList" :key="index" 
                                        class="badge badge-outline p-3">
                                        {{ type }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Coluna da direita -->
                        <div class="space-y-6">
                            <div class="bg-base-200 p-4 rounded-lg">
                                <h2 class="text-lg font-bold mb-3">Informações de Contato</h2>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <span class="font-semibold mr-2">Email:</span>
                                        <span>{{ professional.user.email }}</span>
                                    </li>
                                    <li class="flex items-center">
                                        <span class="font-semibold mr-2">Telefone:</span>
                                        <span>{{ professional.user.phone }}</span>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="bg-base-200 p-4 rounded-lg">
                                <h2 class="text-lg font-bold mb-3">Serviços</h2>
                                <div class="space-y-3">
                                    <div v-for="(service, index) in services" :key="index" 
                                        class="p-3 bg-base-100 rounded-md shadow-sm">
                                        <div class="font-semibold">{{ service.name }}</div>
                                        <div class="text-sm">{{ formatPrice(service.price) }}</div>
                                        <div class="text-xs">Duração: {{ service.duration }} min</div>
                                    </div>
                                    <div v-if="services.length === 0" class="text-center py-2">
                                        Nenhum serviço disponível.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <footer class="bg-base-200 py-8 mt-12">
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-3 gap-8 text-center md:text-left">
                    <div>
                        <h4 class="font-bold text-lg mb-4">Psy +</h4>
                        <p class="text-sm">Conectando pessoas a profissionais de saúde mental</p>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg mb-4">Links Úteis</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary">Sobre Nós</a></li>
                            <li><a href="#" class="hover:text-primary">Contato</a></li>
                            <li><a href="#" class="hover:text-primary">Blog</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg mb-4">Contato</h4>
                        <p class="text-sm">contato@Psy +.com</p>
                    </div>
                </div>
                <div class="text-center mt-8 text-sm">
                    <p>&copy; 2025 Psy +. Todos os direitos reservados.</p>
                </div>
            </div>
        </footer>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'

const props = defineProps({
    professional: {
        type: Object,
        required: true
    }
})

const services = ref([])

// Computed properties
const specialtiesList = computed(() => {
    if (!props.professional.specialties) return []
    return props.professional.specialties.split(',')
})

const publicList = computed(() => {
    if (!props.professional.public) return []
    return props.professional.public.split(',')
})

// Métodos
function formatPrice(price) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(price)
}

// Lifecycle hooks
onMounted(async () => {
    try {
        // Buscar serviços do profissional
        // Adapte conforme necessário para sua API
        const response = await axios.get(`/api/professional/${props.professional.id}/services`)
        services.value = response.data
    } catch (error) {
        console.error('Erro ao buscar serviços:', error)
    }
})
</script>
