<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('plan_id', 255)->nullable()->index()->constrained()->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('name')->nullable();
            $table->string('logo')->nullable();
            $table->string('banner')->default('/banners/default-image.webp');
            $table->string('monday_time')->nullable();
            $table->string('monday_pause')->nullable();
            $table->string('tuesday_time')->nullable();
            $table->string('tuesday_pause')->nullable();
            $table->string('wednesday_time')->nullable();
            $table->string('wednesday_pause')->nullable();
            $table->string('thursday_time')->nullable();
            $table->string('thursday_pause')->nullable();
            $table->string('friday_time')->nullable();
            $table->string('friday_pause')->nullable();
            $table->string('saturday_time')->nullable();
            $table->string('saturday_pause')->nullable();
            $table->string('sunday_time')->nullable();
            $table->string('sunday_pause')->nullable();
            $table->string('buttons_color')->default('#4B87A4');
            $table->string('background_color')->default('#FFFFFF');
            $table->string('cards_color')->default('#F2F2F2');
            $table->text('about')->nullable();
            $table->boolean('solo_professional')->default(false);
            $table->boolean('tryal_phase');
            $table->string('slug');
            $table->string('instagram')->nullable();
            $table->string('whatsapp')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
