<template>
    <form class="flex flex-col gap-3" @submit="submit">
        <div class="flex flex-col sm:grid sm:grid-cols-2 gap-3 w-full">
            <input-base class="col-span-2" v-model="name" :error="errors['name']" label="Nome" />
            <input-base v-model="email" :error="errors['email']" label="Email" class="" />
            <input-base v-model="phone" :error="errors['phone']" label="Telefone" data-maska="## #####-####" class="" />
            <div class="flex w-full col-span-2 gap-3">
                <input-base :error="errors['password']" v-model="password" label="Senha" type="password" />
                <input-base :error="errors['password_confirmation']" v-model="password_confirmation" label="Confirmar senha"
                    type="password" />
            </div>
        </div>
        <div class="text-right text-primary">
            <Link :href="`login`">
            <span
                class="font-black my-1 inline-block hover:text-primary hover:underline hover:cursor-pointer transition duration-200">
                Já possui registro? Entrar
            </span>
            </Link>
        </div>
        <base-button :loading type="submit" class="mt-2 w-full"> Registrar </base-button>
    </form>
</template>
<script setup lang="ts">
import { object, string, ref } from "yup";
import { useForm } from "vee-validate";
import { Link } from "@inertiajs/vue3";
import { useCompanyStore } from "@/store/company";

const companyStore = useCompanyStore();
const emit = defineEmits(["submit"]);
const validationSchema = object().shape({
    name: string().required("O nome é obrigatório"),
    email: string().email("Email inválido"),
    phone: string().required("Número de telefone é obrigatório"),
    password: string()
        .required("A senha do produto é obrigatória")
        .min(6, "A senha deve ter no mínimo 6 caracteres"),
    password_confirmation: string().oneOf([ref("password")], "Passwords must match"),
});
const { handleSubmit, defineField, errors } = useForm({
    validationSchema,
});
const { slug, loading } = defineProps({
    slug: {
        type: String,
    },
    loading: {
        type: Boolean,
    },
});
const [email] = defineField("email");
const [password] = defineField("password");
const [password_confirmation] = defineField("password_confirmation");
const [name] = defineField("name");
const [phone] = defineField("phone");

// eslint-disable-next-line
function handleFormSubmit(values: any) {
    emit("submit", values);
}
const submit = handleSubmit(handleFormSubmit);
</script>
