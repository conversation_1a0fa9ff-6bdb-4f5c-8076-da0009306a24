<template>
  <div>
    <ul class="menu w-56">
      <li class="bg-base-200 flex-row text-lg font-bold justify-between py-2 px-4 border-t cursor-pointer border-t-slate-50/40 hover:bg-gray-900 transition-colors">
        <Link href="/" class="flex-1">Início</Link>
        <ChevronRightIcon class="w-5 text-white text-2xl p-0" />
      </li>
      <li class="bg-base-200 flex-row text-lg font-bold justify-between py-2 px-4 border-t cursor-pointer border-t-slate-50/40 hover:bg-gray-900 transition-colors">
        <Link href="/profissionais" class="flex-1">Buscar profissionais</Link>
        <ChevronRightIcon class="w-5 text-white text-2xl p-0" />
      </li>
      <li class="bg-base-200 flex-row text-lg font-bold justify-between py-2 px-4 border-t cursor-pointer border-t-slate-50/40 hover:bg-gray-900 transition-colors">
        <Link href="/perfil" class="flex-1">Perfil</Link>
        <ChevronRightIcon class="w-5 text-white text-2xl p-0" />
      </li>
      <li class="bg-base-200 flex-row text-lg font-bold justify-between py-2 px-4 border-t cursor-pointer border-t-slate-50/40 hover:bg-gray-900 transition-colors">
        <Link href="/pedidos" class="flex-1">Pedidos</Link>
        <ChevronRightIcon class="w-5 text-white text-2xl p-0" />
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { ChevronRightIcon } from "@heroicons/vue/24/solid";
import { Link } from '@inertiajs/vue3';
</script>
