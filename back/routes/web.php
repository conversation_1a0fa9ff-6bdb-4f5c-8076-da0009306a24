<?php

use App\Models\Company;
use App\Models\CompanyProfessional;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Home');
})->name('home');


Route::get('/perfil', function () {
    return Inertia::render('company/perfil');
})->name('profile');

Route::get('/profissionais', function () {
    return Inertia::render('Profissionais');
})->name('professionals');

Route::get('/pedidos', function () {
    return Inertia::render(
        'company/orders',
    );
})->name('pedidos');
Route::get('/login', function () {
    return Inertia::render(
        'company/login'
    );
})->name('login');
Route::get('/register', function () {
    return Inertia::render(
        'company/register',
    );
})->name('register');
Route::prefix('/{slug}')->group(function () {
    Route::get('/servicos', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }
        // return $company;
        return Inertia::render(
            'company/servicos',
            ['slug' => $slug, 'company' => $company, 'professional' => $professional]
        );
    })->name('servicos');

    Route::get('/cart', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }
        // $companyProfessionals = CompanyProfessional::where('company_id', $company->id)->count();
        // $company->solo_professional = $companyProfessionals === 1;
        return Inertia::render(
            'company/cart',
            ['slug' => $slug, 'company' => $company, 'professional' => $professional]
        );
    })->name('cart');
    Route::get('/horarios', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }
        // $companyProfessionals = CompanyProfessional::where('company_id', $company->id)->count();
        // $company->solo_professional = $companyProfessionals === 1;
        return Inertia::render(
            'company/horarios',
            ['slug' => $slug, 'company' => $company, 'professional' => $professional]
        );
    })->name('horarios');
    Route::get('/', function (string $slug) {
        try {
            $company = Company::where('slug', $slug)->first();
            $professional = [];
            if ($company->solo_professional) {
                $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
            }
            // return $company;
            return Inertia::render(
                'company/index',
                ['slug' => $slug, 'company' => $company, 'professional' => $professional]
            );
        } catch (\Exception) {
            return redirect()->to('/');
        }
    })->name('index');
    Route::get('/login', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        return Inertia::render(
            'company/login',
            ['slug' => $slug, 'company' => $company]
        );
    })->name('login');
    Route::get('/register', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        return Inertia::render(
            'company/register',
            ['slug' => $slug, 'company' => $company]
        );
    })->name('register');
    Route::get('/perfil', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }
        return Inertia::render(
            'company/perfil',
            ['slug' => $slug, 'company' => $company, 'professional' => $professional]
        );
    })->name('perfil');
    Route::get('/pedidos', function ($slug) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }
        return Inertia::render(
            'company/orders',
            [
                'slug' => $slug,
                'company' => $company,
                'professional' => $professional
            ]
        );
    })->name('pedidos');
    Route::get('/checkout', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }
        return Inertia::render(
            'company/checkout',
            ['slug' => $slug, 'company' => $company, 'professional' => $professional]
        );
    })->name('checkout');

    Route::get('/finished-form', function (string $slug) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }
        return Inertia::render(
            'company/index',
            ['slug' => $slug, 'company' => $company, 'professional' => $professional]
        );
    })->name('finished-form');

    Route::get('/finish-register', function (string $slug, \Illuminate\Http\Request $request) {
        $company = Company::where('slug', $slug)->first();
        $professional = [];
        if ($company->solo_professional) {
            $professional = CompanyProfessional::where('company_id', $company->id)->with('user')->first();
        }

        Log::info('Finish register route accessed with data: ' . ($request->query('data') ? substr($request->query('data'), 0, 50) . '...' : 'null'));

        return Inertia::render(
            'company/finish-register',
            [
                'slug' => $slug,
                'company' => $company,
                'professional' => $professional,
                'encryptedData' => $request->query('data')
            ]
        );
    })->name('finish-register');
});
