<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div>
            <div @click="
                router.get(
                    `/${slug}/${hasProfessional ? 'profissionais' : 'servicos'}`,
                )
                "
                class="ml-2 mb-1 cursor-pointer font-bold gap-1 text-lg text-accent-content flex items-center mt-3 md:mt-5">
                <ArrowLeftIcon class="w-5 h-5" />
                <span>Alterar
                    {{ hasProfessional ? "profissional" : "serviço" }}</span>
            </div>
            <div class="px-3 pb-3">
                <div class="text-lg font-bold text-accent-content">
                    Escolha o dia
                </div>
                <input-base v-model="date" type="date" class="my-2" input-classes="border-black focus:border-black" />

                <div class="text-lg font-bold mb-3 text-accent-content">
                    Hor<PERSON><PERSON><PERSON> disponíveis
                </div>
                <div v-if="!companyClosed" class="grid grid-cols-3 sm:grid-cols-5 md:grid-cols-7 gap-2">
                    <Skeleton :number="21" classes="h-12" v-if="loading" />
                    <template v-else>
                        <base-button @click="goToCheckout(horario.hour)" color="primary" :disabled="horario.locked"
                            v-for="(horario, i) in horarios" :key="i">
                            {{ horario.hour }}
                        </base-button>
                    </template>
                </div>
                <div v-if="!loading && (horarios.length === 0 || companyClosed)" class="text-center mt-4 text-lg font-bold text-accent-content">
                    Não há horários disponíveis para este dia.
                </div>
            </div>
        </div>
    </AppLayout>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import { api } from "@/server/api";
import { useCompanyStore } from "@/store/company";
import { useSchedule } from "@/store/schedule";
import { router } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { ArrowLeftIcon } from "@heroicons/vue/24/solid";

type Slot = {
    hour: string;
    locked: boolean;
};
const companyClosed  = ref(false)
const companyStore = useCompanyStore();
const { addHour, cart } = useSchedule();
const horarios = ref<{ hour: string; locked: boolean }[]>([]);
const loading = ref(false)
const date = ref(dayjs().format("YYYY-MM-DD"));
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
    professional: {
        type: Object,
    },
});
function goToCheckout(horario: string) {
    addHour({ hour: horario, date: date.value });
    router.get(`/${slug}/cart`);
}

function isConflicting(hour: string, duration: number): boolean {
    const start = dayjs(`${date.value}T${hour}`);
    const end = start.add(duration, "minute");

    return cart.some((item) => {
        const itemStart = dayjs(`${item.date}T${item.hour}`);
        const itemEnd = itemStart.add(item.service.duration, "minute");

        return start.isBefore(itemEnd) && end.isAfter(itemStart);
    });
}

async function getSlots() {
    loading.value = true
    companyClosed.value = false
    try {
        const schedule = useSchedule();
        const { data } = await api.get(`${company.slug}/slots`, {
            params: {
                professionalId: schedule.professional.id,
                serviceId: schedule.service.id,
                date: date.value,
            },
        });

        horarios.value = data.map((slot: Slot) => {
            const conflicting = isConflicting(slot.hour, schedule.service.duration);
            return {
                ...slot,
                locked: slot.locked || conflicting,
            };
        });
    }
    catch {
        companyClosed.value = true
    }
    finally {
        loading.value = false
    }
}

onMounted(async () => {
    getSlots();
});
const hasProfessional = computed(() => !!useSchedule().professional.id
)
watch(date, getSlots);
</script>
