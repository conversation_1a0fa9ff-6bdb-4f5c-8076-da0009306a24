<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div :class="loading ? 'h-full overflow-hidden' : ''">
            <!-- Add search bar -->
            <div class="px-4 pt-3">
                <input-base type="text" v-model="searchQuery" placeholder="Buscar serviços..."
                    label-style="!-mb-2 ml-2 z-10 text-xs !text-accent-content font-bold" class="sm:min-w-52"
                    label-bg="!bg-base-100" label="Filtrar serviços" inputClasses="!border-black" />
            </div>

            <div class="flex flex-wrap sm:gap-10 gap-1 justify-center sm:mt-10 p-1 mt-3 overflow-auto mx-1">
                <Skeleton :number="10" classes="h-24 w-full sm:w-52 sm:h-72" v-if="loading" />
                <template v-else>
                    <ServiceCard v-for="service in filteredServices" :key="service.id" :item="service" type="services"
                        @schedule="handleSchedule(service as Service)" />
                </template>
            </div>
        </div>
        <!-- <BaseDialog v-model="selectVariant" class="text-black">
            <div v-for="(product, i) in selectedService?.products" :key="i">
                <div class="text-lg mb-2">{{ product.name }}</div>
                <div
                    class="flex items-center gap-1 mb-1"
                    v-for="(variant, k) in product.variants"
                    :key="k"
                >
                    <input
                        type="radio"
                        class="radio radio-primary radio-sm"
                        :name="`opt${i}`"
                        :value="variant.id"
                        v-model="selectedVariant"
                    />
                    <label class="" :for="variant.name">{{
                        variant.name
                    }}</label
                    ><br />
                </div>
            </div>
            <div class="flex mt-4">
                <div class="btn ml-auto btn-primary" @click="handleVariant">
                    Confirmar
                </div>
            </div>
        </BaseDialog> -->
    </AppLayout>
</template>

<script lang="ts" setup>
import { useCompanyStore } from "@/store/company";
import { useSchedule } from "@/store/schedule";
import { Service } from "@/utils/models";
import AppLayout from "@/Layouts/AppLayout.vue";
const { filterProductsByGame, addService, cartLength } = useSchedule();
import { router } from "@inertiajs/vue3";
import { ref, computed } from "vue";

const selectedVariant = ref();
const loading = ref(true);
const companyStore = useCompanyStore();
const selectedService = ref();
const data = ref<Service[]>();
const searchQuery = ref("");

// Add computed property for filtered services
const filteredServices = computed(() => {
    if (!data.value) return [];
    if (!searchQuery.value) return data.value;

    const query = searchQuery.value.toLowerCase();
    return data.value.filter((service) =>
        service.name.toLowerCase().includes(query),
    );
});

function handleSchedule(service: Service) {
    let hasVariant = false;
    selectedService.value = service;

    addService(service);
    if (company.solo_professional) router.get(`/${slug}/horarios`);
    else router.get(`/${slug}/profissionais`);

}

function handleVariant(e) {
    addService(selectedService.value);
    // addVariant(selectedVariant.value);
    if (company.solo_professional) router.get(`/${slug}/horarios`);
    else router.get(`/${slug}/profissionais`);
}

const { slug, company } = defineProps({
    slug: { type: String },
    company: { type: Object },
    professional: { type: Object },
});

const selectVariant = ref();

onMounted(async () => {
    if (cartLength) router.get(`/${slug}/cart`);
    else {
        try {
            loading.value = true;
            const response = await filterProductsByGame(`${slug}/services`);
            data.value = response.data;
            loading.value = false;
        } catch (error) {
            return;
        }
    }
});
</script>
