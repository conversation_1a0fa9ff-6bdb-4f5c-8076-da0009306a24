import{f as n,o as t,c as a,a as l,g as d,Q as i,n as r}from"./app-BkKCG4YJ.js";const c=["disabled"],u={class:"flex items-center justify-center"},m={key:0,class:"loading loading-spinner text-primary mr-2"},g={__name:"BaseButton",props:{color:{type:String,default:"primary"},size:{type:String,default:""},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},setup(e){n(()=>`btn-${s.size}`);const s=e;return(o,f)=>(t(),a("button",{class:r(["btn",`btn-${e.color} `]),disabled:!!e.loading||e.disabled},[l("div",u,[e.loading?(t(),a("div",m)):d("",!0),i(o.$slots,"default")])],10,c))}};export{g as _};
