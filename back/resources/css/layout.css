/* Layout-specific styles */
:root {
  --header-height: 56px;
  --bottom-nav-height: 64px;
}

.base-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.h-layout {
  flex: 1;
  overflow-y: auto;
  padding-bottom: var(--bottom-nav-height);
}

/* Ensure the bottom navigation has the correct height */
.btm-nav {
  height: var(--bottom-nav-height);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
}

/* Adjust content padding on mobile */
@media (max-width: 768px) {
  .h-layout {
    padding-bottom: calc(var(--bottom-nav-height) + 16px);
  }
}
