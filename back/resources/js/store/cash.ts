import { defineStore } from "pinia";
// import type { Cash } from "@/models/cash";
import { getServices } from "@/server/api";

const baseUrl = "/cash";

const { create, deleteById, getAll, editById } = getServices(baseUrl);

export const useCashStore = defineStore("cash", {
  state: () => ({
    cashs: [] as Cash[],
    loading: false,
  }),
  actions: {
    async add(cash: Cash) {
      this.loading = true;
      try {
        await create(cash);
      } catch (err) {
        throw err;
      } finally {
        this.loading = false;
      }
    },
    async delete(cashId: string) {
      this.loading = true;
      try {
        await deleteById(Number(cashId));
      } catch (err) {
        throw err;
      } finally {
        this.loading = false;
      }
    },
    async edit(cash: Cash) {
      this.loading = true;
      try {
        await editById(Number(cash.id));
      } catch (err) {
        throw err;
      } finally {
        this.loading = false;
      }
    },
    async getAll() {
      this.loading = true;
      try {
        return await getAll();
      } catch (err) {
        throw err;
      } finally {
        this.loading = false;
      }
    },
  },
  persist: true,
});
