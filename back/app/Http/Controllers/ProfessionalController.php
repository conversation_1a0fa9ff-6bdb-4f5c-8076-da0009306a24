<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\CompanyProfessional;
// use App\Models\Professional;
use App\Models\User;
use Illuminate\Http\Request;

class ProfessionalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $companyID = $request->get('company_id');
        $filterName = $request->query('name');
        $sortDirection = $request->query('direction', 'asc'); // Crescente por padrão
        $perPage = $request->query('per_page', 10); // Valor padrão de 10
        $page = $request->query('page', 1); // Valor padrão de 1

        $query = CompanyProfessional::where('company_id', $companyID)->where('status', 'active')->with('user');

        if ($filterName) {
            $query->whereHas('user', function ($q) use ($filterName) {
                $q->where('name', 'like', '%' . $filterName . '%');
            });
        }

        $query->join('users', 'company_professionals.user_id', '=', 'users.id')
            ->orderBy('users.name', $sortDirection);

        $professionals = $query->paginate($perPage, ['company_professionals.*'], 'page', $page);

        $professionals->getCollection()->transform(function ($item) {
            return [
                'id' => $item->id,
                'name' =>  $item->user->name,
                'phone' => $item->user->phone,
                'email' => $item->user->email,
                'image' => $item->user->profile_photo_url,
                'role' => $item->role,
                'status' => $item->status,
                'commission' => $item->commission,
            ];
        });

        return response()->json($professionals, 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function getProfessionals(String $slug){
        $company = Company::where('slug', $slug)->first();
        $professionals = CompanyProfessional::with('user')->where('company_id', $company->id)->get();
        return response()->json($professionals,200);

    }
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'company_id' => 'required|exists:companies,id',
            'user_id' => 'required|exists:users,id',
            'role' => 'nullable|string|max:255',
            'commission' => 'required'
        ]);

        $companyProfessional = CompanyProfessional::create($validatedData);

        return response()->json([
            'message' => 'Company professional created successfully.',
            'data' => $companyProfessional,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $companyProfessional = CompanyProfessional::with(['company', 'user'])->find($id);

        if (!$companyProfessional) {
            return response()->json(['message' => 'Company professional not found.'], 404);
        }

        return response()->json($companyProfessional, 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $companyProfessional = CompanyProfessional::find($id);

        if (!$companyProfessional) {
            return response()->json(['message' => 'Company professional not found.'], 404);
        }

        $validatedData = $request->validate([
            'company_id' => 'required|exists:companies,id',
            'user_id' => 'required|exists:users,id',
            'role' => 'nullable|string|max:255',
        ]);

        $companyProfessional->update($validatedData);

        return response()->json([
            'message' => 'Company professional updated successfully.',
            'data' => $companyProfessional,
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $companyProfessional = CompanyProfessional::find($id);

        if (!$companyProfessional) {
            return response()->json(['message' => 'Company professional not found.'], 404);
        }

        $companyProfessional->delete();

        return response()->json(['message' => 'Company professional deleted successfully.'], 200);
    }

    public function createUserAndProfessional(Request $request)
    {
        $newUser = [
            'name' => $request->get('name'),
            'phone' => $request->get('phone'),
            'email' => $request->get('email'),
            'password' => $request->get('password'),
            'type' => 'professional',
        ];
        $user = User::create($newUser);
        CompanyProfessional::create([
            'user_id' => $user->id,
            'company_id' => $request->get('company_id'),
            'role' => $request->get('role'),
            // 'status' => $request->get('status'),
            'commission' => $request->get('commission'),
        ]);
        return response()->json('Profissional criado com sucesso', 201);
    }
    public function vinculateExistingUserAsProfessional(Request $request)
    {
        CompanyProfessional::create([
            'user_id' => $request->get('user_id'),
            'company_id' => $request->get('company_id'),
            'role' => $request->get('role'),
            // 'status' => $request->get('status'),
            'commission' => $request->get('commission'),
        ]);
        return response()->json('Profissional vinculado com sucesso', 201);
    }
    public function updateProfessional(Request $request, $id)
    {
        $professional = CompanyProfessional::find($id);
        $professional->commission = $request->get('commission');
        $professional->save();
        // $professional->update($request->all());
        return response()->json('Profissional atualizado com sucesso', 200);
    }
    public function finishProfessionalBond($id)
    {
        $professional = CompanyProfessional::find($id);
        $professional->status = 'inactive';
        $professional->save();
        return response()->json('Profissional desvinculado com sucesso', 200);
    }

    /**
     * Get all professionals with their specialties and public types for the index page.
     */
    public function getAllProfessionals(Request $request)
    {
        $filterName = $request->query('name');
        $filterSpecialties = $request->query('specialties'); // comma-separated list
        $filterPublic = $request->query('public'); // comma-separated list

        // Using the query scopes defined in the CompanyProfessional model
        $query = CompanyProfessional::active()
            ->with('user')
            ->filterByName($filterName)
            ->filterBySpecialties($filterSpecialties)
            ->filterByPublicTypes($filterPublic);

        $professionals = $query->get();

        return response()->json($professionals, 200);
    }

    /**
     * Public endpoint to get all psychologists with their specialties and public types.
     * This endpoint is specifically for the psychologist app and requires no authentication.
     */
    public function getPublicPsychologists(Request $request)
    {
        $filterName = $request->query('name');
        $filterSpecialties = $request->query('specialties'); // comma-separated list
        $filterPublic = $request->query('public'); // comma-separated list

        // Using the query scopes defined in the CompanyProfessional model
        $query = CompanyProfessional::with('user')->where('status', 'active')
            ->filterByName($filterName)
            ->filterBySpecialties($filterSpecialties)
            ->filterByPublicTypes($filterPublic);

        // Only include professionals with specialties or public types set
        // This ensures we only return psychologists
        // $query->where(function($q) {
        //     $q->whereNotNull('specialties')
        //       ->orWhereNotNull('public');
        // });

        $professionals = $query->get();

        return response()->json($professionals, 200);
    }

    /**
     * Public endpoint to get a single psychologist's details.
     * This endpoint is specifically for the psychologist app and requires no authentication.
     */
    public function getPublicPsychologistDetails($id)
    {
        $professional = CompanyProfessional::with('user')
            ->where('id', $id)
            ->where('status', 'active')
            ->where(function($q) {
                $q->whereNotNull('specialties')
                  ->orWhereNotNull('public');
            })
            ->first();

        if (!$professional) {
            return response()->json(['message' => 'Professional not found'], 404);
        }

        return response()->json($professional, 200);
    }
}
