<template>
  <div v-if="shouldShowNavigation" class="btm-nav flex" :key="navigationKey">
    <button
      v-for="({ icon, to, label }, i) in routes"
      :key="`${navigationKey}-${i}`"
      :class="to === route.path ? 'active-navigation' : 'bg-base-200'"
      class="relative px-1"
      @click="router.push(to)"
    >
      <component
        :class="to === route.path ? 'fill-primary-content' : ''"
        :is="icon"
        class="w-5 h-5"
      ></component>
      <span class="text-xs">{{ label }}</span>
    </button>
  </div>
</template>
<script setup lang="ts">
import { useNavigationBottom } from "@/store/navigationBottom";
import { useLoginStore } from "@/store/user";

const navigationBottom = useNavigationBottom();
const loginStore = useLoginStore();
const router = useRouter();
const route = useRoute();

// Reactive navigation key that updates when login state changes
const navigationKey = computed(() => {
  return `${loginStore.isLoggedIn}-${loginStore.isSuperAdmin}-${navigationBottom.refreshKey}`;
});

// Check if navigation should be shown
const shouldShowNavigation = computed(() => {
  return loginStore.isLoggedIn;
});

// Make routes reactive to login state changes
const routes = computed(() => {
  // Force reactivity by watching login state
  if (!loginStore.isLoggedIn) {
    return [];
  }

  return navigationBottom.routes;
});

// Watch for login state changes and force refresh
watch(() => loginStore.isLoggedIn, (newValue) => {
  if (newValue) {
    // User just logged in, force refresh
    nextTick(() => {
      navigationBottom.forceRefresh();
    });
  }
}, { immediate: true });

watch(() => loginStore.isSuperAdmin, () => {
  navigationBottom.forceRefresh();
});

// Ensure navigation shows immediately after component mount if user is logged in
onMounted(() => {
  if (loginStore.isLoggedIn) {
    navigationBottom.forceRefresh();
  }
});
</script>
<style lang="scss">
.active-navigation {
  @apply active bg-primary border-primary-content text-primary-content;
}
</style>
