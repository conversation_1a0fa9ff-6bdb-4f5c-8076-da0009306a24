<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubscriptionAnalytics extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'plan_id',
        'event_type',
        'amount',
        'currency',
        'payment_method',
        'payment_status',
        'billing_period_start',
        'billing_period_end',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'billing_period_start' => 'date',
        'billing_period_end' => 'date',
        'metadata' => 'array',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    // Scopes
    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    public function scopeByPaymentStatus($query, $status)
    {
        return $query->where('payment_status', $status);
    }

    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function scopeByPlan($query, $planId)
    {
        return $query->where('plan_id', $planId);
    }

    // Static methods for tracking subscription events
    public static function trackSubscriptionCreated($companyId, $planId, $amount, $paymentMethod = null, $billingStart = null, $billingEnd = null, $metadata = [])
    {
        return self::create([
            'company_id' => $companyId,
            'plan_id' => $planId,
            'event_type' => 'subscription_created',
            'amount' => $amount,
            'currency' => 'BRL',
            'payment_method' => $paymentMethod,
            'payment_status' => 'pending',
            'billing_period_start' => $billingStart ?? now(),
            'billing_period_end' => $billingEnd ?? now()->addMonth(),
            'metadata' => $metadata,
        ]);
    }

    public static function trackPaymentCompleted($companyId, $planId, $amount, $paymentMethod = null, $metadata = [])
    {
        return self::create([
            'company_id' => $companyId,
            'plan_id' => $planId,
            'event_type' => 'payment_completed',
            'amount' => $amount,
            'currency' => 'BRL',
            'payment_method' => $paymentMethod,
            'payment_status' => 'completed',
            'billing_period_start' => now(),
            'billing_period_end' => now()->addMonth(),
            'metadata' => $metadata,
        ]);
    }

    public static function trackSubscriptionUpgraded($companyId, $oldPlanId, $newPlanId, $amount, $metadata = [])
    {
        return self::create([
            'company_id' => $companyId,
            'plan_id' => $newPlanId,
            'event_type' => 'upgraded',
            'amount' => $amount,
            'currency' => 'BRL',
            'payment_status' => 'completed',
            'billing_period_start' => now(),
            'billing_period_end' => now()->addMonth(),
            'metadata' => array_merge($metadata, ['old_plan_id' => $oldPlanId]),
        ]);
    }

    public static function trackSubscriptionCancelled($companyId, $planId, $metadata = [])
    {
        return self::create([
            'company_id' => $companyId,
            'plan_id' => $planId,
            'event_type' => 'cancelled',
            'amount' => 0,
            'currency' => 'BRL',
            'payment_status' => 'cancelled',
            'billing_period_start' => now(),
            'billing_period_end' => now(),
            'metadata' => $metadata,
        ]);
    }
}
