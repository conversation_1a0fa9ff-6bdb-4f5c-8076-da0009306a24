<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    public function updatePhoto(Request $request)
    {
        $request->validate([
            'image' => 'required|string', // Base64 é uma string
        ]);

        $userId = Auth::id();
        $user = User::find($userId);

        $imageData = $request->input('image');
        $imageParts = explode(';base64,', $imageData);
        if (count($imageParts) !== 2) {
            return response()->json(['error' => 'Formato inválido de base64.'], 400);
        }

        $imageTypeAux = explode('image/', $imageParts[0]);
        $imageType = $imageTypeAux[1] ?? 'png';
        $imageBase64 = base64_decode($imageParts[1]);

        $sourceImage = imagecreatefromstring($imageBase64);
        if (!$sourceImage) {
            return response()->json(['error' => 'Não foi possível processar a imagem.'], 400);
        }

        $maxWidth = 300; // Largura máxima
        $maxHeight = 300; // Altura máxima
        list($origWidth, $origHeight) = getimagesizefromstring($imageBase64);

        $aspectRatio = $origWidth / $origHeight;
        if ($origWidth > $origHeight) {
            $newWidth = $maxWidth;
            $newHeight = $maxWidth / $aspectRatio;
        } else {
            $newHeight = $maxHeight;
            $newWidth = $maxHeight * $aspectRatio;
        }

        $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
        imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $origWidth, $origHeight);

        ob_start();
        if ($imageType === 'jpeg' || $imageType === 'jpg') {
            imagejpeg($resizedImage, null, 75); // Qualidade 75%
        } elseif ($imageType === 'png') {
            imagepng($resizedImage, null, 6); // Compressão PNG (0 a 9)
        } elseif ($imageType === 'gif') {
            imagegif($resizedImage);
        }
        $compressedImage = ob_get_clean();

        $filename = 'user_' . $user->id . '_' . time() . '.' . $imageType;
        $path = 'user_photos/' . $filename;

        Storage::disk('public')->put($path, $compressedImage);

        if ($user->profile_photo_path) {
            Storage::disk('public')->delete($user->profile_photo_path);
        }

        $user->profile_photo_path = "users/$filename";
        $user->save();

        imagedestroy($sourceImage);
        imagedestroy($resizedImage);

        return response()->json([
            'message' => 'Foto atualizada com sucesso!',
            // 'photo_url' => Storage::disk('public')->url($path),
            'photo_url' => Storage::url($path),
        ]);
    }
    public function getAllUsers(Request $request)
    {
        $users = User::all();
        return response()->json($users, 200);
    }
    public function searchUserByPhone(Request $request)
    {
        $phone = $request->input('phone');
        $user = User::where('phone', $phone)->first();
        return response()->json($user, 200);
    }
}
