<template>
  <input ref="inputRef" type="text" class="input input-bordered" />
</template>

<script setup lang="ts">
import { useCurrencyInput } from "vue-currency-input";
const props = defineProps(["modelValue", "options"]);
const defaltValues = {
  currency: "BRL",
  locale: "pt-BR",
  autoDecimalDigits: true,
  hideCurrencySymbolOnFocus: false,
  hideGroupingSeparatorOnFocus: false,
  valueRange: { min: 0 },
};
const options = { ...defaltValues, ...props?.options };
watch(
  () => props.modelValue,
  (value) => {
    setValue(value);
  }
);

const { inputRef, setValue } = useCurrencyInput(options);
</script>
