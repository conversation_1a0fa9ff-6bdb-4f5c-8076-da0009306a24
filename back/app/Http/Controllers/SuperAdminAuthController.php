<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\AccessLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class SuperAdminAuthController extends Controller
{
    /**
     * Login super admin
     */
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            
            // Check if user is super admin
            if ($user->type !== 'super_admin') {
                Auth::logout();
                return response()->json([
                    'error' => 'Access denied. Super admin privileges required.'
                ], 403);
            }

            // Create token with super admin abilities
            $token = $user->createToken('super_admin_token', ['super_admin'])->plainTextToken;

            // Log the access
            AccessLog::logLogin($user->id, 'super_admin');

            return response()->json([
                'token' => $token,
                'expires_in' => 30 * 24 * 60 * 60, // 30 days
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'type' => $user->type,
                    'profile_photo_url' => $user->profile_photo_url,
                ],
            ]);
        }

        return response()->json([
            'error' => 'Invalid credentials'
        ], 401);
    }

    /**
     * Logout super admin
     */
    public function logout(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        if ($user) {
            // Log the logout
            AccessLog::logLogout($user->id, 'super_admin');
            
            // Revoke current token
            $request->user()->currentAccessToken()->delete();
        }

        return response()->json([
            'message' => 'Successfully logged out'
        ]);
    }

    /**
     * Get authenticated super admin user
     */
    public function user(Request $request): JsonResponse
    {
        $user = $request->user();
        
        return response()->json([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'type' => $user->type,
            'profile_photo_url' => $user->profile_photo_url,
            'created_at' => $user->created_at,
        ]);
    }

    /**
     * Create a new super admin (only for existing super admins)
     */
    public function createSuperAdmin(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone ?? 'N/A',
            'password' => Hash::make($request->password),
            'type' => 'super_admin',
            'profile_photo_path' => "https://ui-avatars.com/api/?name=" . urlencode($request->name),
        ]);

        return response()->json([
            'message' => 'Super admin created successfully',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'type' => $user->type,
            ]
        ], 201);
    }

    /**
     * Update super admin profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $user = $request->user();

        $request->validate([
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|string|email|max:255|unique:users,email,' . $user->id,
            'current_password' => 'required_with:password|string',
            'password' => 'sometimes|string|min:8|confirmed',
        ]);

        // Verify current password if trying to change password
        if ($request->has('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                throw ValidationException::withMessages([
                    'current_password' => ['The current password is incorrect.'],
                ]);
            }
        }

        $updateData = [];
        
        if ($request->has('name')) {
            $updateData['name'] = $request->name;
        }
        
        if ($request->has('email')) {
            $updateData['email'] = $request->email;
        }
        
        if ($request->has('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'type' => $user->type,
                'profile_photo_url' => $user->profile_photo_url,
            ]
        ]);
    }

    /**
     * Get all super admins
     */
    public function getSuperAdmins(): JsonResponse
    {
        $superAdmins = User::where('type', 'super_admin')
            ->select('id', 'name', 'email', 'created_at', 'profile_photo_path')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'created_at' => $user->created_at,
                    'profile_photo_url' => $user->profile_photo_url,
                ];
            });

        return response()->json($superAdmins);
    }

    /**
     * Delete a super admin (cannot delete self)
     */
    public function deleteSuperAdmin(Request $request, User $user): JsonResponse
    {
        $currentUser = $request->user();

        // Cannot delete self
        if ($currentUser->id === $user->id) {
            return response()->json([
                'error' => 'Cannot delete your own account'
            ], 400);
        }

        // Verify the user is actually a super admin
        if ($user->type !== 'super_admin') {
            return response()->json([
                'error' => 'User is not a super admin'
            ], 400);
        }

        // Ensure at least one super admin remains
        $superAdminCount = User::where('type', 'super_admin')->count();
        if ($superAdminCount <= 1) {
            return response()->json([
                'error' => 'Cannot delete the last super admin'
            ], 400);
        }

        $user->delete();

        return response()->json([
            'message' => 'Super admin deleted successfully'
        ]);
    }
}
