<template>
    <div class="navbar bg-base-200 shadow-3xl">
        <!-- <ArrowLeftIcon class="w-5 cursor-pointer pb-[2px]" @click="router.back" /> -->
        <!-- <div class="font-semibold ml-2 text-lg pb-1">
      {{ headerTitle }}
    </div> -->
        <app-logo
            class="w-12 h-12 cursor-pointer"
            @click="router.get(`/${slug}`)"
        />
        <div class="flex-1 md:pl-0 py-1 ml-20">
            <template class="hidden md:block">
                <Link class="btn btn-ghost" :href="`/${slug}/servicos`">Serviços</Link>
                <Link class="btn btn-ghost" :href="`/${slug}/pedidos`"
                    >Meus agendamentos</Link
                >
                <Link class="btn btn-ghost" :href="`/${slug}/perfil`">Meus dados</Link>
                <!-- <Link class="btn btn-ghost" :href="`/${slug}/`">Serviços</Link>
                <Link class="btn btn-ghost" :href="`/${slug}/`">Serviços</Link> -->
            </template>
        </div>
        <button
            class="btn btn-ghost relative hidden md:block"
            @click="openDrawerCart"
        >
            <ShoppingCartIcon class="w-8" />
            <span
                v-if="cart.cart.length > 0"
                class="rounded-full bg-primary font-bold w-4 h-4 text-xs text-center absolute top-1 right-2"
                >{{ cart.cart.length }}</span
            >
        </button>
        <button
            class="btn btn-ghost relative md:hidden"
            @click="router.get(`/${slug}/cart`)"
        >
            <ShoppingCartIcon class="w-8" />
            <span
                class="rounded-full bg-primary text-primary-content font-bold w-4 h-4 text-xs text-center absolute top-1 right-2"
                >{{ cart.cart?.length || 0 }}</span
            >
        </button>

        <!-- <div class="mr-auto">
      <button
        @click="openDrawerSide"
        class="btn btn-square btn-ghost hidden sm:flex"
      >
        <bars3-icon class="w-7 h-7" />
      </button>
    </div> -->
    </div>
</template>
<script lang="ts" setup>
import { ArrowLeftIcon, ShoppingCartIcon } from "@heroicons/vue/24/solid";
import { useDrawer } from "@/store/drawer";
import { useSchedule } from "@/store/schedule";
import { Link, router } from "@inertiajs/vue3";

const cart = useSchedule();
const drawer = useDrawer();
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
});
// const headerTitle = computed(() => {
//     switch (route.name) {
//         case "slug-produtos":
//             return "Serviços";
//         case "slug-profissionais":
//             return "Profissionais";
//         case "slug-cart":
//             return "Carrinho";
//         case "slug-horarios":
//             return "Agende o dia";
//         case "slug-orders":
//             return "Agendamentos";
//         case "slug-checkout":
//             return "Finalizar pedido";
//         case "slug-login":
//             return "Entrar";
//         case "slug-perfil":
//             return "Meus dados";
//         default:
//             return (
//                 String(route.name).charAt(0).toUpperCase() +
//                 String(route.name).slice(1)
//             );
//     }
// });
function openDrawerCart() {
    if (!drawer.active) {
        cart.toggleActive();
    }
}
function openDrawerSide() {
    if (!cart.cartDrawerActive) {
        drawer.toggleActive();
    }
}
</script>
