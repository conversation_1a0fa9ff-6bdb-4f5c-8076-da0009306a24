// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: [
    "@nuxtjs/tailwindcss",
    "@nuxtjs/color-mode",
    "@nuxtjs/eslint-module",
    "nuxt-socket-io",
    "@pinia/nuxt",
    "@pinia-plugin-persistedstate/nuxt",
    "@averjs/nuxt-compression",
    "@nuxt/image",
    "@vite-pwa/nuxt",
  ],
  nitro: {
    compressPublicAssets: true,
  },
  plugins: ["~/plugins/pusher.ts"],
  colorMode: {
    preference: "light", // default theme
    dataValue: "theme", // activate data-theme in <html> tag
  },

  // eslint-disable-next-line
  // @ts-ignore
  runtimeConfig: {
    public: {
      API_URL: process.env.API_URL,
    },
  },

  typescript: {
    typeCheck: false,
  },

  tailwindcss: {
    cssPath: "~/assets/css/tailwind.scss",
    viewer: true,
  },

  devtools: {
    enabled: true,
  },

  pinia: {
    // autoImports: ["defineStore", "storeToRefs"]
  },

  piniaPersistedstate: {
    storage: "localStorage",
  },

  ssr: true,
  compatibilityDate: "2024-07-22",
  pwa: {
    registerType: 'autoUpdate',
    strategies: 'injectManifest',
    filename: 'service-worker.js',
    // srcDir: '/', // directory where your service-worker.js is located
    manifest: {
      name: 'Psy+ profissionais',
      short_name: 'Psy+ profissionais, Terapia acessível para todos',
      // srcDir: 'public',
      start_url: '/',
      description: 'Sistema de gestão e agendamento para psicólogos!',
      theme_color: '#4B87A4',
      icons: [
        {
          "src": "https://psyplus.site/icons/logo/48.png",
          "sizes": "48x48",
          "type": "image/webp",
          "purpose": "any maskable"
        },
        {
          "src": "https://psyplus.site/icons/logo/96.png",
          "sizes": "96x96",
          "type": "image/webp",
          "purpose": "any maskable"
        },
        {
          "src": "https://psyplus.site/icons/logo/192.png",
          "sizes": "192x192",
          "type": "image/webp",
          "purpose": "any maskable"
        },
        {
          "src": "https://psyplus.site/icons/logo/512.png",
          "sizes": "512x512",
          "type": "image/webp",
          "purpose": "any maskable"
        }
      ]
    },
    // workbox: {
    //   navigateFallback: '/',
    // },
    devOptions: {
      enabled: true,
      type: 'classic',
    },
  },
});