# Financial Analytics Dashboard

A comprehensive financial analytics dashboard for visualizing and analyzing financial data including payment analysis, cash flow, and DRE (Demonstração do Resultado do Exercício).

## 🚀 Features

### 📊 Analytics & Visualizations
- **Overview Dashboard**: Summary cards and overview charts
- **Payment Analysis**: Payment method distribution with pie and bar charts
- **Cash Flow**: Predicted vs actual cash flow with line charts
- **DRE**: Revenue, expenses, and profit visualization with bar charts

### 🔍 Filtering & Search
- Filter by year and month
- Date range filtering
- Real-time data updates
- Clear and apply filter controls

### 📁 Data Management
- CSV file upload for all data types
- Automatic data processing and parsing
- Support for Brazilian number formats
- Data validation and error handling

### 🔐 Authentication
- Secure admin login
- Token-based authentication
- Session management
- Auto-logout on token expiration

## 🛠️ Setup Instructions

### Backend Setup (Laravel)

1. **Navigate to backend directory**
   ```bash
   cd back
   ```

2. **Install dependencies** (if not already done)
   ```bash
   composer install
   ```

3. **Database Migration**
   ```bash
   php artisan migrate
   ```

4. **Seed Sample Data**
   ```bash
   php artisan db:seed --class=FinancialDataSeeder
   ```

5. **Start Laravel Server**
   ```bash
   php artisan serve
   ```

### Frontend Setup

1. **Navigate to financial dashboard directory**
   ```bash
   cd financial-dashboard
   ```

2. **Configure API URL** (if needed)
   - Edit `auth.js` and `api.js`
   - Update `baseUrl` to match your Laravel backend URL (default: `http://localhost:8000/api`)

3. **Serve Frontend**
   - Use any web server to serve the current directory
   - Or use Python's built-in server:
   ```bash
   python -m http.server 8080
   ```

4. **Access Dashboard**
   - Open `http://localhost:8080` in your browser
   - Login with admin credentials

## 📡 API Endpoints

### Authentication
- `POST /api/login-professional` - Admin login
- `GET /api/profile` - Get user profile

### Financial Data
- `GET /api/financial/` - Get overview data with filters
- `GET /api/financial/payment-analysis` - Get payment analysis data
- `GET /api/financial/cash-flow` - Get cash flow data
- `GET /api/financial/dre` - Get DRE data
- `POST /api/financial/upload-csv` - Upload CSV data

### Query Parameters
- `year` - Filter by year
- `month` - Filter by month (1-12)
- `start_date` - Start date (YYYY-MM-DD)
- `end_date` - End date (YYYY-MM-DD)
- `type` - Data type filter (payment_analysis, cash_flow, dre)

## 📄 CSV File Formats

### Payment Analysis CSV
```csv
Payment Method,Value,Percentage,Monthly Value
Cartão de Crédito,22500.00,45.67,22500.00
PIX,12500.00,25.33,12500.00
Boleto,7500.00,15.20,7500.00
```

### Cash Flow CSV
```csv
Category,Predicted,Actual
Receitas Operacionais,50000,48000
Vendas de Produtos,35000,33500
Despesas Operacionais,-25000,-26000
```

### DRE CSV
```csv
Categoria,Janeiro,Fevereiro,Março,Abril,Maio,Junho,Julho,Agosto,Setembro,Outubro,Novembro,Dezembro,Total
Receitas Operacionais,48656.40,46161.10,41691.10,42341.10,40191.10,40191.10,40191.10,40191.10,40191.10,40191.10,40191.10,41655.10,501842.50
Receita Líquida de Vendas,42582.48,46085.49,41691.09,42341.09,40191.09,40191.09,40191.09,40191.09,40191.09,40191.10,40191.10,41655.10,495692.90
```

## 📁 File Structure

```
financial-dashboard/
├── index.html          # Main dashboard
├── login.html          # Login page
├── style.css           # Styles
├── auth.js             # Authentication handling
├── api.js              # API communication
├── charts.js           # Chart management
├── app.js              # Main application logic
└── README.md           # This file
```

## 🔧 Technologies Used

### Frontend
- **HTML5/CSS3** - Structure and styling
- **Vanilla JavaScript** - Application logic
- **Chart.js** - Data visualization
- **Fetch API** - HTTP requests

### Backend
- **Laravel 11** - PHP framework
- **MySQL** - Database
- **Sanctum** - API authentication
- **Carbon** - Date manipulation

## 🌐 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📝 Usage Examples

### Uploading Data
1. Navigate to the "Upload Data" tab
2. Select the data type (Payment Analysis, Cash Flow, or DRE)
3. Choose the period date
4. Select your CSV file
5. Click "Upload"

### Filtering Data
1. Use the filter controls at the top
2. Select year, month, or date range
3. Click "Apply Filters"
4. Use "Clear" to reset filters

### Viewing Charts
- Charts automatically update when filters are applied
- Hover over chart elements for detailed information
- Charts are responsive and work on mobile devices

## 🚨 Important Notes

- Ensure your CSV files follow the exact format specified above
- Brazilian number format is supported (comma as decimal separator)
- All monetary values should be in the format: 1.234,56
- Date formats should be YYYY-MM-DD for API calls

## 🔒 Security

- All API endpoints require authentication
- Tokens expire automatically for security
- File uploads are validated and sanitized
- CORS is properly configured

## 📞 Support

For issues or questions, please check:
1. Browser console for JavaScript errors
2. Laravel logs for backend errors
3. Network tab for API request/response details

## 📄 License

This project is licensed under the MIT License.
