<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;
    protected $hidden = [
        'company_id',
    ];
    protected $fillable = [
        'company_id',
        'company_professional_id',
        'company_user_id',
        'service_id',
        'status',
        'subtotal',
        'discount',
        'total',
        'finished_at',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'discount' => 'decimal:2',
        'total' => 'decimal:2',
        'finished_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function client()
    {
        return $this->belongsTo(CompanyUser::class, 'company_user_id');
    }
    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }
}
