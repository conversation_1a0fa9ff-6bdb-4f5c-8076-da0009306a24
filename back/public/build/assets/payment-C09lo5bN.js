import{_ as m}from"./BaseButton-CxEL1ccK.js";import{_ as p,a as i}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{l as r,o as c,j as _,w as n,a as e,b as t,d as f}from"./app-BkKCG4YJ.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./BaseLayout--Fu-QUOh.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const d={class:"p-4 md:p-12 min-h-full"},u={class:"grid md:grid-cols-2 gap-12 mt-8"},B=r({__name:"payment",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(o){return(g,a)=>{const s=i,l=m;return c(),_(p,{slug:o.slug,company:o.company,professional:o.professional},{default:n(()=>[e("div",d,[a[1]||(a[1]=e("h1",{class:"text-3xl"},"Pagamento",-1)),e("div",u,[t(s,{label:"Nome"}),t(s,{label:"Email"}),t(s,{label:"Cep"})]),t(l,{class:"float-right mt-10 px-10"},{default:n(()=>a[0]||(a[0]=[f(" Pagar ")])),_:1})])]),_:1},8,["slug","company","professional"])}}});export{B as default};
