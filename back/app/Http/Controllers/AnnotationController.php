<?php

namespace App\Http\Controllers;

use App\Models\Annotation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\StoreAnnotationRequest;
use App\Http\Requests\UpdateAnnotationRequest;
use App\Models\CompanyProfessional;
use Illuminate\Support\Facades\Auth;

class AnnotationController extends Controller
{
    /**
     * Display a listing of the annotations.
     */
    public function index(): JsonResponse
    {
        // with(['companyProfessional', 'client', 'service'])->
        $user = Auth::user();
        $professional = CompanyProfessional::where('user_id', $user->id)->first();  
        $annotations = Annotation::with(['companyUser.user'])->where('company_professional_id', $professional->id)->get();
        
        return response()->json([
            'data' => $annotations
        ]);
    }

    /**
     * Store a newly created annotation.
     */
    public function store(Request $request): JsonResponse
    {
        // return response()->json($request->all());
        $user = Auth::user();
        $professional = CompanyProfessional::where('user_id', $user->id)->first();
        
        $annotation = Annotation::create([
            'company_professional_id' => $professional->id,
            'company_user_id' => $request->user_id,
            'order_id' => $request->order_id,
            'title' => $request->title,
            'description' => $request->description,
            'appointment_date' => $request->appointment_date,
            'status' => 'active',
        ]);

        return response()->json([
            'message' => 'Annotation created successfully',
            'data' => $annotation
        ], 201);
    }

    /**
     * Display the specified annotation.
     */
    public function show(Annotation $annotation): JsonResponse
    {
        $annotation->load(['companyProfessional', 'client', 'service']);
        
        return response()->json([
            'data' => $annotation
        ]);
    }

    /**
     * Update the specified annotation.
     */
    public function update(UpdateAnnotationRequest $request, Annotation $annotation): JsonResponse
    {
        $annotation->update($request->validated());

        return response()->json([
            'message' => 'Annotation updated successfully',
            'data' => $annotation
        ]);
    }

    /**
     * Remove the specified annotation.
     */
    public function destroy(Annotation $annotation): JsonResponse
    {
        $annotation->delete();

        return response()->json([
            'message' => 'Annotation deleted successfully'
        ]);
    }
} 