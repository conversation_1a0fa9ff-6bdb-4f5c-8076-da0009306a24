<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Company;
use App\Models\CompanyProfessional;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ServiceController extends Controller
{
    public function index(Request $request)
    {
        $companyID = $request->get('company_id');
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
        $filterName = $request->query('name');
        $sortBy = $request->query('sort_by', 'name'); // Ordenar por 'name' por padrão
        $sortDirection = $request->query('direction', 'asc'); // Ordem crescente por padrão

        $query = Service::where('company_id', $companyID);
        if ($filterName) {
            $query->where('name', 'like', '%' . $filterName . '%');
        }

        if (in_array($sortBy, ['name', 'price', 'duration'])) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $services = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($services, 200);
    }

    public function store(Request $request)
    {
        $data = $request->only(['name', 'description', 'price', 'duration']);
        $user = auth('sanctum')->user();
        $companyProfessional = CompanyProfessional::where('user_id', $user->id)->where('company_id', $request->get('company_id'))->first();
        $data['company_id'] = $companyProfessional->company_id;
        $service = Service::create($data);
        return response()->json(['service' => $service], 201);
    }
    public function getCompanyServices(string $slug)
    {
        $company = Company::where('slug', $slug)->first();
        $services = Service::where('company_id', $company->id)->get();
        return response()->json($services, 200);
    }
    public function show($id)
    {
        $service = DB::table('services')->where('id', $id)->first();

        if (!$service) {
            return response()->json(['message' => 'Service not found'], 404);
        }

        return response()->json($service, 200);
    }

    public function update(Request $request, $id)
    {
        $data = $request->only(['name', 'description', 'price', 'duration']);
        DB::table('services')->where('id', $id)->update($data);
        return response()->json(['message' => 'Serviço atualizado'], 200);
    }

    public function destroy($id)
    {
        $deleted = DB::table('services')->where('id', $id)->delete();

        if ($deleted) {
            return response()->json(['message' => 'Service deleted successfully'], 200);
        }

        return response()->json(['message' => 'Service not found'], 404);
    }
    public function getMostScheduled(string $slug)
    {
        $company = Company::where('slug', $slug)->first();
        $top5Appointments = Service::where('company_id', $company->id)
            ->limit(5)
            ->get();
        if ($top5Appointments->isEmpty()) {
            $services = Service::where('company_id', $company->id)->limit(5)->get();
            return response()->json($services, 200);
        }
        return response()->json($top5Appointments, 200);
    }
}
