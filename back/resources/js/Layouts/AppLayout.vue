<template>

    <Head>
        <!-- <link name="manifest" rel="manifest" :href="`/api/${slug}/manifest`" /> -->
    </Head>
    <!-- <TheHeader v-if="route().current() !== 'index'" :slug="slug" /> -->
    <BaseLayout :slug="slug" :company="company" :professional="professional">
        <!-- <div class="pb-[64px]"> -->
        <div class="h-bottom" :class="slug ? '' : ''">
            <slot />
        </div>
        <bottom-navigation :slug="slug" />
    </BaseLayout>
    <!-- Confirmation Modal -->
    <base-dialog v-if="showConfirmModal" v-model="showConfirmModal" title="Questionário Triagem">
        <div class="px-2 py-5">
            <p>{{ professional?.user.name }} preparou algumas perguntas antes do atendimento. Deseja prosseguir?</p>
            <div class="flex justify-end gap-2 mt-8">
                <!-- <base-button @click="showConfirmModal = false" color="ghost">
                    Cancelar
                </base-button> -->
                <base-button @click="openQuestionModal" color="primary">
                    Responder Questionário
                </base-button>
            </div>
        </div>
    </base-dialog>

    <!-- Questions Modal -->
    <question-modal v-model="showQuestionModal" :slug="slug" title="Questionário" :professional-id="professional?.id"
        @submitted="handleQuestionnaireSubmitted" />
</template>

<script setup lang="ts">
import { Head } from "@inertiajs/vue3";
import { ref } from 'vue';

import Color from "colorjs.io";
import { useCompanyStore } from "@/store/company";
import { useUser } from "@/store/user";
import { api } from "@/server/api";
import { useToast } from 'vue-toast-notification';
import BaseLayout from "./BaseLayout.vue";

const user = useUser();
const $toast = useToast();

// Modal states
const showConfirmModal = ref(false);
const showQuestionModal = ref(false);

// Function to get contrast color for accessibility
function getContrastColor(hexColor: string): string {
    // Convert hex to RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);

    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Return black or white based on luminance
    return luminance > 0.5 ? '#000000' : '#ffffff';
}

function toOklch(hex: string) {
    const color = new Color(hex).oklch;
    color[0] = Number((color[0] * 100).toFixed(4));
    const [color1, color2, color3] = color;
    return `${color1}% ${color2} ${color3 || 0}`;
}

const { slug, company, professional } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
    professional: {
        type: Object,
    },
});

const companyStore = useCompanyStore();

function updateColor({
    primaryColor,
    backgroundColor,
    cardsColor,
}: {
    primaryColor: string;
    backgroundColor: string;
    cardsColor: string;
}) {
    document.documentElement.style.setProperty("--p", toOklch(primaryColor));
    document.documentElement.style.setProperty(
        "--b1",
        toOklch(backgroundColor),
    );
    document.documentElement.style.setProperty("--b2", toOklch(cardsColor));
    const primaryContrast = getContrastColor(primaryColor);
    const cardsContrast = getContrastColor(cardsColor);
    const backgroundConstrast = getContrastColor(backgroundColor);

    document.documentElement.style.setProperty(
        "--ac",
        toOklch(backgroundConstrast),
    );
    document.documentElement.style.setProperty("--bc", toOklch(cardsContrast));
    document.documentElement.style.setProperty(
        "--pc",
        toOklch(primaryContrast),
    );
}

async function getClientInfo() {
    const hasQuestionary = professional?.hasQuestionary;
    if (!user.isLoggedIn || !hasQuestionary) return;

    const currentPath = window.location.pathname;
    if (currentPath.includes('/login') || currentPath.includes('/register')) return;
    const urlParams = new URLSearchParams(window.location.search);
    const checkForm = urlParams.get('checkForm');

    if (checkForm && !user.form_completed) {
        const { data } = await api.get(`/finished-form?slug=${slug}`);
        if (typeof data?.form_completed !== 'boolean') return;
        if (data.form_completed === true) user.form_completed = true;
        else {
            showConfirmModal.value = true;
        }
    }
}

// Function to check if we're on the finished-form route
// function checkFinishedFormRoute() {
//     const currentRoute = page.url;
//     if (currentRoute.includes('/finished-form') && professional.value?.hasQuestionary) {
//         showConfirmModal.value = true;
//     }
// }

function openQuestionModal() {
    showConfirmModal.value = false;
    showQuestionModal.value = true;
}

function handleQuestionnaireSubmitted() {
    $toast.success('Questionário enviado com sucesso!');
    showQuestionModal.value = false;
    user.form_completed = true;
}

onMounted(async () => {
    if (!user?.token?.length) return
    companyStore.logo = company.logo;
    companyStore.name = company.name;
    companyStore.plan_id = company.plan_id;
    companyStore.slug = company.slug;
    companyStore.sunday_time = company.sunday_time;
    companyStore.monday_time = company.monday_time;
    companyStore.tuesday_time = company.tuesday_time;
    companyStore.wednesday_time = company.wednesday_time;
    companyStore.thursday_time = company.thursday_time;
    companyStore.friday_time = company.friday_time;
    companyStore.saturday_time = company.saturday_time;
    companyStore.solo_professional = company.solo_professional;
    companyStore.background_color = company.background_color;
    companyStore.buttons_color = company.buttons_color;
    companyStore.cards_color = company.cards_color;
    updateColor({
        backgroundColor: companyStore.background_color,
        primaryColor: companyStore.buttons_color,
        cardsColor: companyStore.cards_color,
    });

    getClientInfo();
    // checkFinishedFormRoute();
});
</script>
