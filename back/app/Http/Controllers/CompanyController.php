<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\Company;
use App\Models\CompanyProfessional;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\App;

class CompanyController extends Controller
{
    public function create(Request $request)
    {
        $data = $request->only([
            'name',
            'logo',
            'sunday_time',
            'monday_time',
            'tuesday_time',
            'wednesday_time',
            'thursday_time',
            'friday_time',
            'saturday_time',
            'buttons_color',
            'background_color',
            'cards_color'
        ]);
        $config = DB::table('company')->create($data);
        return $config;
    }
    public function startSetup(Request $request)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'phone' => 'required|string',
            'password' => 'required|string',
        ]);

        $user = User::where('phone', $request->phone)->first();

        if ($user) {
            $request->validate([
                'has_email' => 'required|boolean'
            ]);

            if (!$request->has_email) {
                $request->validate([
                    'email' => 'required|email|unique:users'
                ]);
            }

            if (!auth()->guard('web')->attempt(['phone' => $request->phone, 'password' => $request->password])) {
                return response()->json(['message' => 'Invalid credentials'], 401);
            }

            if (!$request->has_email) {
                $user->email = $request->email;
                $user->save();
            }
        } else {
            $request->validate([
                'email' => 'required|email|unique:users',
                'name' => 'required|string|max:255',
                'password_confirmation' => 'required|string|min:8',
            ]);

            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = Hash::make($request->password);
            $user->phone = $request->phone;
            $user->type = 'admin';
            $user->profile_photo_path = "https://ui-avatars.com/api/?name=$request->name";
            $user->save();
        }

        // Update user type to admin
        $user->type = 'admin';
        $user->save();

        // Create company
        $company = new Company();
        $company->name = $request->company_name;
        $company->tryal_phase = true;
        $company->solo_professional = true;

        // Generate slug
        // First replace spaces with hyphens, then remove non-alphanumeric characters (except hyphens)
        $loweredName = strtolower(preg_replace('/[^a-zA-Z0-9\-]/', '', str_replace(" ", "-", $request->company_name)));
        // get first name before space
        // $loweredName = explode("_", $loweredName)[0];
        $slug = $loweredName;
        $companiesWithSlug = Company::where('slug', $slug)->count();
        if ($companiesWithSlug > 0) {
            $hash = bin2hex(random_bytes(4));
            $slug = $slug . '-' . $hash;
        }
        $companiesWithSlug = Company::where('slug', $slug)->count();
        if ($companiesWithSlug > 0) {
            $hash = bin2hex(random_bytes(4));
            $slug = $slug . '-' . $hash;
        }
        $company->slug = $slug;
        $company->save();
        $companyProfessional = new CompanyProfessional();
        $companyProfessional->user_id = $user->id;
        $companyProfessional->company_id = $company->id;
        $companyProfessional->CRP = $request->CRP;
        $companyProfessional->slug = $slug;
        $companyProfessional->save();

        $this->populateCompanyWithDataExamples($company);

        $token = $user->createToken('token')->plainTextToken;
        return response()->json(['user' => $user, 'company' => $company, 'token' => $token], 201);
    }
    public function registerUser(Request $request)
    {
        $request->validate([
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'name' => 'required|string',
        ]);
    }
    public function updateTime(Request $request)
    {
        $data = $request->only([
            'sunday_time',
            'monday_time',
            'tuesday_time',
            'wednesday_time',
            'thursday_time',
            'friday_time',
            'saturday_time',
            'sunday_pause',
            'monday_pause',
            'tuesday_pause',
            'wednesday_pause',
            'thursday_pause',
            'friday_pause',
            'saturday_pause'
        ]);
        $companyProfessional = CompanyProfessional::where('user_id', $request->user()->id)->
            where('company_id', $request->get('company_id'))->first();
        $company = Company::find($companyProfessional->company_id);
        $company->sunday_time = $request->sunday_time;
        $company->monday_time = $request->monday_time;
        $company->tuesday_time = $request->tuesday_time;
        $company->wednesday_time = $request->wednesday_time;
        $company->thursday_time = $request->thursday_time;
        $company->friday_time = $request->friday_time;
        $company->saturday_time = $request->saturday_time;
        $company->sunday_pause = $request->sunday_pause;
        $company->monday_pause = $request->monday_pause;
        $company->tuesday_pause = $request->tuesday_pause;
        $company->wednesday_pause = $request->wednesday_pause;
        $company->thursday_pause = $request->thursday_pause;
        $company->friday_pause = $request->friday_pause;
        $company->saturday_pause = $request->saturday_pause;
        $company->save();
        return response()->json(['company' => $company], 200);
    }
    public function index()
    {
        // $userId = auth()->id();

        // if (!$userId) {
        //     return response()->json(['message' => 'Unauthorized'], 401);
        // }

        // try {
        //     $config = DB::table('configs')->where('admin_id', $userId)->firstOrFail();
        //     return response()->json($config, 200);
        // } catch (\Exception $e) {
        //     // Cria configuração padrão caso não exista
        //     $configId = DB::table('configs')->insertGetId([
        //         'admin_id' => $userId,
        //         'company_name' => 'teste',
        //     ]);
        //     $config = DB::table('configs')->where('id', $configId)->firstOrFail();
        //     return response()->json($config, 200);
        // }
    }
    public function getClientConfig()
    {
        try {
            $config = DB::table('configs')->firstOrFail();
            return response()->json($config, 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error retrieving client config'], 500);
        }
    }
    public function updateMyCompany(Request $request)
    {
        $data = $request->only([
            'name',
            'monday_time',
            'sunday_time',
            'tuesday_time',
            'thursday_time',
            'wednesday_time',
            'friday_time',
            'saturday_time',
            'monday_pause',
            'tuesday_pause',
            'wednesday_pause',
            'thursday_pause',
            'friday_pause',
            'saturday_pause',
            'sunday_pause',
            'buttons_color',
            'background_color',
            'cards_color',
            'instagram',
            'whatsapp',
            'slug',
            'CRP'
        ]);
        $companyID = $request->get('company_id');
        $company = Company::find($companyID);
        $company->update($data);
        if($company->solo_professional) {
            $companyProfessional = CompanyProfessional::where('company_id', $companyID)->first();
            $companyProfessional->about = $request->about;
            $companyProfessional->specialties = $request->specialties;
            $companyProfessional->public = $request->public;
            // $companyProfessional->CRP = $request->CRP;
            $companyProfessional->slug = $request->slug;
            $companyProfessional->save();
        }
        return $company;
    }
    public function getMyCompany(Request $request)
    {
        $companyID = $request->get('company_id');
        $company = Company::find($companyID);
        if($company->solo_professional) {
            $companyProfessional = CompanyProfessional::where('company_id', $companyID)->first();
            $specialties = strlen($companyProfessional->specialties) ? explode(',', $companyProfessional->specialties) : [];
            $public = strlen($companyProfessional->public) ? explode(',', $companyProfessional->public) : [];
            $company->about = $companyProfessional->about;
            $company->specialties = $specialties;
            $company->public = $public;
            $company->CRP = $companyProfessional->CRP;
        }
        return $company;
    }
    public function getCompany(string $slug)
    {
        $company = Company::where('slug', $slug)->first();
        $companyProfessional = CompanyProfessional::where('company_id', $company->id)->count();
        $company->solo_professional = $companyProfessional === 1;
        return $company;
    }
    public function getManifest(string $slug)
    {
        $company = Company::where('slug', $slug)->first();
        $path = $company->logo;
        return response()->json([
            'name' => $company->name,
            'short_name' => $company->name,
            'start_url' => "/{$slug}",
            'display' => 'standalone',
            'background_color' => $company->background_color,
            'theme_color' => $company->buttons_color,
            'icons' => [
                [
                    'src' => "{$path}_48.webp",
                    'type' => 'image/webp',
                    'sizes' => '48x48',
                ],
                [
                    'src' => "{$path}_96.webp",
                    'type' => 'image/webp',
                    'sizes' => '96x96',
                ],
                [
                    'src' => "{$path}_192.webp",
                    'type' => 'image/webp',
                    'sizes' => '192x192',
                ],
                [
                    'src' => "{$path}_512.webp",
                    'type' => 'image/webp',
                    'sizes' => '512x512',
                ],

            ],
        ]);
    }
    public function updateLogo(Request $request)
    {
        $request->validate([
            'image' => 'required|string', // Base64 é uma string
        ]);

        $logoData = $request->input('image');
        $imageParts = explode(';base64,', $logoData);
        if (count($imageParts) !== 2) {
            return response()->json(['error' => 'Formato inválido de base64.'], 400);
        }

        $imageBase64 = base64_decode($imageParts[1]);
        $sourceImage = imagecreatefromstring($imageBase64);

        if (!$sourceImage) {
            return response()->json(['error' => 'Não foi possível processar a imagem.'], 400);
        }

        $dimensions = [
            '512x512' => [512, 512],
            '192x192' => [192, 192],
            '96x96' => [96, 96],
            '48x48'   => [48, 48],
        ];

        $newFilePaths = [];
        $companyId = $request->get('company_id');
        $hash = bin2hex(random_bytes(4)); // Generate a random hash

        foreach ($dimensions as $suffix => [$width, $height]) {
            $resizedImage = imagecreatetruecolor($width, $height);

            imagealphablending($resizedImage, false);
            imagesavealpha($resizedImage, true);

            $transparentColor = imagecolorallocatealpha($resizedImage, 0, 0, 0, 127);
            imagefill($resizedImage, 0, 0, $transparentColor);

            imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, $width, $height, imagesx($sourceImage), imagesy($sourceImage));

            $filename = "company_{$companyId}_{$hash}_{$width}.webp";
            $path = "company_logos/{$filename}";

            ob_start();
            imagewebp($resizedImage, null, 100); // Qualidade WebP (0 a 100)
            $compressedImage = ob_get_clean();
            Storage::disk('public')->put($path, $compressedImage);
            $newFilePaths[$width] = $path;

            // Create JPG version for 512x512 meta image
            if ($width === 512) {
                $jpgFilename = "company_{$companyId}_{$hash}_512.jpg";
                $jpgPath = "company_logos/{$jpgFilename}";
                ob_start();
                imagejpeg($resizedImage, null, 100);
                $jpgImage = ob_get_clean();
                Storage::disk('public')->put($jpgPath, $jpgImage);
                $newFilePaths['512_jpg'] = $jpgPath;
            }

            imagedestroy($resizedImage);
        }

        // Delete old files with the same company ID, regardless of the hash
        $oldFiles = Storage::disk('public')->files("company_logos");
        foreach ($oldFiles as $file) {
            if (preg_match("/company_{$companyId}_[a-f0-9]+_\d+\.(webp|jpg)$/", $file) && !in_array($file, $newFilePaths)) {
                Storage::disk('public')->delete($file);
            }
        }

        $urls = array_map(fn($path) => Storage::url($path), $newFilePaths);
        imagedestroy($sourceImage);

        $company = Company::find($companyId);
        $company->logo = "/logos/company_{$company->id}_{$hash}";
        $company->save();

        return response()->json([
            'message' => 'Logo atualizada com sucesso!',
            'logos' => $urls,
        ]);
    }
    public function updateBanner(Request $request)
    {
        $request->validate([
            'image' => 'required|string', // Base64 é uma string
        ]);

        $bannerData = $request->input('image');
        $imageParts = explode(';base64,', $bannerData);
        if (count($imageParts) !== 2) {
            return response()->json(['error' => 'Formato inválido de base64.'], 400);
        }

        $imageBase64 = base64_decode($imageParts[1]);
        $sourceImage = imagecreatefromstring($imageBase64);

        if (!$sourceImage) {
            return response()->json(['error' => 'Não foi possível processar a imagem.'], 400);
        }

        $width = 900;
        $height = 300;
        $companyId = $request->get('company_id');
        $hash = bin2hex(random_bytes(4)); // Generate a random hash

        $resizedImage = imagecreatetruecolor($width, $height);

        // Manter transparência
        imagealphablending($resizedImage, false);
        imagesavealpha($resizedImage, true);

        $transparentColor = imagecolorallocatealpha($resizedImage, 0, 0, 0, 127);
        imagefill($resizedImage, 0, 0, $transparentColor);

        imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, $width, $height, imagesx($sourceImage), imagesy($sourceImage));

        $filename = "company_{$companyId}_{$hash}.webp";
        $path = "company_banners/{$filename}";

        ob_start();
        imagewebp($resizedImage, null, 100); // Qualidade WebP (0 a 100)
        $compressedImage = ob_get_clean();
        Storage::disk('public')->put($path, $compressedImage);

        // Delete old files with the same company ID, regardless of the hash
        $oldFiles = Storage::disk('public')->files("company_banners");
        foreach ($oldFiles as $file) {
            if (preg_match("/company_{$companyId}_[a-f0-9]+\.webp$/", $file) && $file !== $path) {
                Storage::disk('public')->delete($file);
            }
        }

        imagedestroy($resizedImage);
        imagedestroy($sourceImage);

        $company = Company::find($companyId);
        $company->banner = "/banners/company_{$company->id}_{$hash}.webp";
        $company->save();

        return response()->json([
            'message' => 'Banner atualizado com sucesso!',
            'banner_url' => Storage::url($path),
        ]);
    }
    public function populateCompanyWithDataExamples(Company $company)
    {
        $service = new Service();
        $service->name = 'Consulta';
        $service->description = 'Consulta simples';
        $service->price = 100;
        $service->duration = 60;
        $service->company_id = $company->id;
        $service->save();
        return $company;
    }
}
