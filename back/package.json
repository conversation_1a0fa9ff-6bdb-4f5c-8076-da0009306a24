{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@heroicons/vue": "^2.0.18", "@inertiajs/vue3": "^1.0.14", "@types/node": "^22", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.16", "axios": "^1.7.4", "colorjs.io": "^0.5.2", "concurrently": "^9.0.1", "culori": "^4.0.1", "daisyui": "^4.12.22", "dayjs": "^1.11.7", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-vue": "^9.14.1", "laravel-vite-plugin": "^1.0", "maska": "^2.1.11", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^4.2.0", "postcss": "^8.4.32", "prettier": "latest", "sass": "^1.8", "tailwind-config-viewer": "^1.7.2", "tailwindcss": "^3.4.0", "typescript": "^5.2", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "v-lazy-image": "^2.1.1", "vee-validate": "^4.10.5", "vite": "^5.0", "vite-plugin-checker": "^0.8", "vue": "^3.5", "vue-currency-input": "^3.0.5", "vue-i18n": "^9.4.1", "vue-toast-notification": "^3.1.1", "vue-tsc": "^2", "yup": "^1.2.0"}, "dependencies": {"laravel-echo": "^2.0.2"}}