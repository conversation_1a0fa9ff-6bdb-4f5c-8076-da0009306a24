<template>
  <div>
    <div class="p-6 h-full w-full">
      <!-- <div class="flex justify-end gap-4 mb-8">
        <base-button @click="isScheduleFormOpen = true">
          Agendar
        </base-button>
        <base-button @click="isProductFormOpen = true" variant="secondary">
          Cadastrar Produto
        </base-button>
      </div> -->

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div
          class="bg-gradient-to-br p-2 rounded shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div class="flex items-center p-2">
            <div class="flex flex-col">
              <span class="text-lg font-bold">Agendamentos</span>
              <span class="text-3xl font-bold">{{ totalAppointments }}</span>
            </div>
          </div>
        </div>

        <div
          class="bg-gradient-to-br p-2 rounded shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div class="flex items-center p-2">
            <div class="flex flex-col">
              <span class="text-lg font-bold">Lucro Mensal</span>
              <span class="text-3xl font-bold">R$ {{ totalProfit }}</span>
            </div>
          </div>
        </div>

        <div
          class="bg-gradient-to-br p-2 rounded shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div class="flex items-center p-2">
            <div class="flex flex-col">
              <span class="text-lg font-bold">Gastos Mensais</span>
              <span class="text-3xl font-bold">R$ {{ totalSpent }}</span>
            </div>
          </div>
        </div>

        <div
          class="bg-gradient-to-br p-2 rounded shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div class="flex items-center p-2">
            <div class="flex flex-col">
              <span class="text-lg font-bold">Produtos Utilizados</span>
              <span class="text-3xl font-bold">{{ totalProducts }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- <base-dialog title="Novo Agendamento" v-model="isScheduleFormOpen">
        <schedule-register-form
          v-if="isScheduleFormOpen"
          @submit="handleScheduleCreate"
        />
      </base-dialog>

      <base-dialog title="Novo Produto" v-model="isProductFormOpen">
        <products-register-form
          v-if="isProductFormOpen"
          @submit="handleProductCreate"
        />
      </base-dialog> -->
    </div>
  </div>
</template>

<script setup lang="ts">
const totalAppointments = ref(0);
const totalProfit = ref(0);
const totalSpent = ref(0);
const totalProducts = ref(0);

const isScheduleFormOpen = ref(false);
const isProductFormOpen = ref(false);

const handleScheduleCreate = () => {
  isScheduleFormOpen.value = false;
  // Refresh data after creation
  fetchDashboardData();
};

const handleProductCreate = () => {
  isProductFormOpen.value = false;
  // Refresh data after creation
  fetchDashboardData();
};

const fetchDashboardData = async () => {
  try {
    // Redirect to analytics page for admin dashboard
    await navigateTo('/analytics');
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
  }
};

onMounted(() => {
  fetchDashboardData();
});
</script>
