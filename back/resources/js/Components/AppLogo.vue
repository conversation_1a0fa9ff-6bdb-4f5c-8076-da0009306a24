<template>
    <div class="flex items-center">
        <img :src="logoSrc" :width="size" :height="size" alt="Psy+ Logo" />
        <span v-if="showText" class="ml-2 text-xl font-bold text-primary">Psy+</span>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    size: {
        type: [Number, String],
        default: 40
    },
    showText: {
        type: Boolean,
        default: true
    }
});

const logoSrc = computed(() => {
    return '/img/logo.png';
});
</script>
