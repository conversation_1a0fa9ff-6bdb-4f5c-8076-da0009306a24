<template>
    <component :is="slug ? AppLayout : BaseLayout" :slug="slug" :company="company" :professional="professional">
        <div class="p-2 md:p-12 h-full flex flex-col justify-center px-3">
            <div
                class="bg-base-200 overflow-y-auto rounded-lg w-full mx-auto text-white shadow-xl overflow-hidden flex flex-col sm:flex-row justify-center items-center md:max-w-96 ">
                <div class="w-full h-full flex flex-col gap-2 px-5 pt-5  flex-1">
                    <div>
                        <label for="file" class="cursor-pointer">
                            <img class="w-24 h-24 object-cover rounded-full  mx-auto" :src="image"
                                alt="Profile image" />
                            <input id="file" type="file" class="hidden" @change="uploadImage" />
                        </label>
                    </div>
                    <input-base class="w-full" label="Nome" placeholder="name" type="text" v-model="name" />
                    <input-base class="w-full" label="email" placeholder="email" type="email" v-model="email" />
                    <input-base class="w-full" label="Telefone" placeholder="phone" type="phone"
                        data-maska="(##) #####-####" v-model="phone" />
                    <base-button :loading="loading" @click="updateProfile" class="mt-2 w-full btn btn-primary">
                        Salvar Alterações
                    </base-button>
                    <div class="w-full pb-2">
                        <base-button @click="
                        () => {
                            useUser().logout(slug);
                            if (slug) {
                                router.get(`/${slug}/login`);
                            }
                            else {
                                router.get(`/login`);
                            }
                        }
                        " class="text-base-content w-full btn-ghost">
                        Sair da conta
                        <ArrowRightOnRectangleIcon class="w-6 ml-1" />
                    </base-button>
                </div>
                </div>
                <!-- <div class="hidden sm:block bg-gray-500 w-[2px] divider divider-horizontal mx-0 my-auto h-80"></div> -->
                <!-- <div class="w-full px-5 pt-2 md:py-5 mt-auto flex-1">
                    <p class="mb-5 text-accent-content font-bold text-center">
                        Alterar Senha
                    </p>
                    <form class="gap-y-2 flex flex-col mt-auto">
                        <input-base class="w-full" label="Senha Atual" placeholder="Senha Atual" type="password"
                            v-model="password" />
                        <input-base class="w-full" label="Nova Senha" placeholder="Nova Senha" type="password"
                            v-model="newPassword" />
                        <input-base class="w-full" label="Confirmar Senha" placeholder="Confirmar Senha" type="password"
                            v-model="confirmPassword" />
                        <base-button :loading="loading" @click="changePassword" class="mt-2">
                            Alterar Senha
                        </base-button>
                    </form>
                </div> -->
                <!-- <div class="w-full px-5 pt-2 md:py-5 mt-auto flex-1">
                    <div class="gap-y-2 flex flex-col mt-auto">

                    </div>
                </div> -->
            </div>
        </div>
    </component>
</template>
<script setup lang="ts">
import AppLayout from "@/Layouts/AppLayout.vue";
import { api } from "@/server/api";
import { ArrowRightOnRectangleIcon } from "@heroicons/vue/24/solid";
import { ref, onMounted } from "vue";
import { useToast } from "vue-toast-notification";
import { useUser } from "@/store/user";
import { router } from "@inertiajs/vue3";
import BaseLayout from "@/Layouts/BaseLayout.vue";
const toast = useToast();
const loading = ref(false);
const name = ref("");
const email = ref("");
const phone = ref("");
const image = ref(
    "https://upload.wikimedia.org/wikipedia/commons/a/ad/Placeholder_no_text.svg",
);
const newImage = ref();
const password = ref("");
const newPassword = ref("");
const confirmPassword = ref("");
async function getProfile() {
    loading.value = true;
    const { data } = await api.get("/profile");
    name.value = data.name;
    email.value = data.email;
    phone.value = data.phone;
    if (data.profile_photo_path)
        image.value = '/' + data.profile_photo_path;
    loading.value = false;
}
async function updateProfile() {
    loading.value = true;
    try {
        await api.put("/profile", {
            name: name.value,
            email: email.value,
            phone: phone.value,
        });
        loading.value = false;
        toast.success("Perfil Atualizado", { position: "top" });
    } catch (err) {
        if (axios.isAxiosError(err)) {
            toast.error(err.response?.data.message, { position: "top" });
        }
    } finally {
        loading.value = false;
    }
}
async function changePassword() {
    loading.value = true;
    await api.post("/change-password", {
        password: password.value,
        newPassword: newPassword.value,
        confirmPassword: confirmPassword.value,
    });
    loading.value = false;
}
async function uploadImage(e: Event) {
    try {
        const file = (e.target as HTMLInputElement).files![0];
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = async () => {
            image.value = reader.result as string;
            await api.post("/update-photo", { image: reader.result });
        };
    } catch (err) {
        if (axios.isAxiosError(err)) {
            toast.error(err.response?.data.message, { position: "top" });
        }
    } finally {
        loading.value = false;
    }
}
const { slug } = defineProps({
    slug: { type: String },
    company: { type: Object },
    professional: { type: Object },
});
onMounted(() => {
    getProfile();
});
</script>
