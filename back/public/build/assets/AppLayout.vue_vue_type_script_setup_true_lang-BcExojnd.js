var Yr=Object.defineProperty;var Xr=(t,e,n)=>e in t?Yr(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var tt=(t,e,n)=>Xr(t,typeof e!="symbol"?e+"":e,n);import{o as C,c as R,a as $,l as fe,t as Z,g as we,b as X,u as oe,Q as _t,n as G,r as z,f as Oe,A as Je,R as Zr,S as Wr,U as He,V as Xn,j as le,m as ze,W as Jr,y as qr,q as Zn,h as Wn,w as ue,e as ft,v as Ur,p as Qr,X as Kr,F as Ge,d as gt,B as ea,N as ta,Z as na}from"./app-BkKCG4YJ.js";import{_ as Jn}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{_ as qn}from"./BaseButton-CxEL1ccK.js";import{a as pt,b as Un,e as ra,f as aa,c as sa,_ as ia}from"./BaseLayout--Fu-QUOh.js";import{r as oa}from"./CalendarDaysIcon-BL3Qt4XI.js";function la(t,e){return C(),R("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[$("path",{"fill-rule":"evenodd",d:"M6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6Zm1.5 1.5a.75.75 0 0 0-.75.75V16.5a.75.75 0 0 0 1.085.67L12 15.089l4.165 2.083a.75.75 0 0 0 1.085-.671V5.25a.75.75 0 0 0-.75-.75h-9Z","clip-rule":"evenodd"})])}function ua(t,e){return C(),R("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[$("path",{"fill-rule":"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z","clip-rule":"evenodd"})])}const ca={class:"modal-box overflow-visible text-accent-content"},da={key:0,class:"text-2xl text-center -mt-1 mb-2"},ma=fe({inheritAttrs:!1,__name:"BaseDialog",props:{modelValue:{type:Boolean,default:!1},title:{type:String,default:void 0},loading:{type:Boolean,default:!1},hideClose:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(t,{emit:e}){const n=e;return(r,a)=>(C(),R("div",{class:G([[t.modelValue&&"modal-open",r.$attrs.class],"modal"])},[$("div",ca,[t.title?(C(),R("div",da,Z(t.title),1)):we("",!0),t.hideClose?we("",!0):(C(),R("div",{key:1,onClick:a[0]||(a[0]=()=>{t.loading||n("update:modelValue",!1)}),class:"absolute -right-2 -top-2 text-xl cursor-pointer z-50"},[X(oe(ua),{class:"w-9 h-9"})])),_t(r.$slots,"default",{},void 0,!0)])],2))}}),Qn=Jn(ma,[["__scopeId","data-v-16b6b2ce"]]);/**
 * Vue Currency Input 3.1.0
 * (c) 2018-2024 Matthias Stiller
 * @license MIT
 */var te;(function(t){t.symbol="symbol",t.narrowSymbol="narrowSymbol",t.code="code",t.name="name",t.hidden="hidden"})(te||(te={}));var K;(function(t){t.precision="precision",t.thousands="thousands",t.millions="millions",t.billions="billions"})(K||(K={}));const Te=t=>t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),Kn=t=>t.replace(/^0+(0$|[^0])/,"$1"),nt=(t,e)=>(t.match(new RegExp(Te(e),"g"))||[]).length,ha=(t,e)=>t.substring(0,t.indexOf(e)),er=[",",".","٫","。"],Tt="(0|[1-9]\\d*)";class fa{constructor(e){var n,r,a,s,o,i;const{currency:l,currencyDisplay:u,locale:c,precision:d,accountingSign:m,useGrouping:f}=e;this.locale=c,this.options={currency:l,useGrouping:f,style:"currency",currencySign:m?"accounting":void 0,currencyDisplay:u!==te.hidden?u:void 0};const h=new Intl.NumberFormat(c,this.options),p=h.formatToParts(123456);this.currency=(n=p.find(({type:M})=>M==="currency"))===null||n===void 0?void 0:n.value,this.digits=[0,1,2,3,4,5,6,7,8,9].map(M=>M.toLocaleString(c)),this.decimalSymbol=(r=p.find(({type:M})=>M==="decimal"))===null||r===void 0?void 0:r.value,this.groupingSymbol=(a=p.find(({type:M})=>M==="group"))===null||a===void 0?void 0:a.value,this.minusSign=(s=h.formatToParts(-1).find(({type:M})=>M==="minusSign"))===null||s===void 0?void 0:s.value,this.decimalSymbol===void 0?this.minimumFractionDigits=this.maximumFractionDigits=0:typeof d=="number"?this.minimumFractionDigits=this.maximumFractionDigits=d:(this.minimumFractionDigits=(o=d==null?void 0:d.min)!==null&&o!==void 0?o:h.resolvedOptions().minimumFractionDigits,this.maximumFractionDigits=(i=d==null?void 0:d.max)!==null&&i!==void 0?i:h.resolvedOptions().maximumFractionDigits);const g=M=>ha(M,this.digits[1]),b=M=>M.substring(M.lastIndexOf(this.decimalSymbol?this.digits[0]:this.digits[1])+1);this.prefix=g(h.format(1)),this.suffix=b(h.format(1)),this.negativePrefix=g(h.format(-1)),this.negativeSuffix=b(h.format(-1))}parse(e){if(e){const n=this.isNegative(e);e=this.normalizeDigits(e),e=this.stripCurrency(e,n),e=this.stripSignLiterals(e);const r=this.decimalSymbol?`(?:${Te(this.decimalSymbol)}(\\d*))?`:"",a=this.stripGroupingSeparator(e).match(new RegExp(`^${Tt}${r}$`));if(a&&this.isValidIntegerFormat(this.decimalSymbol?e.split(this.decimalSymbol)[0]:e,Number(a[1])))return+`${n?"-":""}${this.onlyDigits(a[1])}.${this.onlyDigits(a[2]||"")}`}return null}isValidIntegerFormat(e,n){const r={...this.options,minimumFractionDigits:0};return[this.stripCurrency(this.normalizeDigits(n.toLocaleString(this.locale,{...r,useGrouping:!0})),!1),this.stripCurrency(this.normalizeDigits(n.toLocaleString(this.locale,{...r,useGrouping:!1})),!1)].includes(e)}format(e,n={minimumFractionDigits:this.minimumFractionDigits,maximumFractionDigits:this.maximumFractionDigits}){return e!=null?e.toLocaleString(this.locale,{...this.options,...n}):""}toFraction(e){return`${this.digits[0]}${this.decimalSymbol}${this.onlyLocaleDigits(e.substring(1)).substring(0,this.maximumFractionDigits)}`}isFractionIncomplete(e){return!!this.normalizeDigits(this.stripGroupingSeparator(e)).match(new RegExp(`^${Tt}${Te(this.decimalSymbol)}$`))}isNegative(e){return e.startsWith(this.negativePrefix)||this.minusSign===void 0&&(e.startsWith("(")||e.startsWith("-"))||this.minusSign!==void 0&&e.replace("-",this.minusSign).startsWith(this.minusSign)}insertCurrency(e,n){return`${n?this.negativePrefix:this.prefix}${e}${n?this.negativeSuffix:this.suffix}`}stripGroupingSeparator(e){return this.groupingSymbol!==void 0?e.replace(new RegExp(Te(this.groupingSymbol),"g"),""):e}stripSignLiterals(e){return this.minusSign!==void 0?e.replace("-",this.minusSign).replace(this.minusSign,""):e.replace(/[-()]/g,"")}stripCurrency(e,n){return e.replace(n?this.negativePrefix:this.prefix,"").replace(n?this.negativeSuffix:this.suffix,"")}normalizeDecimalSeparator(e,n){return er.forEach(r=>{e=e.substring(0,n)+e.substring(n).replace(r,this.decimalSymbol)}),e}normalizeDigits(e){return this.digits[0]!=="0"&&this.digits.forEach((n,r)=>{e=e.replace(new RegExp(n,"g"),String(r))}),e}onlyDigits(e){return this.normalizeDigits(e).replace(/\D+/g,"")}onlyLocaleDigits(e){return e.replace(new RegExp(`[^${this.digits.join("")}]*`,"g"),"")}}class tr{constructor(e){this.currencyFormat=e}}class ga extends tr{conformToMask(e,n=""){const r=this.currencyFormat.isNegative(e),a=h=>h===""&&r&&!(this.currencyFormat.minusSign===void 0?n===this.currencyFormat.negativePrefix+this.currencyFormat.negativeSuffix:n===this.currencyFormat.negativePrefix),s=h=>{if(a(h))return"";if(this.currencyFormat.maximumFractionDigits>0){if(this.currencyFormat.isFractionIncomplete(h))return h;if(h.startsWith(this.currencyFormat.decimalSymbol))return this.currencyFormat.toFraction(h)}return null};let o=e;o=this.currencyFormat.stripCurrency(o,r),o=this.currencyFormat.stripSignLiterals(o);const i=s(o);if(i!=null)return this.currencyFormat.insertCurrency(i,r);const[l,...u]=o.split(this.currencyFormat.decimalSymbol),c=Kn(this.currencyFormat.onlyDigits(l)),d=this.currencyFormat.onlyDigits(u.join("")).substring(0,this.currencyFormat.maximumFractionDigits),m=u.length>0&&d.length===0,f=c===""&&r&&(this.currencyFormat.minusSign===void 0?n===e.slice(0,-2)+this.currencyFormat.negativeSuffix:n===e.slice(0,-1));return m||f||a(c)?n:c.match(/\d+/)?{numberValue:+`${r?"-":""}${c}.${d}`,fractionDigits:d}:""}}class pa extends tr{conformToMask(e,n=""){if(e===""||this.currencyFormat.parse(n)===0&&this.currencyFormat.stripCurrency(n,!0).slice(0,-1)===this.currencyFormat.stripCurrency(e,!0))return"";const r=this.currencyFormat.isNegative(e),a=this.currencyFormat.stripSignLiterals(e)===""?-0:+`${r?"-":""}${Kn(this.currencyFormat.onlyDigits(e))}`/Math.pow(10,this.currencyFormat.maximumFractionDigits);return{numberValue:a,fractionDigits:a.toFixed(this.currencyFormat.maximumFractionDigits).slice(-this.currencyFormat.maximumFractionDigits)}}}const ba={locale:void 0,currency:void 0,currencyDisplay:void 0,hideGroupingSeparatorOnFocus:!0,hideCurrencySymbolOnFocus:!0,hideNegligibleDecimalDigitsOnFocus:!0,precision:void 0,autoDecimalDigits:!1,valueRange:void 0,useGrouping:void 0,valueScaling:void 0};class ya{constructor(e){this.el=e.el,this.onInput=e.onInput,this.onChange=e.onChange,this.addEventListener(),this.init(e.options)}setOptions(e){this.init(e),this.format(this.currencyFormat.format(this.validateValueRange(this.numberValue))),this.onChange(this.getValue())}getValue(){return{number:this.valueScaling&&this.numberValue!=null?this.toInteger(this.numberValue,this.valueScaling):this.numberValue,formatted:this.formattedValue}}setValue(e){const n=this.valueScaling!==void 0&&e!=null?this.toFloat(e,this.valueScaling):e;n!==this.numberValue&&(this.format(this.currencyFormat.format(this.validateValueRange(n))),this.onChange(this.getValue()))}init(e){this.options={...ba,...e},this.options.autoDecimalDigits&&(this.options.hideNegligibleDecimalDigitsOnFocus=!1),this.el.getAttribute("inputmode")||this.el.setAttribute("inputmode",this.options.autoDecimalDigits?"numeric":"decimal"),this.currencyFormat=new fa(this.options),this.numberMask=this.options.autoDecimalDigits?new pa(this.currencyFormat):new ga(this.currencyFormat);const n={[K.precision]:this.currencyFormat.maximumFractionDigits,[K.thousands]:3,[K.millions]:6,[K.billions]:9};this.valueScaling=this.options.valueScaling?n[this.options.valueScaling]:void 0,this.valueScalingFractionDigits=this.valueScaling!==void 0&&this.options.valueScaling!==K.precision?this.valueScaling+this.currencyFormat.maximumFractionDigits:this.currencyFormat.maximumFractionDigits,this.minValue=this.getMinValue(),this.maxValue=this.getMaxValue()}getMinValue(){var e,n;let r=this.toFloat(-Number.MAX_SAFE_INTEGER);return((e=this.options.valueRange)===null||e===void 0?void 0:e.min)!==void 0&&(r=Math.max((n=this.options.valueRange)===null||n===void 0?void 0:n.min,this.toFloat(-Number.MAX_SAFE_INTEGER))),r}getMaxValue(){var e,n;let r=this.toFloat(Number.MAX_SAFE_INTEGER);return((e=this.options.valueRange)===null||e===void 0?void 0:e.max)!==void 0&&(r=Math.min((n=this.options.valueRange)===null||n===void 0?void 0:n.max,this.toFloat(Number.MAX_SAFE_INTEGER))),r}toFloat(e,n){return e/Math.pow(10,n??this.valueScalingFractionDigits)}toInteger(e,n){return Number(e.toFixed(n??this.valueScalingFractionDigits).split(".").join(""))}validateValueRange(e){return e!=null?Math.min(Math.max(e,this.minValue),this.maxValue):e}format(e,n=!1){if(e!=null){this.decimalSymbolInsertedAt!==void 0&&(e=this.currencyFormat.normalizeDecimalSeparator(e,this.decimalSymbolInsertedAt),this.decimalSymbolInsertedAt=void 0);const r=this.numberMask.conformToMask(e,this.formattedValue);let a;if(typeof r=="object"){const{numberValue:s,fractionDigits:o}=r;let{maximumFractionDigits:i,minimumFractionDigits:l}=this.currencyFormat;this.focus?l=n?o.replace(/0+$/,"").length:Math.min(i,o.length):Number.isInteger(s)&&!this.options.autoDecimalDigits&&(this.options.precision===void 0||l===0)&&(l=i=0),a=this.toInteger(Math.abs(s))>Number.MAX_SAFE_INTEGER?this.formattedValue:this.currencyFormat.format(s,{useGrouping:this.options.useGrouping!==!1&&!(this.focus&&this.options.hideGroupingSeparatorOnFocus),minimumFractionDigits:l,maximumFractionDigits:i})}else a=r;this.maxValue<=0&&!this.currencyFormat.isNegative(a)&&this.currencyFormat.parse(a)!==0&&(a=a.replace(this.currencyFormat.prefix,this.currencyFormat.negativePrefix)),this.minValue>=0&&(a=a.replace(this.currencyFormat.negativePrefix,this.currencyFormat.prefix)),(this.options.currencyDisplay===te.hidden||this.focus&&this.options.hideCurrencySymbolOnFocus)&&(a=a.replace(this.currencyFormat.negativePrefix,this.currencyFormat.minusSign!==void 0?this.currencyFormat.minusSign:"(").replace(this.currencyFormat.negativeSuffix,this.currencyFormat.minusSign!==void 0?"":")").replace(this.currencyFormat.prefix,"").replace(this.currencyFormat.suffix,"")),this.el.value=a,this.numberValue=this.currencyFormat.parse(a)}else this.el.value="",this.numberValue=null;this.formattedValue=this.el.value,this.onInput(this.getValue())}addEventListener(){this.el.addEventListener("input",e=>{const{value:n,selectionStart:r}=this.el,a=e;if(r&&a.data&&er.includes(a.data)&&(this.decimalSymbolInsertedAt=r-1),this.format(n),this.focus&&r!=null){const s=()=>{const{prefix:o,suffix:i,decimalSymbol:l,maximumFractionDigits:u,groupingSymbol:c}=this.currencyFormat;let d=n.length-r;const m=this.formattedValue.length;if(this.currencyFormat.minusSign===void 0&&(n.startsWith("(")||n.startsWith("-"))&&!n.endsWith(")"))return m-this.currencyFormat.negativeSuffix.length>1?this.formattedValue.substring(r).length:1;if(this.formattedValue.substring(r,1)===c&&nt(this.formattedValue,c)===nt(n,c)+1)return m-d-1;if(m<d)return r;if(l!==void 0&&n.indexOf(l)!==-1){const f=n.indexOf(l)+1;if(Math.abs(m-n.length)>1&&r<=f)return this.formattedValue.indexOf(l)+1;!this.options.autoDecimalDigits&&r>f&&this.currencyFormat.onlyDigits(n.substring(f)).length-1===u&&(d-=1)}return this.options.hideCurrencySymbolOnFocus||this.options.currencyDisplay===te.hidden?m-d:Math.max(m-Math.max(d,i.length),o.length)};this.setCaretPosition(s())}}),this.el.addEventListener("focus",()=>{this.focus=!0,this.numberValueOnFocus=this.numberValue,setTimeout(()=>{const{value:e,selectionStart:n,selectionEnd:r}=this.el;if(this.format(e,this.options.hideNegligibleDecimalDigitsOnFocus),n!=null&&r!=null&&Math.abs(n-r)>0)this.setCaretPosition(0,this.el.value.length);else if(n!=null){const a=this.getCaretPositionOnFocus(e,n);this.setCaretPosition(a)}})}),this.el.addEventListener("blur",()=>{this.focus=!1,this.format(this.currencyFormat.format(this.validateValueRange(this.numberValue))),this.numberValueOnFocus!==this.numberValue&&this.onChange(this.getValue())})}getCaretPositionOnFocus(e,n){if(this.numberValue==null)return n;const{prefix:r,negativePrefix:a,suffix:s,negativeSuffix:o,groupingSymbol:i,currency:l}=this.currencyFormat,u=this.numberValue<0,c=u?a:r,d=c.length;if(this.options.hideCurrencySymbolOnFocus||this.options.currencyDisplay===te.hidden){if(u){if(n<=1)return 1;if(e.endsWith(")")&&n>e.indexOf(")"))return this.formattedValue.length-1}}else{const f=u?o.length:s.length;if(n>=e.length-f)return this.formattedValue.length-f;if(n<d)return d}let m=n;return this.options.hideCurrencySymbolOnFocus&&this.options.currencyDisplay!==te.hidden&&n>=d&&l!==void 0&&c.includes(l)&&(m-=d,u&&(m+=1)),this.options.hideGroupingSeparatorOnFocus&&i!==void 0&&(m-=nt(e.substring(0,n),i)),m}setCaretPosition(e,n=e){this.el.setSelectionRange(e,n)}}const Ma=t=>t!=null&&t.matches("input")?t:t==null?void 0:t.querySelector("input");function va(t,e){var n,r,a,s;let o;const i=z(null),l=z(null),u=z(null),c=Zr(),d=(c==null?void 0:c.emit)||((r=(n=c==null?void 0:c.proxy)===null||n===void 0?void 0:n.$emit)===null||r===void 0?void 0:r.bind(c==null?void 0:c.proxy)),m=(c==null?void 0:c.props)||((a=c==null?void 0:c.proxy)===null||a===void 0?void 0:a.$props),f=Wr.startsWith("3"),h=f&&((s=c==null?void 0:c.attrs.modelModifiers)===null||s===void 0?void 0:s.lazy),p=Oe(()=>m==null?void 0:m[f?"modelValue":"value"]),g=f?"update:modelValue":"input",b=h?"update:modelValue":"change";return Je(i,M=>{var v;if(M){const S=Ma((v=M==null?void 0:M.$el)!==null&&v!==void 0?v:M);S?(o=new ya({el:S,options:t,onInput:E=>{!h&&e!==!1&&p.value!==E.number&&(d==null||d(g,E.number)),u.value=E.number,l.value=E.formatted},onChange:E=>{d==null||d(b,E.number)}}),o.setValue(p.value)):console.error('No input element found. Please make sure that the "inputRef" template ref is properly assigned.')}else o=null}),{inputRef:i,numberValue:u,formattedValue:l,setValue:M=>o==null?void 0:o.setValue(M),setOptions:M=>o==null?void 0:o.setOptions(M)}}const wa=fe({__name:"Currency",props:["modelValue","options"],setup(t){const e=t,r={...{currency:"BRL",locale:"pt-BR",autoDecimalDigits:!0,hideCurrencySymbolOnFocus:!1,hideGroupingSeparatorOnFocus:!1,valueRange:{min:0}},...e==null?void 0:e.options};Je(()=>e.modelValue,o=>{s(o)});const{inputRef:a,setValue:s}=va(r);return(o,i)=>(C(),R("input",{ref_key:"inputRef",ref:a,type:"text",class:"input input-bordered"},null,512))}});/*! maska v2.1.11 | (c) Alexander Shabunevich | Released under the MIT license */var Sa=Object.defineProperty,xa=(t,e,n)=>e in t?Sa(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Me=(t,e,n)=>(xa(t,typeof e!="symbol"?e+"":e,n),n);const jt={"#":{pattern:/[0-9]/},"@":{pattern:/[a-zA-Z]/},"*":{pattern:/[a-zA-Z0-9]/}};class Ht{constructor(e={}){Me(this,"opts",{}),Me(this,"memo",new Map);const n={...e};if(n.tokens!=null){n.tokens=n.tokensReplace?{...n.tokens}:{...jt,...n.tokens};for(const r of Object.values(n.tokens))typeof r.pattern=="string"&&(r.pattern=new RegExp(r.pattern))}else n.tokens=jt;Array.isArray(n.mask)&&(n.mask.length>1?n.mask=[...n.mask].sort((r,a)=>r.length-a.length):n.mask=n.mask[0]??""),n.mask===""&&(n.mask=null),this.opts=n}masked(e){return this.process(e,this.findMask(e))}unmasked(e){return this.process(e,this.findMask(e),!1)}isEager(){return this.opts.eager===!0}isReversed(){return this.opts.reversed===!0}completed(e){const n=this.findMask(e);if(this.opts.mask==null||n==null)return!1;const r=this.process(e,n).length;return typeof this.opts.mask=="string"?r>=this.opts.mask.length:typeof this.opts.mask=="function"?r>=n.length:this.opts.mask.filter(a=>r>=a.length).length===this.opts.mask.length}findMask(e){const n=this.opts.mask;if(n==null)return null;if(typeof n=="string")return n;if(typeof n=="function")return n(e);const r=this.process(e,n.slice(-1).pop()??"",!1);return n.find(a=>this.process(e,a,!1).length>=r.length)??""}escapeMask(e){const n=[],r=[];return e.split("").forEach((a,s)=>{a==="!"&&e[s-1]!=="!"?r.push(s-r.length):n.push(a)}),{mask:n.join(""),escaped:r}}process(e,n,r=!0){if(n==null)return e;const a=`value=${e},mask=${n},masked=${r?1:0}`;if(this.memo.has(a))return this.memo.get(a);const{mask:s,escaped:o}=this.escapeMask(n),i=[],l=this.opts.tokens!=null?this.opts.tokens:{},u=this.isReversed()?-1:1,c=this.isReversed()?"unshift":"push",d=this.isReversed()?0:s.length-1,m=this.isReversed()?()=>g>-1&&b>-1:()=>g<s.length&&b<e.length,f=v=>!this.isReversed()&&v<=d||this.isReversed()&&v>=d;let h,p=-1,g=this.isReversed()?s.length-1:0,b=this.isReversed()?e.length-1:0,M=!1;for(;m();){const v=s.charAt(g),S=l[v],E=(S==null?void 0:S.transform)!=null?S.transform(e.charAt(b)):e.charAt(b);if(!o.includes(g)&&S!=null?(E.match(S.pattern)!=null?(i[c](E),S.repeated?(p===-1?p=g:g===d&&g!==p&&(g=p-u),d===p&&(g-=u)):S.multiple&&(M=!0,g-=u),g+=u):S.multiple?M&&(g+=u,b-=u,M=!1):E===h?h=void 0:S.optional&&(g+=u,b-=u),b+=u):(r&&!this.isEager()&&i[c](v),E===v&&!this.isEager()?b+=u:h=v,this.isEager()||(g+=u)),this.isEager())for(;f(g)&&(l[s.charAt(g)]==null||o.includes(g));)r?i[c](s.charAt(g)):s.charAt(g)===e.charAt(b)&&(b+=u),g+=u}return this.memo.set(a,i.join("")),this.memo.get(a)}}const nr=t=>JSON.parse(t.replaceAll("'",'"')),Gt=(t,e={})=>{const n={...e};return t.dataset.maska!=null&&t.dataset.maska!==""&&(n.mask=Ca(t.dataset.maska)),t.dataset.maskaEager!=null&&(n.eager=rt(t.dataset.maskaEager)),t.dataset.maskaReversed!=null&&(n.reversed=rt(t.dataset.maskaReversed)),t.dataset.maskaTokensReplace!=null&&(n.tokensReplace=rt(t.dataset.maskaTokensReplace)),t.dataset.maskaTokens!=null&&(n.tokens=ka(t.dataset.maskaTokens)),n},rt=t=>t!==""?!!JSON.parse(t):!0,Ca=t=>t.startsWith("[")&&t.endsWith("]")?nr(t):t,ka=t=>{if(t.startsWith("{")&&t.endsWith("}"))return nr(t);const e={};return t.split("|").forEach(n=>{const r=n.split(":");e[r[0]]={pattern:new RegExp(r[1]),optional:r[2]==="optional",multiple:r[2]==="multiple",repeated:r[2]==="repeated"}}),e};class _a{constructor(e,n={}){Me(this,"items",new Map),Me(this,"beforeinputEvent",r=>{const a=r.target,s=this.items.get(a);s.isEager()&&"inputType"in r&&r.inputType.startsWith("delete")&&s.unmasked(a.value).length<=1&&this.setMaskedValue(a,"")}),Me(this,"inputEvent",r=>{if(r instanceof CustomEvent&&r.type==="input"&&r.detail!=null&&typeof r.detail=="object"&&"masked"in r.detail)return;const a=r.target,s=this.items.get(a),o=a.value,i=a.selectionStart,l=a.selectionEnd;let u=o;if(s.isEager()){const c=s.masked(o),d=s.unmasked(o);d===""&&"data"in r&&r.data!=null?u=r.data:d!==s.unmasked(c)&&(u=d)}if(this.setMaskedValue(a,u),"inputType"in r&&(r.inputType.startsWith("delete")||i!=null&&i<o.length))try{a.setSelectionRange(i,l)}catch{}}),this.options=n,typeof e=="string"?this.init(Array.from(document.querySelectorAll(e)),this.getMaskOpts(n)):this.init("length"in e?Array.from(e):[e],this.getMaskOpts(n))}destroy(){for(const e of this.items.keys())e.removeEventListener("input",this.inputEvent),e.removeEventListener("beforeinput",this.beforeinputEvent);this.items.clear()}needUpdateOptions(e,n){const r=this.items.get(e),a=new Ht(Gt(e,this.getMaskOpts(n)));return JSON.stringify(r.opts)!==JSON.stringify(a.opts)}needUpdateValue(e){const n=e.dataset.maskaValue;return n==null&&e.value!==""||n!=null&&n!==e.value}getMaskOpts(e){const{onMaska:n,preProcess:r,postProcess:a,...s}=e;return s}init(e,n){for(const r of e){const a=new Ht(Gt(r,n));this.items.set(r,a),r.value!==""&&this.setMaskedValue(r,r.value),r.addEventListener("input",this.inputEvent),r.addEventListener("beforeinput",this.beforeinputEvent)}}setMaskedValue(e,n){const r=this.items.get(e);this.options.preProcess!=null&&(n=this.options.preProcess(n));const a=r.masked(n),s=r.unmasked(r.isEager()?a:n),o=r.completed(n),i={masked:a,unmasked:s,completed:o};n=a,this.options.postProcess!=null&&(n=this.options.postProcess(n)),e.value=n,e.dataset.maskaValue=n,this.options.onMaska!=null&&(Array.isArray(this.options.onMaska)?this.options.onMaska.forEach(l=>l(i)):this.options.onMaska(i)),e.dispatchEvent(new CustomEvent("maska",{detail:i})),e.dispatchEvent(new CustomEvent("input",{detail:i}))}}const bt=new WeakMap,Ra=t=>{setTimeout(()=>{var e;((e=bt.get(t))==null?void 0:e.needUpdateValue(t))===!0&&t.dispatchEvent(new CustomEvent("input"))})},Ea=(t,e)=>{const n=t instanceof HTMLInputElement?t:t.querySelector("input"),r={...e.arg};if(n==null||(n==null?void 0:n.type)==="file")return;Ra(n);const a=bt.get(n);if(a!=null){if(!a.needUpdateOptions(n,r))return;a.destroy()}if(e.value!=null){const s=e.value,o=i=>{s.masked=i.masked,s.unmasked=i.unmasked,s.completed=i.completed};r.onMaska=r.onMaska==null?o:Array.isArray(r.onMaska)?[...r.onMaska,o]:[r.onMaska,o]}bt.set(n,new _a(n,r))},$a={class:G("form-control w-full")},La={class:"relative"},Fa=["data-maska","type","disabled","readonly"],Na={key:0,class:"text-error text-xs"},Ia=fe({__name:"InputBase",props:He({label:String,labelStyle:String,classes:String,labelBg:String,type:{type:String,default:"text"},size:{type:String,default:"md"},value:{type:[String,Number]},error:{default:void 0,type:String},placeholder:{type:String,default:""},color:{type:String,default:"base-content"},bgColor:{type:String,default:"base-100"},bordered:{type:Boolean,default:!0},min:{type:[String,Number]},max:{type:[String,Number]},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},isCurrency:{type:Boolean},options:{type:Array},itemsSelected:{type:Object},itemsToSelected:{type:Object},dataMaska:{type:String},unmasked:{type:String},dataMaskaReversed:{type:Boolean},inputClasses:{type:String},optionLabel:{type:String,default:void 0},optionValue:{type:String,default:void 0}},{modelValue:{},modelModifiers:{}}),emits:He(["update:modelValue","update:selected"],["update:modelValue"]),setup(t,{emit:e}){const n=Xn(t,"modelValue"),r=t,a=Oe(()=>`${s.value?"select":"input"}-${r.size} `),s=Oe(()=>{var u;return!!((u=r.options)!=null&&u.length)});Oe(()=>{const u={...r};return o(u)});function o(u){const c={};for(const d in u)if(u.hasOwnProperty(d)){const m=d.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();c[m]=u[d]}return c}const i=z(n.value);function l(u){n.value=u.detail.unmasked}return Je(n,u=>{(!r.dataMaska||!i.value)&&(i.value=u)}),(u,c)=>{const d=wa;return C(),R("div",$a,[$("label",{class:G([t.error?"text-error":`label-text text-base-content ${t.labelStyle} mb-1`,"text-sm"])},[$("div",{style:{display:"inline"},class:G(t.labelBg)},Z(t.label),3)],2),$("div",La,[t.isCurrency?(C(),le(d,{key:0,modelValue:n.value,"onUpdate:modelValue":c[0]||(c[0]=m=>n.value=m)},null,8,["modelValue"])):ze((C(),R("input",{key:1,"data-maska":t.dataMaska,onMaska:l,"onUpdate:modelValue":c[1]||(c[1]=m=>qr(i)?i.value=m:null),class:G(["input w-full bordered",[t.error?"input-error ":`${t.color} ${t.bgColor}`,t.bordered&&"input-bordered",oe(a),t.inputClasses]]),type:t.type,disabled:t.disabled,readonly:t.readonly},null,42,Fa)),[[oe(Ea)],[Jr,oe(i)]]),_t(u.$slots,"append")]),t.error?(C(),R("div",Na,Z(t.error),1)):we("",!0)])}}}),Da={class:"px-2 pt-2 flex flex-col gap-4 w-full h-full overflow-y-auto"},Pa={key:0,class:"text-center py-4"},Va={key:1,class:"flex justify-center py-4"},Aa={class:"font-bold mb-3 text-lg"},Ba={key:1,class:"form-control w-full"},Oa={class:"label cursor-pointer justify-start gap-4"},za=["onUpdate:modelValue"],Ta={class:"label-text"},ja=["onUpdate:modelValue"],Ha=["onUpdate:modelValue"],Ga=["value"],Ya={class:"flex justify-end gap-2 mt-4 mb-4"},Xa=fe({__name:"QuestionModal",props:He({slug:{type:String,required:!0},title:{type:String,default:"Questionário"},professionalId:{type:Number,default:null}},{modelValue:{},modelModifiers:{}}),emits:He(["submitted"],["update:modelValue"]),setup(t,{emit:e}){const n=t,r=Xn(t,"modelValue"),a=e,s=Zn.useToast(),o=z([]),i=z({}),l=z(!1),u=z(!1),c=async()=>{l.value=!0;try{const m=n.professionalId?{professional_id:n.professionalId}:{},{data:f}=await pt.get(`/${n.slug}/questions`,{params:m});o.value=f,f.forEach(h=>{h.type==="multiple"?i.value[h.id]="":h.type==="switch"?i.value[h.id]=!1:h.type==="checkbox"?i.value[h.id]=[]:i.value[h.id]=""})}catch(m){console.error("Error fetching questions:",m),s.error("Erro ao carregar as perguntas")}finally{l.value=!1}},d=async()=>{u.value=!0;try{for(const m of Object.keys(i.value)){const f=o.value.find(p=>p.id===parseInt(m));if(!f)continue;const h=typeof i.value[parseInt(m)]=="object"?JSON.stringify(i.value[parseInt(m)]):String(i.value[parseInt(m)]);await pt.post("/answers",{question_text:f.label,answer:h,company_slug:n.slug})}a("submitted"),r.value=!1}catch(m){console.error("Error submitting answers:",m),s.error("Erro ao enviar as respostas")}finally{u.value=!1}};return Je(()=>r.value,m=>{m&&c()}),Wn(()=>{r.value&&c()}),(m,f)=>{const h=Ia,p=qn,g=Qn;return C(),le(g,{loading:l.value,"hide-close":"",title:t.title,modelValue:r.value,"onUpdate:modelValue":f[1]||(f[1]=b=>r.value=b),class:"modal-fullscreen"},{default:ue(()=>[$("div",Da,[o.value.length===0&&!l.value?(C(),R("div",Pa," Nenhuma pergunta encontrada. ")):we("",!0),l.value?(C(),R("div",Va,f[2]||(f[2]=[$("div",{class:"loading loading-spinner loading-lg"},null,-1)]))):(C(!0),R(Ge,{key:2},ft(o.value,(b,M)=>(C(),R("div",{key:M,class:"mb-6 w-full"},[$("div",Aa,Z(b.label),1),b.type==="string"?(C(),le(h,{key:0,modelValue:i.value[b.id],"onUpdate:modelValue":v=>i.value[b.id]=v,placeholder:"Digite sua resposta",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"])):b.type==="switch"?(C(),R("div",Ba,[$("label",Oa,[ze($("input",{type:"checkbox",class:"toggle toggle-primary","onUpdate:modelValue":v=>i.value[b.id]=v},null,8,za),[[Ur,i.value[b.id]]]),$("span",Ta,Z(i.value[b.id]?"Sim":"Não"),1)])])):b.type==="textarea"?ze((C(),R("textarea",{key:2,"onUpdate:modelValue":v=>i.value[b.id]=v,class:"textarea textarea-bordered w-full",placeholder:"Digite sua resposta",rows:"4"},null,8,ja)),[[Qr,i.value[b.id]]]):b.type==="multiple"?ze((C(),R("select",{key:3,class:"select select-bordered w-full","onUpdate:modelValue":v=>i.value[b.id]=v},[f[3]||(f[3]=$("option",{disabled:"",value:""},"Selecione uma opção",-1)),(C(!0),R(Ge,null,ft(b.options,(v,S)=>(C(),R("option",{key:S,value:v},Z(v),9,Ga))),128))],8,Ha)),[[Kr,i.value[b.id]]]):(C(),le(h,{key:4,modelValue:i.value[b.id],"onUpdate:modelValue":v=>i.value[b.id]=v,placeholder:"Digite sua resposta",class:"w-full"},null,8,["modelValue","onUpdate:modelValue"]))]))),128)),$("div",Ya,[X(p,{onClick:f[0]||(f[0]=b=>r.value=!1),color:"ghost"},{default:ue(()=>f[4]||(f[4]=[gt(" Cancelar ")])),_:1}),X(p,{onClick:d,color:"primary",loading:u.value},{default:ue(()=>f[5]||(f[5]=[gt(" Enviar ")])),_:1},8,["loading"])])])]),_:1},8,["loading","title","modelValue"])}}}),Za=Jn(Xa,[["__scopeId","data-v-34541652"]]),Wa={class:"btm-nav flex md:hidden h-[64px] fixed bottom-0 left-0 right-0 z-50"},Ja=["onClick"],qa=fe({__name:"BottomNavigation",props:{slug:{type:String},company:{type:Object}},setup(t){Un();const e=[{to:`/${t.slug}`,label:"Início",icon:ra,name:"index"},{to:`/${t.slug}/servicos`,label:"Agendar",icon:oa,name:"servicos"},{to:`/${t.slug}/pedidos`,label:"Historico",icon:la,name:"pedidos"},{to:`/${t.slug}/perfil`,label:"Perfil",icon:aa,name:"perfil"}];return(n,r)=>(C(),R("div",Wa,[(C(),R(Ge,null,ft(e,({icon:a,to:s,label:o,name:i})=>$("button",{key:s,class:G([i===n.route().current()?"active bg-primary border-primary-content text-primary-content":"bg-base-200","relative"]),onClick:l=>oe(ta).get(s)},[(C(),le(ea(a),{class:G([i===n.route().current()?"fill-primary-content":"","w-5 h-5"])},null,8,["class"])),$("span",{class:G([i===n.route().current()?"text-primary-content":"","text-xs"])},Z(o),3)],10,Ja)),64))]))}});function _(t,e){let n=t.length;Array.isArray(t[0])||(t=[t]),Array.isArray(e[0])||(e=e.map(o=>[o]));let r=e[0].length,a=e[0].map((o,i)=>e.map(l=>l[i])),s=t.map(o=>a.map(i=>{let l=0;if(!Array.isArray(o)){for(let u of i)l+=o*u;return l}for(let u=0;u<o.length;u++)l+=o[u]*(i[u]||0);return l}));return n===1&&(s=s[0]),r===1?s.map(o=>o[0]):s}function ke(t){return W(t)==="string"}function W(t){return(Object.prototype.toString.call(t).match(/^\[object\s+(.*?)\]$/)[1]||"").toLowerCase()}function Ye(t,{precision:e,unit:n}){return J(t)?"none":rr(t,e)+(n??"")}function J(t){return Number.isNaN(t)||t instanceof Number&&(t==null?void 0:t.none)}function L(t){return J(t)?0:t}function rr(t,e){if(t===0)return 0;let n=~~t,r=0;n&&e&&(r=~~Math.log10(Math.abs(n))+1);const a=10**(e-r);return Math.floor(t*a+.5)/a}const Ua={deg:1,grad:.9,rad:180/Math.PI,turn:360};function ar(t){if(!t)return;t=t.trim();const e=/^([a-z]+)\((.+?)\)$/i,n=/^-?[\d.]+$/,r=/%|deg|g?rad|turn$/,a=/\/?\s*(none|[-\w.]+(?:%|deg|g?rad|turn)?)/g;let s=t.match(e);if(s){let o=[];return s[2].replace(a,(i,l)=>{let u=l.match(r),c=l;if(u){let d=u[0],m=c.slice(0,-d.length);d==="%"?(c=new Number(m/100),c.type="<percentage>"):(c=new Number(m*Ua[d]),c.type="<angle>",c.unit=d)}else n.test(c)?(c=new Number(c),c.type="<number>"):c==="none"&&(c=new Number(NaN),c.none=!0);i.startsWith("/")&&(c=c instanceof Number?c:new Number(c),c.alpha=!0),typeof c=="object"&&c instanceof Number&&(c.raw=l),o.push(c)}),{name:s[1].toLowerCase(),rawName:s[1],rawArgs:s[2],args:o}}}function sr(t){return t[t.length-1]}function Se(t,e,n){return isNaN(t)?e:isNaN(e)?t:t+(e-t)*n}function ir(t,e,n){return(n-t)/(e-t)}function Rt(t,e,n){return Se(e[0],e[1],ir(t[0],t[1],n))}function or(t){return t.map(e=>e.split("|").map(n=>{n=n.trim();let r=n.match(/^(<[a-z]+>)\[(-?[.\d]+),\s*(-?[.\d]+)\]?$/);if(r){let a=new String(r[1]);return a.range=[+r[2],+r[3]],a}return n}))}function lr(t,e,n){return Math.max(Math.min(n,e),t)}function qe(t,e){return Math.sign(t)===Math.sign(e)?t:-t}function T(t,e){return qe(Math.abs(t)**e,t)}function Et(t,e){return e===0?0:t/e}function ur(t,e,n=0,r=t.length){for(;n<r;){const a=n+r>>1;t[a]<e?n=a+1:r=a}return n}var Qa=Object.freeze({__proto__:null,bisectLeft:ur,clamp:lr,copySign:qe,interpolate:Se,interpolateInv:ir,isNone:J,isString:ke,last:sr,mapRange:Rt,multiplyMatrices:_,parseCoordGrammar:or,parseFunction:ar,serializeNumber:Ye,skipNone:L,spow:T,toPrecision:rr,type:W,zdiv:Et});class Ka{add(e,n,r){if(typeof arguments[0]!="string"){for(var e in arguments[0])this.add(e,arguments[0][e],arguments[1]);return}(Array.isArray(e)?e:[e]).forEach(function(a){this[a]=this[a]||[],n&&this[a][r?"unshift":"push"](n)},this)}run(e,n){this[e]=this[e]||[],this[e].forEach(function(r){r.call(n&&n.context?n.context:n,n)})}}const q=new Ka;var Hn,Gn,Yn,B={gamut_mapping:"css",precision:5,deltaE:"76",verbose:((Yn=(Gn=(Hn=globalThis==null?void 0:globalThis.process)==null?void 0:Hn.env)==null?void 0:Gn.NODE_ENV)==null?void 0:Yn.toLowerCase())!=="test",warn:function(e){var n,r;this.verbose&&((r=(n=globalThis==null?void 0:globalThis.console)==null?void 0:n.warn)==null||r.call(n,e))}};const I={D50:[.3457/.3585,1,(1-.3457-.3585)/.3585],D65:[.3127/.329,1,(1-.3127-.329)/.329]};function yt(t){return Array.isArray(t)?t:I[t]}function Xe(t,e,n,r={}){if(t=yt(t),e=yt(e),!t||!e)throw new TypeError(`Missing white point to convert ${t?"":"from"}${!t&&!e?"/":""}${e?"":"to"}`);if(t===e)return n;let a={W1:t,W2:e,XYZ:n,options:r};if(q.run("chromatic-adaptation-start",a),a.M||(a.W1===I.D65&&a.W2===I.D50?a.M=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]]:a.W1===I.D50&&a.W2===I.D65&&(a.M=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]])),q.run("chromatic-adaptation-end",a),a.M)return _(a.M,a.XYZ);throw new TypeError("Only Bradford CAT with white points D50 and D65 supported for now.")}const es=new Set(["<number>","<percentage>","<angle>"]);function Yt(t,e,n,r){return Object.entries(t.coords).map(([s,o],i)=>{let l=e.coordGrammar[i],u=r[i],c=u==null?void 0:u.type,d;if(u.none?d=l.find(h=>es.has(h)):d=l.find(h=>h==c),!d){let h=o.name||s;throw new TypeError(`${c??u.raw} not allowed for ${h} in ${n}()`)}let m=d.range;c==="<percentage>"&&(m||(m=[0,1]));let f=o.range||o.refRange;return m&&f&&(r[i]=Rt(m,f,r[i])),d})}function cr(t,{meta:e}={}){var r,a,s,o;let n={str:(r=String(t))==null?void 0:r.trim()};if(q.run("parse-start",n),n.color)return n.color;if(n.parsed=ar(n.str),n.parsed){let i=n.parsed.name;if(i==="color"){let l=n.parsed.args.shift(),u=l.startsWith("--")?l.substring(2):`--${l}`,c=[l,u],d=n.parsed.rawArgs.indexOf("/")>0?n.parsed.args.pop():1;for(let h of y.all){let p=h.getFormat("color");if(p&&(c.includes(p.id)||(a=p.ids)!=null&&a.filter(g=>c.includes(g)).length)){const g=Object.keys(h.coords).map((M,v)=>n.parsed.args[v]||0);let b;return p.coordGrammar&&(b=Yt(h,p,"color",g)),e&&Object.assign(e,{formatId:"color",types:b}),p.id.startsWith("--")&&!l.startsWith("--")&&B.warn(`${h.name} is a non-standard space and not currently supported in the CSS spec. Use prefixed color(${p.id}) instead of color(${l}).`),l.startsWith("--")&&!p.id.startsWith("--")&&B.warn(`${h.name} is a standard space and supported in the CSS spec. Use color(${p.id}) instead of prefixed color(${l}).`),{spaceId:h.id,coords:g,alpha:d}}}let m="",f=l in y.registry?l:u;if(f in y.registry){let h=(o=(s=y.registry[f].formats)==null?void 0:s.color)==null?void 0:o.id;h&&(m=`Did you mean color(${h})?`)}throw new TypeError(`Cannot parse color(${l}). `+(m||"Missing a plugin?"))}else for(let l of y.all){let u=l.getFormat(i);if(u&&u.type==="function"){let c=1;(u.lastAlpha||sr(n.parsed.args).alpha)&&(c=n.parsed.args.pop());let d=n.parsed.args,m;return u.coordGrammar&&(m=Yt(l,u,i,d)),e&&Object.assign(e,{formatId:u.name,types:m}),{spaceId:l.id,coords:d,alpha:c}}}}else for(let i of y.all)for(let l in i.formats){let u=i.formats[l];if(u.type!=="custom"||u.test&&!u.test(n.str))continue;let c=u.parse(n.str);if(c)return c.alpha??(c.alpha=1),e&&(e.formatId=l),c}throw new TypeError(`Could not parse ${t} as a color. Missing a plugin?`)}function w(t){if(Array.isArray(t))return t.map(w);if(!t)throw new TypeError("Empty color reference");ke(t)&&(t=cr(t));let e=t.space||t.spaceId;return e instanceof y||(t.space=y.get(e)),t.alpha===void 0&&(t.alpha=1),t}const ts=75e-6,D=class D{constructor(e){var a;this.id=e.id,this.name=e.name,this.base=e.base?D.get(e.base):null,this.aliases=e.aliases,this.base&&(this.fromBase=e.fromBase,this.toBase=e.toBase);let n=e.coords??this.base.coords;for(let s in n)"name"in n[s]||(n[s].name=s);this.coords=n;let r=e.white??this.base.white??"D65";this.white=yt(r),this.formats=e.formats??{};for(let s in this.formats){let o=this.formats[s];o.type||(o.type="function"),o.name||(o.name=s)}(a=this.formats.color)!=null&&a.id||(this.formats.color={...this.formats.color??{},id:e.cssId||this.id}),e.gamutSpace?this.gamutSpace=e.gamutSpace==="self"?this:D.get(e.gamutSpace):this.isPolar?this.gamutSpace=this.base:this.gamutSpace=this,this.gamutSpace.isUnbounded&&(this.inGamut=(s,o)=>!0),this.referred=e.referred,Object.defineProperty(this,"path",{value:ns(this).reverse(),writable:!1,enumerable:!0,configurable:!0}),q.run("colorspace-init-end",this)}inGamut(e,{epsilon:n=ts}={}){if(!this.equals(this.gamutSpace))return e=this.to(this.gamutSpace,e),this.gamutSpace.inGamut(e,{epsilon:n});let r=Object.values(this.coords);return e.every((a,s)=>{let o=r[s];if(o.type!=="angle"&&o.range){if(Number.isNaN(a))return!0;let[i,l]=o.range;return(i===void 0||a>=i-n)&&(l===void 0||a<=l+n)}return!0})}get isUnbounded(){return Object.values(this.coords).every(e=>!("range"in e))}get cssId(){var e,n;return((n=(e=this.formats)==null?void 0:e.color)==null?void 0:n.id)||this.id}get isPolar(){for(let e in this.coords)if(this.coords[e].type==="angle")return!0;return!1}getFormat(e){if(typeof e=="object")return e=Xt(e,this),e;let n;return e==="default"?n=Object.values(this.formats)[0]:n=this.formats[e],n?(n=Xt(n,this),n):null}equals(e){return e?this===e||this.id===e||this.id===e.id:!1}to(e,n){if(arguments.length===1){const i=w(e);[e,n]=[i.space,i.coords]}if(e=D.get(e),this.equals(e))return n;n=n.map(i=>Number.isNaN(i)?0:i);let r=this.path,a=e.path,s,o;for(let i=0;i<r.length&&r[i].equals(a[i]);i++)s=r[i],o=i;if(!s)throw new Error(`Cannot convert between color spaces ${this} and ${e}: no connection space was found`);for(let i=r.length-1;i>o;i--)n=r[i].toBase(n);for(let i=o+1;i<a.length;i++)n=a[i].fromBase(n);return n}from(e,n){if(arguments.length===1){const r=w(e);[e,n]=[r.space,r.coords]}return e=D.get(e),e.to(this,n)}toString(){return`${this.name} (${this.id})`}getMinCoords(){let e=[];for(let n in this.coords){let r=this.coords[n],a=r.range||r.refRange;e.push((a==null?void 0:a.min)??0)}return e}static get all(){return[...new Set(Object.values(D.registry))]}static register(e,n){if(arguments.length===1&&(n=arguments[0],e=n.id),n=this.get(n),this.registry[e]&&this.registry[e]!==n)throw new Error(`Duplicate color space registration: '${e}'`);if(this.registry[e]=n,arguments.length===1&&n.aliases)for(let r of n.aliases)this.register(r,n);return n}static get(e,...n){if(!e||e instanceof D)return e;if(W(e)==="string"){let a=D.registry[e.toLowerCase()];if(!a)throw new TypeError(`No color space found with id = "${e}"`);return a}if(n.length)return D.get(...n);throw new TypeError(`${e} is not a valid color space`)}static resolveCoord(e,n){var l;let r=W(e),a,s;if(r==="string"?e.includes(".")?[a,s]=e.split("."):[a,s]=[,e]:Array.isArray(e)?[a,s]=e:(a=e.space,s=e.coordId),a=D.get(a),a||(a=n),!a)throw new TypeError(`Cannot resolve coordinate reference ${e}: No color space specified and relative references are not allowed here`);if(r=W(s),r==="number"||r==="string"&&s>=0){let u=Object.entries(a.coords)[s];if(u)return{space:a,id:u[0],index:s,...u[1]}}a=D.get(a);let o=s.toLowerCase(),i=0;for(let u in a.coords){let c=a.coords[u];if(u.toLowerCase()===o||((l=c.name)==null?void 0:l.toLowerCase())===o)return{space:a,id:u,index:i,...c};i++}throw new TypeError(`No "${s}" coordinate found in ${a.name}. Its coordinates are: ${Object.keys(a.coords).join(", ")}`)}};tt(D,"registry",{}),tt(D,"DEFAULT_FORMAT",{type:"functions",name:"color"});let y=D;function ns(t){let e=[t];for(let n=t;n=n.base;)e.push(n);return e}function Xt(t,{coords:e}={}){if(t.coords&&!t.coordGrammar){t.type||(t.type="function"),t.name||(t.name="color"),t.coordGrammar=or(t.coords);let n=Object.entries(e).map(([r,a],s)=>{let o=t.coordGrammar[s][0],i=a.range||a.refRange,l=o.range,u="";return o=="<percentage>"?(l=[0,100],u="%"):o=="<angle>"&&(u="deg"),{fromRange:i,toRange:l,suffix:u}});t.serializeCoords=(r,a)=>r.map((s,o)=>{let{fromRange:i,toRange:l,suffix:u}=n[o];return i&&l&&(s=Rt(i,l,s)),s=Ye(s,{precision:a,unit:u}),s})}return t}var N=new y({id:"xyz-d65",name:"XYZ D65",coords:{x:{name:"X"},y:{name:"Y"},z:{name:"Z"}},white:"D65",formats:{color:{ids:["xyz-d65","xyz"]}},aliases:["xyz"]});class P extends y{constructor(e){e.coords||(e.coords={r:{range:[0,1],name:"Red"},g:{range:[0,1],name:"Green"},b:{range:[0,1],name:"Blue"}}),e.base||(e.base=N),e.toXYZ_M&&e.fromXYZ_M&&(e.toBase??(e.toBase=n=>{let r=_(e.toXYZ_M,n);return this.white!==this.base.white&&(r=Xe(this.white,this.base.white,r)),r}),e.fromBase??(e.fromBase=n=>(n=Xe(this.base.white,this.white,n),_(e.fromXYZ_M,n)))),e.referred??(e.referred="display"),super(e)}}function _e(t,e){return t=w(t),!e||t.space.equals(e)?t.coords.slice():(e=y.get(e),e.from(t))}function V(t,e){t=w(t);let{space:n,index:r}=y.resolveCoord(e,t.space);return _e(t,n)[r]}function $t(t,e,n){return t=w(t),e=y.get(e),t.coords=e.to(t.space,n),t}$t.returns="color";function Y(t,e,n){if(t=w(t),arguments.length===2&&W(arguments[1])==="object"){let r=arguments[1];for(let a in r)Y(t,a,r[a])}else{typeof n=="function"&&(n=n(V(t,e)));let{space:r,index:a}=y.resolveCoord(e,t.space),s=_e(t,r);s[a]=n,$t(t,r,s)}return t}Y.returns="color";var Lt=new y({id:"xyz-d50",name:"XYZ D50",white:"D50",base:N,fromBase:t=>Xe(N.white,"D50",t),toBase:t=>Xe("D50",N.white,t)});const rs=216/24389,Zt=24/116,Fe=24389/27;let at=I.D50;var A=new y({id:"lab",name:"Lab",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:at,base:Lt,fromBase(t){let n=t.map((r,a)=>r/at[a]).map(r=>r>rs?Math.cbrt(r):(Fe*r+16)/116);return[116*n[1]-16,500*(n[0]-n[1]),200*(n[1]-n[2])]},toBase(t){let e=[];return e[1]=(t[0]+16)/116,e[0]=t[1]/500+e[1],e[2]=e[1]-t[2]/200,[e[0]>Zt?Math.pow(e[0],3):(116*e[0]-16)/Fe,t[0]>8?Math.pow((t[0]+16)/116,3):t[0]/Fe,e[2]>Zt?Math.pow(e[2],3):(116*e[2]-16)/Fe].map((r,a)=>r*at[a])},formats:{lab:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function j(t){return(t%360+360)%360}function as(t,e){if(t==="raw")return e;let[n,r]=e.map(j),a=r-n;return t==="increasing"?a<0&&(r+=360):t==="decreasing"?a>0&&(n+=360):t==="longer"?-180<a&&a<180&&(a>0?n+=360:r+=360):t==="shorter"&&(a>180?n+=360:a<-180&&(r+=360)),[n,r]}var xe=new y({id:"lch",name:"LCH",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,150],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:A,fromBase(t){let[e,n,r]=t,a;const s=.02;return Math.abs(n)<s&&Math.abs(r)<s?a=NaN:a=Math.atan2(r,n)*180/Math.PI,[e,Math.sqrt(n**2+r**2),j(a)]},toBase(t){let[e,n,r]=t;return n<0&&(n=0),isNaN(r)&&(r=0),[e,n*Math.cos(r*Math.PI/180),n*Math.sin(r*Math.PI/180)]},formats:{lch:{coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const Wt=25**7,Ze=Math.PI,Jt=180/Ze,re=Ze/180;function qt(t){const e=t*t;return e*e*e*t}function dr(t,e,{kL:n=1,kC:r=1,kH:a=1}={}){[t,e]=w([t,e]);let[s,o,i]=A.from(t),l=xe.from(A,[s,o,i])[1],[u,c,d]=A.from(e),m=xe.from(A,[u,c,d])[1];l<0&&(l=0),m<0&&(m=0);let f=(l+m)/2,h=qt(f),p=.5*(1-Math.sqrt(h/(h+Wt))),g=(1+p)*o,b=(1+p)*c,M=Math.sqrt(g**2+i**2),v=Math.sqrt(b**2+d**2),S=g===0&&i===0?0:Math.atan2(i,g),E=b===0&&d===0?0:Math.atan2(d,b);S<0&&(S+=2*Ze),E<0&&(E+=2*Ze),S*=Jt,E*=Jt;let Ee=u-s,$e=v-M,O=E-S,ge=S+E,Pt=Math.abs(O),pe;M*v===0?pe=0:Pt<=180?pe=O:O>180?pe=O-360:O<-180?pe=O+360:B.warn("the unthinkable has happened");let Vt=2*Math.sqrt(v*M)*Math.sin(pe*re/2),zr=(s+u)/2,et=(M+v)/2,At=qt(et),H;M*v===0?H=ge:Pt<=180?H=ge/2:ge<360?H=(ge+360)/2:H=(ge-360)/2;let Bt=(zr-50)**2,Tr=1+.015*Bt/Math.sqrt(20+Bt),Ot=1+.045*et,be=1;be-=.17*Math.cos((H-30)*re),be+=.24*Math.cos(2*H*re),be+=.32*Math.cos((3*H+6)*re),be-=.2*Math.cos((4*H-63)*re);let zt=1+.015*et*be,jr=30*Math.exp(-1*((H-275)/25)**2),Hr=2*Math.sqrt(At/(At+Wt)),Gr=-1*Math.sin(2*jr*re)*Hr,Le=(Ee/(n*Tr))**2;return Le+=($e/(r*Ot))**2,Le+=(Vt/(a*zt))**2,Le+=Gr*($e/(r*Ot))*(Vt/(a*zt)),Math.sqrt(Le)}const ss=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],is=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],os=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],ls=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]];var ce=new y({id:"oklab",name:"Oklab",coords:{l:{refRange:[0,1],name:"Lightness"},a:{refRange:[-.4,.4]},b:{refRange:[-.4,.4]}},white:"D65",base:N,fromBase(t){let n=_(ss,t).map(r=>Math.cbrt(r));return _(os,n)},toBase(t){let n=_(ls,t).map(r=>r**3);return _(is,n)},formats:{oklab:{coords:["<percentage> | <number>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function Mt(t,e){[t,e]=w([t,e]);let[n,r,a]=ce.from(t),[s,o,i]=ce.from(e),l=n-s,u=r-o,c=a-i;return Math.sqrt(l**2+u**2+c**2)}const us=75e-6;function ne(t,e,{epsilon:n=us}={}){t=w(t),e||(e=t.space),e=y.get(e);let r=t.coords;return e!==t.space&&(r=e.from(t)),e.inGamut(r,{epsilon:n})}function de(t){return{space:t.space,coords:t.coords.slice(),alpha:t.alpha}}function mr(t,e,n="lab"){n=y.get(n);let r=n.from(t),a=n.from(e);return Math.sqrt(r.reduce((s,o,i)=>{let l=a[i];return isNaN(o)||isNaN(l)?s:s+(l-o)**2},0))}function cs(t,e){return mr(t,e,"lab")}const ds=Math.PI,Ut=ds/180;function ms(t,e,{l:n=2,c:r=1}={}){[t,e]=w([t,e]);let[a,s,o]=A.from(t),[,i,l]=xe.from(A,[a,s,o]),[u,c,d]=A.from(e),m=xe.from(A,[u,c,d])[1];i<0&&(i=0),m<0&&(m=0);let f=a-u,h=i-m,p=s-c,g=o-d,b=p**2+g**2-h**2,M=.511;a>=16&&(M=.040975*a/(1+.01765*a));let v=.0638*i/(1+.0131*i)+.638,S;Number.isNaN(l)&&(l=0),l>=164&&l<=345?S=.56+Math.abs(.2*Math.cos((l+168)*Ut)):S=.36+Math.abs(.4*Math.cos((l+35)*Ut));let E=Math.pow(i,4),Ee=Math.sqrt(E/(E+1900)),$e=v*(Ee*S+1-Ee),O=(f/(n*M))**2;return O+=(h/(r*v))**2,O+=b/$e**2,Math.sqrt(O)}const Qt=203;var Ft=new y({id:"xyz-abs-d65",cssId:"--xyz-abs-d65",name:"Absolute XYZ D65",coords:{x:{refRange:[0,9504.7],name:"Xa"},y:{refRange:[0,1e4],name:"Ya"},z:{refRange:[0,10888.3],name:"Za"}},base:N,fromBase(t){return t.map(e=>Math.max(e*Qt,0))},toBase(t){return t.map(e=>Math.max(e/Qt,0))}});const Ne=1.15,Ie=.66,Kt=2610/2**14,hs=2**14/2610,en=3424/2**12,tn=2413/2**7,nn=2392/2**7,fs=1.7*2523/2**5,rn=2**5/(1.7*2523),De=-.56,st=16295499532821565e-27,gs=[[.41478972,.579999,.014648],[-.20151,1.120649,.0531008],[-.0166008,.2648,.6684799]],ps=[[1.9242264357876067,-1.0047923125953657,.037651404030618],[.35031676209499907,.7264811939316552,-.06538442294808501],[-.09098281098284752,-.3127282905230739,1.5227665613052603]],bs=[[.5,.5,0],[3.524,-4.066708,.542708],[.199076,1.096799,-1.295875]],ys=[[1,.1386050432715393,.05804731615611886],[.9999999999999999,-.1386050432715393,-.05804731615611886],[.9999999999999998,-.09601924202631895,-.8118918960560388]];var hr=new y({id:"jzazbz",name:"Jzazbz",coords:{jz:{refRange:[0,1],name:"Jz"},az:{refRange:[-.5,.5]},bz:{refRange:[-.5,.5]}},base:Ft,fromBase(t){let[e,n,r]=t,a=Ne*e-(Ne-1)*r,s=Ie*n-(Ie-1)*e,i=_(gs,[a,s,r]).map(function(m){let f=en+tn*(m/1e4)**Kt,h=1+nn*(m/1e4)**Kt;return(f/h)**fs}),[l,u,c]=_(bs,i);return[(1+De)*l/(1+De*l)-st,u,c]},toBase(t){let[e,n,r]=t,a=(e+st)/(1+De-De*(e+st)),o=_(ys,[a,n,r]).map(function(m){let f=en-m**rn,h=nn*m**rn-tn;return 1e4*(f/h)**hs}),[i,l,u]=_(ps,o),c=(i+(Ne-1)*u)/Ne,d=(l+(Ie-1)*c)/Ie;return[c,d,u]},formats:{color:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),vt=new y({id:"jzczhz",name:"JzCzHz",coords:{jz:{refRange:[0,1],name:"Jz"},cz:{refRange:[0,1],name:"Chroma"},hz:{refRange:[0,360],type:"angle",name:"Hue"}},base:hr,fromBase(t){let[e,n,r]=t,a;const s=2e-4;return Math.abs(n)<s&&Math.abs(r)<s?a=NaN:a=Math.atan2(r,n)*180/Math.PI,[e,Math.sqrt(n**2+r**2),j(a)]},toBase(t){return[t[0],t[1]*Math.cos(t[2]*Math.PI/180),t[1]*Math.sin(t[2]*Math.PI/180)]}});function Ms(t,e){[t,e]=w([t,e]);let[n,r,a]=vt.from(t),[s,o,i]=vt.from(e),l=n-s,u=r-o;Number.isNaN(a)&&Number.isNaN(i)?(a=0,i=0):Number.isNaN(a)?a=i:Number.isNaN(i)&&(i=a);let c=a-i,d=2*Math.sqrt(r*o)*Math.sin(c/2*(Math.PI/180));return Math.sqrt(l**2+u**2+d**2)}const fr=3424/4096,gr=2413/128,pr=2392/128,an=2610/16384,vs=2523/32,ws=16384/2610,sn=32/2523,Ss=[[.3592832590121217,.6976051147779502,-.035891593232029],[-.1920808463704993,1.100476797037432,.0753748658519118],[.0070797844607479,.0748396662186362,.8433265453898765]],xs=[[2048/4096,2048/4096,0],[6610/4096,-13613/4096,7003/4096],[17933/4096,-17390/4096,-543/4096]],Cs=[[.9999999999999998,.0086090370379328,.111029625003026],[.9999999999999998,-.0086090370379328,-.1110296250030259],[.9999999999999998,.5600313357106791,-.3206271749873188]],ks=[[2.0701522183894223,-1.3263473389671563,.2066510476294053],[.3647385209748072,.6805660249472273,-.0453045459220347],[-.0497472075358123,-.0492609666966131,1.1880659249923042]];var wt=new y({id:"ictcp",name:"ICTCP",coords:{i:{refRange:[0,1],name:"I"},ct:{refRange:[-.5,.5],name:"CT"},cp:{refRange:[-.5,.5],name:"CP"}},base:Ft,fromBase(t){let e=_(Ss,t);return _s(e)},toBase(t){let e=Rs(t);return _(ks,e)}});function _s(t){let e=t.map(function(n){let r=fr+gr*(n/1e4)**an,a=1+pr*(n/1e4)**an;return(r/a)**vs});return _(xs,e)}function Rs(t){return _(Cs,t).map(function(r){let a=Math.max(r**sn-fr,0),s=gr-pr*r**sn;return 1e4*(a/s)**ws})}function Es(t,e){[t,e]=w([t,e]);let[n,r,a]=wt.from(t),[s,o,i]=wt.from(e);return 720*Math.sqrt((n-s)**2+.25*(r-o)**2+(a-i)**2)}const $s=I.D65,br=.42,on=1/br,it=2*Math.PI,yr=[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],Ls=[[1.8620678550872327,-1.0112546305316843,.14918677544445175],[.38752654323613717,.6214474419314753,-.008973985167612518],[-.015841498849333856,-.03412293802851557,1.0499644368778496]],Fs=[[460,451,288],[460,-891,-261],[460,-220,-6300]],Ns={dark:[.8,.525,.8],dim:[.9,.59,.9],average:[1,.69,1]},ee={h:[20.14,90,164.25,237.53,380.14],e:[.8,.7,1,1.2,.8],H:[0,100,200,300,400]},Is=180/Math.PI,ln=Math.PI/180;function Mr(t,e){return t.map(r=>{const a=T(e*Math.abs(r)*.01,br);return 400*qe(a,r)/(a+27.13)})}function Ds(t,e){const n=100/e*27.13**on;return t.map(r=>{const a=Math.abs(r);return qe(n*T(a/(400-a),on),r)})}function Ps(t){let e=j(t);e<=ee.h[0]&&(e+=360);const n=ur(ee.h,e)-1,[r,a]=ee.h.slice(n,n+2),[s,o]=ee.e.slice(n,n+2),i=ee.H[n],l=(e-r)/s;return i+100*l/(l+(a-e)/o)}function Vs(t){let e=(t%400+400)%400;const n=Math.floor(.01*e);e=e%100;const[r,a]=ee.h.slice(n,n+2),[s,o]=ee.e.slice(n,n+2);return j((e*(o*r-s*a)-100*r*o)/(e*(o-s)-100*o))}function vr(t,e,n,r,a){const s={};s.discounting=a,s.refWhite=t,s.surround=r;const o=t.map(p=>p*100);s.la=e,s.yb=n;const i=o[1],l=_(yr,o);r=Ns[s.surround];const u=r[0];s.c=r[1],s.nc=r[2];const d=(1/(5*s.la+1))**4;s.fl=d*s.la+.1*(1-d)*(1-d)*Math.cbrt(5*s.la),s.flRoot=s.fl**.25,s.n=s.yb/i,s.z=1.48+Math.sqrt(s.n),s.nbb=.725*s.n**-.2,s.ncb=s.nbb;const m=Math.max(Math.min(u*(1-1/3.6*Math.exp((-s.la-42)/92)),1),0);s.dRgb=l.map(p=>Se(1,i/p,m)),s.dRgbInv=s.dRgb.map(p=>1/p);const f=l.map((p,g)=>p*s.dRgb[g]),h=Mr(f,s.fl);return s.aW=s.nbb*(2*h[0]+h[1]+.05*h[2]),s}const un=vr($s,64/Math.PI*.2,20,"average",!1);function St(t,e){if(!(t.J!==void 0^t.Q!==void 0))throw new Error("Conversion requires one and only one: 'J' or 'Q'");if(!(t.C!==void 0^t.M!==void 0^t.s!==void 0))throw new Error("Conversion requires one and only one: 'C', 'M' or 's'");if(!(t.h!==void 0^t.H!==void 0))throw new Error("Conversion requires one and only one: 'h' or 'H'");if(t.J===0||t.Q===0)return[0,0,0];let n=0;t.h!==void 0?n=j(t.h)*ln:n=Vs(t.H)*ln;const r=Math.cos(n),a=Math.sin(n);let s=0;t.J!==void 0?s=T(t.J,1/2)*.1:t.Q!==void 0&&(s=.25*e.c*t.Q/((e.aW+4)*e.flRoot));let o=0;t.C!==void 0?o=t.C/s:t.M!==void 0?o=t.M/e.flRoot/s:t.s!==void 0&&(o=4e-4*t.s**2*(e.aW+4)/e.c);const i=T(o*Math.pow(1.64-Math.pow(.29,e.n),-.73),10/9),l=.25*(Math.cos(n+2)+3.8),u=e.aW*T(s,2/e.c/e.z),c=5e4/13*e.nc*e.ncb*l,d=u/e.nbb,m=23*(d+.305)*Et(i,23*c+i*(11*r+108*a)),f=m*r,h=m*a,p=Ds(_(Fs,[d,f,h]).map(g=>g*1/1403),e.fl);return _(Ls,p.map((g,b)=>g*e.dRgbInv[b])).map(g=>g/100)}function wr(t,e){const n=t.map(v=>v*100),r=Mr(_(yr,n).map((v,S)=>v*e.dRgb[S]),e.fl),a=r[0]+(-12*r[1]+r[2])/11,s=(r[0]+r[1]-2*r[2])/9,o=(Math.atan2(s,a)%it+it)%it,i=.25*(Math.cos(o+2)+3.8),l=5e4/13*e.nc*e.ncb*Et(i*Math.sqrt(a**2+s**2),r[0]+r[1]+1.05*r[2]+.305),u=T(l,.9)*Math.pow(1.64-Math.pow(.29,e.n),.73),c=e.nbb*(2*r[0]+r[1]+.05*r[2]),d=T(c/e.aW,.5*e.c*e.z),m=100*T(d,2),f=4/e.c*d*(e.aW+4)*e.flRoot,h=u*d,p=h*e.flRoot,g=j(o*Is),b=Ps(g),M=50*T(e.c*u/(e.aW+4),1/2);return{J:m,C:h,h:g,s:M,Q:f,M:p,H:b}}var As=new y({id:"cam16-jmh",cssId:"--cam16-jmh",name:"CAM16-JMh",coords:{j:{refRange:[0,100],name:"J"},m:{refRange:[0,105],name:"Colorfulness"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:N,fromBase(t){const e=wr(t,un);return[e.J,e.M,e.h]},toBase(t){return St({J:t[0],M:t[1],h:t[2]},un)}});const Bs=I.D65,Os=216/24389,Sr=24389/27;function zs(t){return 116*(t>Os?Math.cbrt(t):(Sr*t+16)/116)-16}function xt(t){return t>8?Math.pow((t+16)/116,3):t/Sr}function Ts(t,e){let[n,r,a]=t,s=[],o=0;if(a===0)return[0,0,0];let i=xt(a);a>0?o=.00379058511492914*a**2+.608983189401032*a+.9155088574762233:o=9514440756550361e-21*a**2+.08693057439788597*a-21.928975842194614;const l=2e-12,u=15;let c=0,d=1/0;for(;c<=u;){s=St({J:o,C:r,h:n},e);const m=Math.abs(s[1]-i);if(m<d){if(m<=l)return s;d=m}o=o-(s[1]-i)*o/(2*s[1]),c+=1}return St({J:o,C:r,h:n},e)}function js(t,e){const n=zs(t[1]);if(n===0)return[0,0,0];const r=wr(t,Nt);return[j(r.h),r.C,n]}const Nt=vr(Bs,200/Math.PI*xt(50),xt(50)*100,"average",!1);var Ce=new y({id:"hct",name:"HCT",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},c:{refRange:[0,145],name:"Colorfulness"},t:{refRange:[0,100],name:"Tone"}},base:N,fromBase(t){return js(t)},toBase(t){return Ts(t,Nt)},formats:{color:{id:"--hct",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const Hs=Math.PI/180,cn=[1,.007,.0228];function dn(t){t[1]<0&&(t=Ce.fromBase(Ce.toBase(t)));const e=Math.log(Math.max(1+cn[2]*t[1]*Nt.flRoot,1))/cn[2],n=t[0]*Hs,r=e*Math.cos(n),a=e*Math.sin(n);return[t[2],r,a]}function Gs(t,e){[t,e]=w([t,e]);let[n,r,a]=dn(Ce.from(t)),[s,o,i]=dn(Ce.from(e));return Math.sqrt((n-s)**2+(r-o)**2+(a-i)**2)}var me={deltaE76:cs,deltaECMC:ms,deltaE2000:dr,deltaEJz:Ms,deltaEITP:Es,deltaEOK:Mt,deltaEHCT:Gs};function Ys(t){const e=t?Math.floor(Math.log10(Math.abs(t))):0;return Math.max(parseFloat(`1e${e-2}`),1e-6)}const mn={hct:{method:"hct.c",jnd:2,deltaEMethod:"hct",blackWhiteClamp:{}},"hct-tonal":{method:"hct.c",jnd:0,deltaEMethod:"hct",blackWhiteClamp:{channel:"hct.t",min:0,max:100}}};function U(t,{method:e=B.gamut_mapping,space:n=void 0,deltaEMethod:r="",jnd:a=2,blackWhiteClamp:s={}}={}){if(t=w(t),ke(arguments[1])?n=arguments[1]:n||(n=t.space),n=y.get(n),ne(t,n,{epsilon:0}))return t;let o;if(e==="css")o=Xs(t,{space:n});else{if(e!=="clip"&&!ne(t,n)){Object.prototype.hasOwnProperty.call(mn,e)&&({method:e,jnd:a,deltaEMethod:r,blackWhiteClamp:s}=mn[e]);let i=dr;if(r!==""){for(let u in me)if("deltae"+r.toLowerCase()===u.toLowerCase()){i=me[u];break}}let l=U(k(t,n),{method:"clip",space:n});if(i(t,l)>a){if(Object.keys(s).length===3){let M=y.resolveCoord(s.channel),v=V(k(t,M.space),M.id);if(J(v)&&(v=0),v>=s.max)return k({space:"xyz-d65",coords:I.D65},t.space);if(v<=s.min)return k({space:"xyz-d65",coords:[0,0,0]},t.space)}let u=y.resolveCoord(e),c=u.space,d=u.id,m=k(t,c);m.coords.forEach((M,v)=>{J(M)&&(m.coords[v]=0)});let h=(u.range||u.refRange)[0],p=Ys(a),g=h,b=V(m,d);for(;b-g>p;){let M=de(m);M=U(M,{space:n,method:"clip"}),i(m,M)-a<p?g=V(m,d):b=V(m,d),Y(m,d,(g+b)/2)}o=k(m,n)}else o=l}else o=k(t,n);if(e==="clip"||!ne(o,n,{epsilon:0})){let i=Object.values(n.coords).map(l=>l.range||[]);o.coords=o.coords.map((l,u)=>{let[c,d]=i[u];return c!==void 0&&(l=Math.max(c,l)),d!==void 0&&(l=Math.min(l,d)),l})}}return n!==t.space&&(o=k(o,t.space)),t.coords=o.coords,t}U.returns="color";const hn={WHITE:{space:ce,coords:[1,0,0]},BLACK:{space:ce,coords:[0,0,0]}};function Xs(t,{space:e}={}){t=w(t),e||(e=t.space),e=y.get(e);const a=y.get("oklch");if(e.isUnbounded)return k(t,e);const s=k(t,a);let o=s.coords[0];if(o>=1){const h=k(hn.WHITE,e);return h.alpha=t.alpha,k(h,e)}if(o<=0){const h=k(hn.BLACK,e);return h.alpha=t.alpha,k(h,e)}if(ne(s,e,{epsilon:0}))return k(s,e);function i(h){const p=k(h,e),g=Object.values(e.coords);return p.coords=p.coords.map((b,M)=>{if("range"in g[M]){const[v,S]=g[M].range;return lr(v,b,S)}return b}),p}let l=0,u=s.coords[1],c=!0,d=de(s),m=i(d),f=Mt(m,d);if(f<.02)return m;for(;u-l>1e-4;){const h=(l+u)/2;if(d.coords[1]=h,c&&ne(d,e,{epsilon:0}))l=h;else if(m=i(d),f=Mt(m,d),f<.02){if(.02-f<1e-4)break;c=!1,l=h}else u=h}return m}function k(t,e,{inGamut:n}={}){t=w(t),e=y.get(e);let r=e.from(t),a={space:e,coords:r,alpha:t.alpha};return n&&(a=U(a,n===!0?void 0:n)),a}k.returns="color";function ve(t,{precision:e=B.precision,format:n="default",inGamut:r=!0,...a}={}){var l;let s;t=w(t);let o=n;n=t.space.getFormat(n)??t.space.getFormat("default")??y.DEFAULT_FORMAT;let i=t.coords.slice();if(r||(r=n.toGamut),r&&!ne(t)&&(i=U(de(t),r===!0?void 0:r).coords),n.type==="custom")if(a.precision=e,n.serialize)s=n.serialize(i,t.alpha,a);else throw new TypeError(`format ${o} can only be used to parse colors, not for serialization`);else{let u=n.name||"color";n.serializeCoords?i=n.serializeCoords(i,e):e!==null&&(i=i.map(f=>Ye(f,{precision:e})));let c=[...i];if(u==="color"){let f=n.id||((l=n.ids)==null?void 0:l[0])||t.space.id;c.unshift(f)}let d=t.alpha;e!==null&&(d=Ye(d,{precision:e}));let m=t.alpha>=1||n.noAlpha?"":`${n.commas?",":" /"} ${d}`;s=`${u}(${c.join(n.commas?", ":" ")}${m})`}return s}const Zs=[[.6369580483012914,.14461690358620832,.1688809751641721],[.2627002120112671,.6779980715188708,.05930171646986196],[0,.028072693049087428,1.060985057710791]],Ws=[[1.716651187971268,-.355670783776392,-.25336628137366],[-.666684351832489,1.616481236634939,.0157685458139111],[.017639857445311,-.042770613257809,.942103121235474]];var Ue=new P({id:"rec2020-linear",cssId:"--rec2020-linear",name:"Linear REC.2020",white:"D65",toXYZ_M:Zs,fromXYZ_M:Ws});const Pe=1.09929682680944,fn=.018053968510807;var xr=new P({id:"rec2020",name:"REC.2020",base:Ue,toBase(t){return t.map(function(e){return e<fn*4.5?e/4.5:Math.pow((e+Pe-1)/Pe,1/.45)})},fromBase(t){return t.map(function(e){return e>=fn?Pe*Math.pow(e,.45)-(Pe-1):4.5*e})}});const Js=[[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],qs=[[2.493496911941425,-.9313836179191239,-.40271078445071684],[-.8294889695615747,1.7626640603183463,.023624685841943577],[.03584583024378447,-.07617238926804182,.9568845240076872]];var Cr=new P({id:"p3-linear",cssId:"--display-p3-linear",name:"Linear P3",white:"D65",toXYZ_M:Js,fromXYZ_M:qs});const Us=[[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],F=[[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]];var kr=new P({id:"srgb-linear",name:"Linear sRGB",white:"D65",toXYZ_M:Us,fromXYZ_M:F}),gn={aliceblue:[240/255,248/255,1],antiquewhite:[250/255,235/255,215/255],aqua:[0,1,1],aquamarine:[127/255,1,212/255],azure:[240/255,1,1],beige:[245/255,245/255,220/255],bisque:[1,228/255,196/255],black:[0,0,0],blanchedalmond:[1,235/255,205/255],blue:[0,0,1],blueviolet:[138/255,43/255,226/255],brown:[165/255,42/255,42/255],burlywood:[222/255,184/255,135/255],cadetblue:[95/255,158/255,160/255],chartreuse:[127/255,1,0],chocolate:[210/255,105/255,30/255],coral:[1,127/255,80/255],cornflowerblue:[100/255,149/255,237/255],cornsilk:[1,248/255,220/255],crimson:[220/255,20/255,60/255],cyan:[0,1,1],darkblue:[0,0,139/255],darkcyan:[0,139/255,139/255],darkgoldenrod:[184/255,134/255,11/255],darkgray:[169/255,169/255,169/255],darkgreen:[0,100/255,0],darkgrey:[169/255,169/255,169/255],darkkhaki:[189/255,183/255,107/255],darkmagenta:[139/255,0,139/255],darkolivegreen:[85/255,107/255,47/255],darkorange:[1,140/255,0],darkorchid:[153/255,50/255,204/255],darkred:[139/255,0,0],darksalmon:[233/255,150/255,122/255],darkseagreen:[143/255,188/255,143/255],darkslateblue:[72/255,61/255,139/255],darkslategray:[47/255,79/255,79/255],darkslategrey:[47/255,79/255,79/255],darkturquoise:[0,206/255,209/255],darkviolet:[148/255,0,211/255],deeppink:[1,20/255,147/255],deepskyblue:[0,191/255,1],dimgray:[105/255,105/255,105/255],dimgrey:[105/255,105/255,105/255],dodgerblue:[30/255,144/255,1],firebrick:[178/255,34/255,34/255],floralwhite:[1,250/255,240/255],forestgreen:[34/255,139/255,34/255],fuchsia:[1,0,1],gainsboro:[220/255,220/255,220/255],ghostwhite:[248/255,248/255,1],gold:[1,215/255,0],goldenrod:[218/255,165/255,32/255],gray:[128/255,128/255,128/255],green:[0,128/255,0],greenyellow:[173/255,1,47/255],grey:[128/255,128/255,128/255],honeydew:[240/255,1,240/255],hotpink:[1,105/255,180/255],indianred:[205/255,92/255,92/255],indigo:[75/255,0,130/255],ivory:[1,1,240/255],khaki:[240/255,230/255,140/255],lavender:[230/255,230/255,250/255],lavenderblush:[1,240/255,245/255],lawngreen:[124/255,252/255,0],lemonchiffon:[1,250/255,205/255],lightblue:[173/255,216/255,230/255],lightcoral:[240/255,128/255,128/255],lightcyan:[224/255,1,1],lightgoldenrodyellow:[250/255,250/255,210/255],lightgray:[211/255,211/255,211/255],lightgreen:[144/255,238/255,144/255],lightgrey:[211/255,211/255,211/255],lightpink:[1,182/255,193/255],lightsalmon:[1,160/255,122/255],lightseagreen:[32/255,178/255,170/255],lightskyblue:[135/255,206/255,250/255],lightslategray:[119/255,136/255,153/255],lightslategrey:[119/255,136/255,153/255],lightsteelblue:[176/255,196/255,222/255],lightyellow:[1,1,224/255],lime:[0,1,0],limegreen:[50/255,205/255,50/255],linen:[250/255,240/255,230/255],magenta:[1,0,1],maroon:[128/255,0,0],mediumaquamarine:[102/255,205/255,170/255],mediumblue:[0,0,205/255],mediumorchid:[186/255,85/255,211/255],mediumpurple:[147/255,112/255,219/255],mediumseagreen:[60/255,179/255,113/255],mediumslateblue:[123/255,104/255,238/255],mediumspringgreen:[0,250/255,154/255],mediumturquoise:[72/255,209/255,204/255],mediumvioletred:[199/255,21/255,133/255],midnightblue:[25/255,25/255,112/255],mintcream:[245/255,1,250/255],mistyrose:[1,228/255,225/255],moccasin:[1,228/255,181/255],navajowhite:[1,222/255,173/255],navy:[0,0,128/255],oldlace:[253/255,245/255,230/255],olive:[128/255,128/255,0],olivedrab:[107/255,142/255,35/255],orange:[1,165/255,0],orangered:[1,69/255,0],orchid:[218/255,112/255,214/255],palegoldenrod:[238/255,232/255,170/255],palegreen:[152/255,251/255,152/255],paleturquoise:[175/255,238/255,238/255],palevioletred:[219/255,112/255,147/255],papayawhip:[1,239/255,213/255],peachpuff:[1,218/255,185/255],peru:[205/255,133/255,63/255],pink:[1,192/255,203/255],plum:[221/255,160/255,221/255],powderblue:[176/255,224/255,230/255],purple:[128/255,0,128/255],rebeccapurple:[102/255,51/255,153/255],red:[1,0,0],rosybrown:[188/255,143/255,143/255],royalblue:[65/255,105/255,225/255],saddlebrown:[139/255,69/255,19/255],salmon:[250/255,128/255,114/255],sandybrown:[244/255,164/255,96/255],seagreen:[46/255,139/255,87/255],seashell:[1,245/255,238/255],sienna:[160/255,82/255,45/255],silver:[192/255,192/255,192/255],skyblue:[135/255,206/255,235/255],slateblue:[106/255,90/255,205/255],slategray:[112/255,128/255,144/255],slategrey:[112/255,128/255,144/255],snow:[1,250/255,250/255],springgreen:[0,1,127/255],steelblue:[70/255,130/255,180/255],tan:[210/255,180/255,140/255],teal:[0,128/255,128/255],thistle:[216/255,191/255,216/255],tomato:[1,99/255,71/255],turquoise:[64/255,224/255,208/255],violet:[238/255,130/255,238/255],wheat:[245/255,222/255,179/255],white:[1,1,1],whitesmoke:[245/255,245/255,245/255],yellow:[1,1,0],yellowgreen:[154/255,205/255,50/255]};let pn=Array(3).fill("<percentage> | <number>[0, 255]"),bn=Array(3).fill("<number>[0, 255]");var he=new P({id:"srgb",name:"sRGB",base:kr,fromBase:t=>t.map(e=>{let n=e<0?-1:1,r=e*n;return r>.0031308?n*(1.055*r**(1/2.4)-.055):12.92*e}),toBase:t=>t.map(e=>{let n=e<0?-1:1,r=e*n;return r<=.04045?e/12.92:n*((r+.055)/1.055)**2.4}),formats:{rgb:{coords:pn},rgb_number:{name:"rgb",commas:!0,coords:bn,noAlpha:!0},color:{},rgba:{coords:pn,commas:!0,lastAlpha:!0},rgba_number:{name:"rgba",commas:!0,coords:bn},hex:{type:"custom",toGamut:!0,test:t=>/^#([a-f0-9]{3,4}){1,2}$/i.test(t),parse(t){t.length<=5&&(t=t.replace(/[a-f0-9]/gi,"$&$&"));let e=[];return t.replace(/[a-f0-9]{2}/gi,n=>{e.push(parseInt(n,16)/255)}),{spaceId:"srgb",coords:e.slice(0,3),alpha:e.slice(3)[0]}},serialize:(t,e,{collapse:n=!0}={})=>{e<1&&t.push(e),t=t.map(s=>Math.round(s*255));let r=n&&t.every(s=>s%17===0);return"#"+t.map(s=>r?(s/17).toString(16):s.toString(16).padStart(2,"0")).join("")}},keyword:{type:"custom",test:t=>/^[a-z]+$/i.test(t),parse(t){t=t.toLowerCase();let e={spaceId:"srgb",coords:null,alpha:1};if(t==="transparent"?(e.coords=gn.black,e.alpha=0):e.coords=gn[t],e.coords)return e}}}}),_r=new P({id:"p3",cssId:"display-p3",name:"P3",base:Cr,fromBase:he.fromBase,toBase:he.toBase});B.display_space=he;let Qs;if(typeof CSS<"u"&&CSS.supports)for(let t of[A,xr,_r]){let e=t.getMinCoords(),r=ve({space:t,coords:e,alpha:1});if(CSS.supports("color",r)){B.display_space=t;break}}function Ks(t,{space:e=B.display_space,...n}={}){let r=ve(t,n);if(typeof CSS>"u"||CSS.supports("color",r)||!B.display_space)r=new String(r),r.color=t;else{let a=t;if((t.coords.some(J)||J(t.alpha))&&!(Qs??(Qs=CSS.supports("color","hsl(none 50% 50%)")))&&(a=de(t),a.coords=a.coords.map(L),a.alpha=L(a.alpha),r=ve(a,n),CSS.supports("color",r)))return r=new String(r),r.color=a,r;a=k(a,e),r=new String(ve(a,n)),r.color=a}return r}function ei(t,e){return t=w(t),e=w(e),t.space===e.space&&t.alpha===e.alpha&&t.coords.every((n,r)=>n===e.coords[r])}function Q(t){return V(t,[N,"y"])}function Rr(t,e){Y(t,[N,"y"],e)}function ti(t){Object.defineProperty(t.prototype,"luminance",{get(){return Q(this)},set(e){Rr(this,e)}})}var ni=Object.freeze({__proto__:null,getLuminance:Q,register:ti,setLuminance:Rr});function ri(t,e){t=w(t),e=w(e);let n=Math.max(Q(t),0),r=Math.max(Q(e),0);return r>n&&([n,r]=[r,n]),(n+.05)/(r+.05)}const ai=.56,si=.57,ii=.62,oi=.65,yn=.022,li=1.414,ui=.1,ci=5e-4,di=1.14,Mn=.027,mi=1.14;function vn(t){return t>=yn?t:t+(yn-t)**li}function ae(t){let e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,2.4)}function hi(t,e){e=w(e),t=w(t);let n,r,a,s,o,i;e=k(e,"srgb"),[s,o,i]=e.coords;let l=ae(s)*.2126729+ae(o)*.7151522+ae(i)*.072175;t=k(t,"srgb"),[s,o,i]=t.coords;let u=ae(s)*.2126729+ae(o)*.7151522+ae(i)*.072175,c=vn(l),d=vn(u),m=d>c;return Math.abs(d-c)<ci?r=0:m?(n=d**ai-c**si,r=n*di):(n=d**oi-c**ii,r=n*mi),Math.abs(r)<ui?a=0:r>0?a=r-Mn:a=r+Mn,a*100}function fi(t,e){t=w(t),e=w(e);let n=Math.max(Q(t),0),r=Math.max(Q(e),0);r>n&&([n,r]=[r,n]);let a=n+r;return a===0?0:(n-r)/a}const gi=5e4;function pi(t,e){t=w(t),e=w(e);let n=Math.max(Q(t),0),r=Math.max(Q(e),0);return r>n&&([n,r]=[r,n]),r===0?gi:(n-r)/r}function bi(t,e){t=w(t),e=w(e);let n=V(t,[A,"l"]),r=V(e,[A,"l"]);return Math.abs(n-r)}const yi=216/24389,wn=24/116,Ve=24389/27;let ot=I.D65;var Ct=new y({id:"lab-d65",name:"Lab D65",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:ot,base:N,fromBase(t){let n=t.map((r,a)=>r/ot[a]).map(r=>r>yi?Math.cbrt(r):(Ve*r+16)/116);return[116*n[1]-16,500*(n[0]-n[1]),200*(n[1]-n[2])]},toBase(t){let e=[];return e[1]=(t[0]+16)/116,e[0]=t[1]/500+e[1],e[2]=e[1]-t[2]/200,[e[0]>wn?Math.pow(e[0],3):(116*e[0]-16)/Ve,t[0]>8?Math.pow((t[0]+16)/116,3):t[0]/Ve,e[2]>wn?Math.pow(e[2],3):(116*e[2]-16)/Ve].map((r,a)=>r*ot[a])},formats:{"lab-d65":{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});const lt=Math.pow(5,.5)*.5+.5;function Mi(t,e){t=w(t),e=w(e);let n=V(t,[Ct,"l"]),r=V(e,[Ct,"l"]),a=Math.abs(Math.pow(n,lt)-Math.pow(r,lt)),s=Math.pow(a,1/lt)*Math.SQRT2-40;return s<7.5?0:s}var je=Object.freeze({__proto__:null,contrastAPCA:hi,contrastDeltaPhi:Mi,contrastLstar:bi,contrastMichelson:fi,contrastWCAG21:ri,contrastWeber:pi});function vi(t,e,n={}){ke(n)&&(n={algorithm:n});let{algorithm:r,...a}=n;if(!r){let s=Object.keys(je).map(o=>o.replace(/^contrast/,"")).join(", ");throw new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${s}`)}t=w(t),e=w(e);for(let s in je)if("contrast"+r.toLowerCase()===s.toLowerCase())return je[s](t,e,a);throw new TypeError(`Unknown contrast algorithm: ${r}`)}function Qe(t){let[e,n,r]=_e(t,N),a=e+15*n+3*r;return[4*e/a,9*n/a]}function Er(t){let[e,n,r]=_e(t,N),a=e+n+r;return[e/a,n/a]}function wi(t){Object.defineProperty(t.prototype,"uv",{get(){return Qe(this)}}),Object.defineProperty(t.prototype,"xy",{get(){return Er(this)}})}var Si=Object.freeze({__proto__:null,register:wi,uv:Qe,xy:Er});function ye(t,e,n={}){ke(n)&&(n={method:n});let{method:r=B.deltaE,...a}=n;for(let s in me)if("deltae"+r.toLowerCase()===s.toLowerCase())return me[s](t,e,a);throw new TypeError(`Unknown deltaE method: ${r}`)}function xi(t,e=.25){let r=[y.get("oklch","lch"),"l"];return Y(t,r,a=>a*(1+e))}function Ci(t,e=.25){let r=[y.get("oklch","lch"),"l"];return Y(t,r,a=>a*(1-e))}var ki=Object.freeze({__proto__:null,darken:Ci,lighten:xi});function $r(t,e,n=.5,r={}){return[t,e]=[w(t),w(e)],W(n)==="object"&&([n,r]=[.5,n]),Re(t,e,r)(n)}function Lr(t,e,n={}){let r;It(t)&&([r,n]=[t,e],[t,e]=r.rangeArgs.colors);let{maxDeltaE:a,deltaEMethod:s,steps:o=2,maxSteps:i=1e3,...l}=n;r||([t,e]=[w(t),w(e)],r=Re(t,e,l));let u=ye(t,e),c=a>0?Math.max(o,Math.ceil(u/a)+1):o,d=[];if(i!==void 0&&(c=Math.min(c,i)),c===1)d=[{p:.5,color:r(.5)}];else{let m=1/(c-1);d=Array.from({length:c},(f,h)=>{let p=h*m;return{p,color:r(p)}})}if(a>0){let m=d.reduce((f,h,p)=>{if(p===0)return 0;let g=ye(h.color,d[p-1].color,s);return Math.max(f,g)},0);for(;m>a;){m=0;for(let f=1;f<d.length&&d.length<i;f++){let h=d[f-1],p=d[f],g=(p.p+h.p)/2,b=r(g);m=Math.max(m,ye(b,h.color),ye(b,p.color)),d.splice(f,0,{p:g,color:r(g)}),f++}}}return d=d.map(m=>m.color),d}function Re(t,e,n={}){if(It(t)){let[l,u]=[t,e];return Re(...l.rangeArgs.colors,{...l.rangeArgs.options,...u})}let{space:r,outputSpace:a,progression:s,premultiplied:o}=n;t=w(t),e=w(e),t=de(t),e=de(e);let i={colors:[t,e],options:n};if(r?r=y.get(r):r=y.registry[B.interpolationSpace]||t.space,a=a?y.get(a):r,t=k(t,r),e=k(e,r),t=U(t),e=U(e),r.coords.h&&r.coords.h.type==="angle"){let l=n.hue=n.hue||"shorter",u=[r,"h"],[c,d]=[V(t,u),V(e,u)];isNaN(c)&&!isNaN(d)?c=d:isNaN(d)&&!isNaN(c)&&(d=c),[c,d]=as(l,[c,d]),Y(t,u,c),Y(e,u,d)}return o&&(t.coords=t.coords.map(l=>l*t.alpha),e.coords=e.coords.map(l=>l*e.alpha)),Object.assign(l=>{l=s?s(l):l;let u=t.coords.map((m,f)=>{let h=e.coords[f];return Se(m,h,l)}),c=Se(t.alpha,e.alpha,l),d={space:r,coords:u,alpha:c};return o&&(d.coords=d.coords.map(m=>m/c)),a!==r&&(d=k(d,a)),d},{rangeArgs:i})}function It(t){return W(t)==="function"&&!!t.rangeArgs}B.interpolationSpace="lab";function _i(t){t.defineFunction("mix",$r,{returns:"color"}),t.defineFunction("range",Re,{returns:"function<color>"}),t.defineFunction("steps",Lr,{returns:"array<color>"})}var Ri=Object.freeze({__proto__:null,isRange:It,mix:$r,range:Re,register:_i,steps:Lr}),Fr=new y({id:"hsl",name:"HSL",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:he,fromBase:t=>{let e=Math.max(...t),n=Math.min(...t),[r,a,s]=t,[o,i,l]=[NaN,0,(n+e)/2],u=e-n;if(u!==0){switch(i=l===0||l===1?0:(e-l)/Math.min(l,1-l),e){case r:o=(a-s)/u+(a<s?6:0);break;case a:o=(s-r)/u+2;break;case s:o=(r-a)/u+4}o=o*60}return i<0&&(o+=180,i=Math.abs(i)),o>=360&&(o-=360),[o,i*100,l*100]},toBase:t=>{let[e,n,r]=t;e=e%360,e<0&&(e+=360),n/=100,r/=100;function a(s){let o=(s+e/30)%12,i=n*Math.min(r,1-r);return r-i*Math.max(-1,Math.min(o-3,9-o,1))}return[a(0),a(8),a(4)]},formats:{hsl:{coords:["<number> | <angle>","<percentage>","<percentage>"]},hsla:{coords:["<number> | <angle>","<percentage>","<percentage>"],commas:!0,lastAlpha:!0}}}),Nr=new y({id:"hsv",name:"HSV",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},v:{range:[0,100],name:"Value"}},base:Fr,fromBase(t){let[e,n,r]=t;n/=100,r/=100;let a=r+n*Math.min(r,1-r);return[e,a===0?0:200*(1-r/a),100*a]},toBase(t){let[e,n,r]=t;n/=100,r/=100;let a=r*(1-n/2);return[e,a===0||a===1?0:(r-a)/Math.min(a,1-a)*100,a*100]},formats:{color:{id:"--hsv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}}),Ei=new y({id:"hwb",name:"HWB",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},w:{range:[0,100],name:"Whiteness"},b:{range:[0,100],name:"Blackness"}},base:Nr,fromBase(t){let[e,n,r]=t;return[e,r*(100-n)/100,100-r]},toBase(t){let[e,n,r]=t;n/=100,r/=100;let a=n+r;if(a>=1){let i=n/a;return[e,0,i*100]}let s=1-r,o=s===0?0:1-n/s;return[e,o*100,s*100]},formats:{hwb:{coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const $i=[[.5766690429101305,.1855582379065463,.1882286462349947],[.29734497525053605,.6273635662554661,.07529145849399788],[.02703136138641234,.07068885253582723,.9913375368376388]],Li=[[2.0415879038107465,-.5650069742788596,-.34473135077832956],[-.9692436362808795,1.8759675015077202,.04155505740717557],[.013444280632031142,-.11836239223101838,1.0151749943912054]];var Ir=new P({id:"a98rgb-linear",cssId:"--a98-rgb-linear",name:"Linear Adobe® 98 RGB compatible",white:"D65",toXYZ_M:$i,fromXYZ_M:Li}),Fi=new P({id:"a98rgb",cssId:"a98-rgb",name:"Adobe® 98 RGB compatible",base:Ir,toBase:t=>t.map(e=>Math.pow(Math.abs(e),563/256)*Math.sign(e)),fromBase:t=>t.map(e=>Math.pow(Math.abs(e),256/563)*Math.sign(e))});const Ni=[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],Ii=[[1.3457868816471583,-.25557208737979464,-.05110186497554526],[-.5446307051249019,1.5082477428451468,.02052744743642139],[0,0,1.2119675456389452]];var Dr=new P({id:"prophoto-linear",cssId:"--prophoto-rgb-linear",name:"Linear ProPhoto",white:"D50",base:Lt,toXYZ_M:Ni,fromXYZ_M:Ii});const Di=1/512,Pi=16/512;var Vi=new P({id:"prophoto",cssId:"prophoto-rgb",name:"ProPhoto",base:Dr,toBase(t){return t.map(e=>e<Pi?e/16:e**1.8)},fromBase(t){return t.map(e=>e>=Di?e**(1/1.8):16*e)}}),Ai=new y({id:"oklch",name:"Oklch",coords:{l:{refRange:[0,1],name:"Lightness"},c:{refRange:[0,.4],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},white:"D65",base:ce,fromBase(t){let[e,n,r]=t,a;const s=2e-4;return Math.abs(n)<s&&Math.abs(r)<s?a=NaN:a=Math.atan2(r,n)*180/Math.PI,[e,Math.sqrt(n**2+r**2),j(a)]},toBase(t){let[e,n,r]=t,a,s;return isNaN(r)?(a=0,s=0):(a=n*Math.cos(r*Math.PI/180),s=n*Math.sin(r*Math.PI/180)),[e,a,s]},formats:{oklch:{coords:["<percentage> | <number>","<number> | <percentage>[0,1]","<number> | <angle>"]}}});let Pr=I.D65;const Bi=216/24389,Sn=24389/27,[xn,Cn]=Qe({space:N,coords:Pr});var Vr=new y({id:"luv",name:"Luv",coords:{l:{refRange:[0,100],name:"Lightness"},u:{refRange:[-215,215]},v:{refRange:[-215,215]}},white:Pr,base:N,fromBase(t){let e=[L(t[0]),L(t[1]),L(t[2])],n=e[1],[r,a]=Qe({space:N,coords:e});if(!Number.isFinite(r)||!Number.isFinite(a))return[0,0,0];let s=n<=Bi?Sn*n:116*Math.cbrt(n)-16;return[s,13*s*(r-xn),13*s*(a-Cn)]},toBase(t){let[e,n,r]=t;if(e===0||J(e))return[0,0,0];n=L(n),r=L(r);let a=n/(13*e)+xn,s=r/(13*e)+Cn,o=e<=8?e/Sn:Math.pow((e+16)/116,3);return[o*(9*a/(4*s)),o,o*((12-3*a-20*s)/(4*s))]},formats:{color:{id:"--luv",coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),Dt=new y({id:"lchuv",name:"LChuv",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,220],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:Vr,fromBase(t){let[e,n,r]=t,a;const s=.02;return Math.abs(n)<s&&Math.abs(r)<s?a=NaN:a=Math.atan2(r,n)*180/Math.PI,[e,Math.sqrt(n**2+r**2),j(a)]},toBase(t){let[e,n,r]=t;return n<0&&(n=0),isNaN(r)&&(r=0),[e,n*Math.cos(r*Math.PI/180),n*Math.sin(r*Math.PI/180)]},formats:{color:{id:"--lchuv",coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const Oi=216/24389,zi=24389/27,kn=F[0][0],_n=F[0][1],ut=F[0][2],Rn=F[1][0],En=F[1][1],ct=F[1][2],$n=F[2][0],Ln=F[2][1],dt=F[2][2];function se(t,e,n){const r=e/(Math.sin(n)-t*Math.cos(n));return r<0?1/0:r}function We(t){const e=Math.pow(t+16,3)/1560896,n=e>Oi?e:t/zi,r=n*(284517*kn-94839*ut),a=n*(838422*ut+769860*_n+731718*kn),s=n*(632260*ut-126452*_n),o=n*(284517*Rn-94839*ct),i=n*(838422*ct+769860*En+731718*Rn),l=n*(632260*ct-126452*En),u=n*(284517*$n-94839*dt),c=n*(838422*dt+769860*Ln+731718*$n),d=n*(632260*dt-126452*Ln);return{r0s:r/s,r0i:a*t/s,r1s:r/(s+126452),r1i:(a-769860)*t/(s+126452),g0s:o/l,g0i:i*t/l,g1s:o/(l+126452),g1i:(i-769860)*t/(l+126452),b0s:u/d,b0i:c*t/d,b1s:u/(d+126452),b1i:(c-769860)*t/(d+126452)}}function Fn(t,e){const n=e/360*Math.PI*2,r=se(t.r0s,t.r0i,n),a=se(t.r1s,t.r1i,n),s=se(t.g0s,t.g0i,n),o=se(t.g1s,t.g1i,n),i=se(t.b0s,t.b0i,n),l=se(t.b1s,t.b1i,n);return Math.min(r,a,s,o,i,l)}var Ti=new y({id:"hsluv",name:"HSLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:Dt,gamutSpace:he,fromBase(t){let[e,n,r]=[L(t[0]),L(t[1]),L(t[2])],a;if(e>99.9999999)a=0,e=100;else if(e<1e-8)a=0,e=0;else{let s=We(e),o=Fn(s,r);a=n/o*100}return[r,a,e]},toBase(t){let[e,n,r]=[L(t[0]),L(t[1]),L(t[2])],a;if(r>99.9999999)r=100,a=0;else if(r<1e-8)r=0,a=0;else{let s=We(r);a=Fn(s,e)/100*n}return[r,a,e]},formats:{color:{id:"--hsluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});F[0][0];F[0][1];F[0][2];F[1][0];F[1][1];F[1][2];F[2][0];F[2][1];F[2][2];function ie(t,e){return Math.abs(e)/Math.sqrt(Math.pow(t,2)+1)}function Nn(t){let e=ie(t.r0s,t.r0i),n=ie(t.r1s,t.r1i),r=ie(t.g0s,t.g0i),a=ie(t.g1s,t.g1i),s=ie(t.b0s,t.b0i),o=ie(t.b1s,t.b1i);return Math.min(e,n,r,a,s,o)}var ji=new y({id:"hpluv",name:"HPLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:Dt,gamutSpace:"self",fromBase(t){let[e,n,r]=[L(t[0]),L(t[1]),L(t[2])],a;if(e>99.9999999)a=0,e=100;else if(e<1e-8)a=0,e=0;else{let s=We(e),o=Nn(s);a=n/o*100}return[r,a,e]},toBase(t){let[e,n,r]=[L(t[0]),L(t[1]),L(t[2])],a;if(r>99.9999999)r=100,a=0;else if(r<1e-8)r=0,a=0;else{let s=We(r);a=Nn(s)/100*n}return[r,a,e]},formats:{color:{id:"--hpluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const In=203,Dn=2610/2**14,Hi=2**14/2610,Gi=2523/2**5,Pn=2**5/2523,Vn=3424/2**12,An=2413/2**7,Bn=2392/2**7;var Yi=new P({id:"rec2100pq",cssId:"rec2100-pq",name:"REC.2100-PQ",base:Ue,toBase(t){return t.map(function(e){return(Math.max(e**Pn-Vn,0)/(An-Bn*e**Pn))**Hi*1e4/In})},fromBase(t){return t.map(function(e){let n=Math.max(e*In/1e4,0),r=Vn+An*n**Dn,a=1+Bn*n**Dn;return(r/a)**Gi})}});const On=.17883277,zn=.28466892,Tn=.55991073,mt=3.7743;var Xi=new P({id:"rec2100hlg",cssId:"rec2100-hlg",name:"REC.2100-HLG",referred:"scene",base:Ue,toBase(t){return t.map(function(e){return e<=.5?e**2/3*mt:(Math.exp((e-Tn)/On)+zn)/12*mt})},fromBase(t){return t.map(function(e){return e/=mt,e<=1/12?Math.sqrt(3*e):On*Math.log(12*e-zn)+Tn})}});const Ar={};q.add("chromatic-adaptation-start",t=>{t.options.method&&(t.M=Br(t.W1,t.W2,t.options.method))});q.add("chromatic-adaptation-end",t=>{t.M||(t.M=Br(t.W1,t.W2,t.options.method))});function Ke({id:t,toCone_M:e,fromCone_M:n}){Ar[t]=arguments[0]}function Br(t,e,n="Bradford"){let r=Ar[n],[a,s,o]=_(r.toCone_M,t),[i,l,u]=_(r.toCone_M,e),c=[[i/a,0,0],[0,l/s,0],[0,0,u/o]],d=_(c,r.toCone_M);return _(r.fromCone_M,d)}Ke({id:"von Kries",toCone_M:[[.40024,.7076,-.08081],[-.2263,1.16532,.0457],[0,0,.91822]],fromCone_M:[[1.8599363874558397,-1.1293816185800916,.21989740959619328],[.3611914362417676,.6388124632850422,-6370596838649899e-21],[0,0,1.0890636230968613]]});Ke({id:"Bradford",toCone_M:[[.8951,.2664,-.1614],[-.7502,1.7135,.0367],[.0389,-.0685,1.0296]],fromCone_M:[[.9869929054667121,-.14705425642099013,.15996265166373122],[.4323052697233945,.5183602715367774,.049291228212855594],[-.00852866457517732,.04004282165408486,.96848669578755]]});Ke({id:"CAT02",toCone_M:[[.7328,.4296,-.1624],[-.7036,1.6975,.0061],[.003,.0136,.9834]],fromCone_M:[[1.0961238208355142,-.27886900021828726,.18274517938277307],[.4543690419753592,.4735331543074117,.07209780371722911],[-.009627608738429355,-.00569803121611342,1.0153256399545427]]});Ke({id:"CAT16",toCone_M:[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],fromCone_M:[[1.862067855087233,-1.0112546305316845,.14918677544445172],[.3875265432361372,.6214474419314753,-.008973985167612521],[-.01584149884933386,-.03412293802851557,1.0499644368778496]]});Object.assign(I,{A:[1.0985,1,.35585],C:[.98074,1,1.18232],D55:[.95682,1,.92149],D75:[.94972,1,1.22638],E:[1,1,1],F2:[.99186,1,.67393],F7:[.95041,1,1.08747],F11:[1.00962,1,.6435]});I.ACES=[.32168/.33767,1,(1-.32168-.33767)/.33767];const Zi=[[.6624541811085053,.13400420645643313,.1561876870049078],[.27222871678091454,.6740817658111484,.05368951740793705],[-.005574649490394108,.004060733528982826,1.0103391003129971]],Wi=[[1.6410233796943257,-.32480329418479,-.23642469523761225],[-.6636628587229829,1.6153315916573379,.016756347685530137],[.011721894328375376,-.008284441996237409,.9883948585390215]];var Or=new P({id:"acescg",cssId:"--acescg",name:"ACEScg",coords:{r:{range:[0,65504],name:"Red"},g:{range:[0,65504],name:"Green"},b:{range:[0,65504],name:"Blue"}},referred:"scene",white:I.ACES,toXYZ_M:Zi,fromXYZ_M:Wi});const Ae=2**-16,ht=-.35828683,Be=(Math.log2(65504)+9.72)/17.52;var Ji=new P({id:"acescc",cssId:"--acescc",name:"ACEScc",coords:{r:{range:[ht,Be],name:"Red"},g:{range:[ht,Be],name:"Green"},b:{range:[ht,Be],name:"Blue"}},referred:"scene",base:Or,toBase(t){const e=-.3013698630136986;return t.map(function(n){return n<=e?(2**(n*17.52-9.72)-Ae)*2:n<Be?2**(n*17.52-9.72):65504})},fromBase(t){return t.map(function(e){return e<=0?(Math.log2(Ae)+9.72)/17.52:e<Ae?(Math.log2(Ae+e*.5)+9.72)/17.52:(Math.log2(e)+9.72)/17.52})}}),jn=Object.freeze({__proto__:null,A98RGB:Fi,A98RGB_Linear:Ir,ACEScc:Ji,ACEScg:Or,CAM16_JMh:As,HCT:Ce,HPLuv:ji,HSL:Fr,HSLuv:Ti,HSV:Nr,HWB:Ei,ICTCP:wt,JzCzHz:vt,Jzazbz:hr,LCH:xe,LCHuv:Dt,Lab:A,Lab_D65:Ct,Luv:Vr,OKLCH:Ai,OKLab:ce,P3:_r,P3_Linear:Cr,ProPhoto:Vi,ProPhoto_Linear:Dr,REC_2020:xr,REC_2020_Linear:Ue,REC_2100_HLG:Xi,REC_2100_PQ:Yi,XYZ_ABS_D65:Ft,XYZ_D50:Lt,XYZ_D65:N,sRGB:he,sRGB_Linear:kr});class x{constructor(...e){let n;e.length===1&&(n=w(e[0]));let r,a,s;n?(r=n.space||n.spaceId,a=n.coords,s=n.alpha):[r,a,s]=e,Object.defineProperty(this,"space",{value:y.get(r),writable:!1,enumerable:!0,configurable:!0}),this.coords=a?a.slice():[0,0,0],this.alpha=s>1||s===void 0?1:s<0?0:s;for(let o=0;o<this.coords.length;o++)this.coords[o]==="NaN"&&(this.coords[o]=NaN);for(let o in this.space.coords)Object.defineProperty(this,o,{get:()=>this.get(o),set:i=>this.set(o,i)})}get spaceId(){return this.space.id}clone(){return new x(this.space,this.coords,this.alpha)}toJSON(){return{spaceId:this.spaceId,coords:this.coords,alpha:this.alpha}}display(...e){let n=Ks(this,...e);return n.color=new x(n.color),n}static get(e,...n){return e instanceof x?e:new x(e,...n)}static defineFunction(e,n,r=n){let{instance:a=!0,returns:s}=r,o=function(...i){let l=n(...i);if(s==="color")l=x.get(l);else if(s==="function<color>"){let u=l;l=function(...c){let d=u(...c);return x.get(d)},Object.assign(l,u)}else s==="array<color>"&&(l=l.map(u=>x.get(u)));return l};e in x||(x[e]=o),a&&(x.prototype[e]=function(...i){return o(this,...i)})}static defineFunctions(e){for(let n in e)x.defineFunction(n,e[n],e[n])}static extend(e){if(e.register)e.register(x);else for(let n in e)x.defineFunction(n,e[n])}}x.defineFunctions({get:V,getAll:_e,set:Y,setAll:$t,to:k,equals:ei,inGamut:ne,toGamut:U,distance:mr,toString:ve});Object.assign(x,{util:Qa,hooks:q,WHITES:I,Space:y,spaces:y.registry,parse:cr,defaults:B});for(let t of Object.keys(jn))y.register(jn[t]);for(let t in y.registry)kt(t,y.registry[t]);q.add("colorspace-init-end",t=>{var e;kt(t.id,t),(e=t.aliases)==null||e.forEach(n=>{kt(n,t)})});function kt(t,e){let n=t.replace(/-/g,"_");Object.defineProperty(x.prototype,n,{get(){let r=this.getAll(t);return typeof Proxy>"u"?r:new Proxy(r,{has:(a,s)=>{try{return y.resolveCoord([e,s]),!0}catch{}return Reflect.has(a,s)},get:(a,s,o)=>{if(s&&typeof s!="symbol"&&!(s in a)){let{index:i}=y.resolveCoord([e,s]);if(i>=0)return a[i]}return Reflect.get(a,s,o)},set:(a,s,o,i)=>{if(s&&typeof s!="symbol"&&!(s in a)||s>=0){let{index:l}=y.resolveCoord([e,s]);if(l>=0)return a[l]=o,this.setAll(t,a),!0}return Reflect.set(a,s,o,i)}})},set(r){this.setAll(t,r)},configurable:!0,enumerable:!0})}x.extend(me);x.extend({deltaE:ye});Object.assign(x,{deltaEMethods:me});x.extend(ki);x.extend({contrast:vi});x.extend(Si);x.extend(ni);x.extend(Ri);x.extend(je);const qi={class:"px-2 py-5"},Ui={class:"flex justify-end gap-2 mt-8"},ao=fe({__name:"AppLayout",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(t){const e=sa(),n=Zn.useToast(),r=z(!1),a=z(!1);function s(m){const f=parseInt(m.slice(1,3),16),h=parseInt(m.slice(3,5),16),p=parseInt(m.slice(5,7),16);return(.299*f+.587*h+.114*p)/255>.5?"#000000":"#ffffff"}function o(m){const f=new x(m).oklch;f[0]=Number((f[0]*100).toFixed(4));const[h,p,g]=f;return`${h}% ${p} ${g||0}`}const i=Un();function l({primaryColor:m,backgroundColor:f,cardsColor:h}){document.documentElement.style.setProperty("--p",o(m)),document.documentElement.style.setProperty("--b1",o(f)),document.documentElement.style.setProperty("--b2",o(h));const p=s(m),g=s(h),b=s(f);document.documentElement.style.setProperty("--ac",o(b)),document.documentElement.style.setProperty("--bc",o(g)),document.documentElement.style.setProperty("--pc",o(p))}async function u(){var g;const m=(g=t.professional)==null?void 0:g.hasQuestionary;if(!e.isLoggedIn||!m)return;const f=window.location.pathname;if(f.includes("/login")||f.includes("/register"))return;if(new URLSearchParams(window.location.search).get("checkForm")&&!e.form_completed){const{data:b}=await pt.get(`/finished-form?slug=${t.slug}`);if(typeof(b==null?void 0:b.form_completed)!="boolean")return;b.form_completed===!0?e.form_completed=!0:r.value=!0}}function c(){r.value=!1,a.value=!0}function d(){n.success("Questionário enviado com sucesso!"),a.value=!1,e.form_completed=!0}return Wn(async()=>{var m;(m=e==null?void 0:e.token)!=null&&m.length&&(i.logo=t.company.logo,i.name=t.company.name,i.plan_id=t.company.plan_id,i.slug=t.company.slug,i.sunday_time=t.company.sunday_time,i.monday_time=t.company.monday_time,i.tuesday_time=t.company.tuesday_time,i.wednesday_time=t.company.wednesday_time,i.thursday_time=t.company.thursday_time,i.friday_time=t.company.friday_time,i.saturday_time=t.company.saturday_time,i.solo_professional=t.company.solo_professional,i.background_color=t.company.background_color,i.buttons_color=t.company.buttons_color,i.cards_color=t.company.cards_color,l({backgroundColor:i.background_color,primaryColor:i.buttons_color,cardsColor:i.cards_color}),u())}),(m,f)=>{var M;const h=qa,p=qn,g=Qn,b=Za;return C(),R(Ge,null,[X(oe(na)),X(ia,{slug:t.slug,company:t.company,professional:t.professional},{default:ue(()=>[$("div",{class:G(["h-bottom",(t.slug,"")])},[_t(m.$slots,"default")],2),X(h,{slug:t.slug},null,8,["slug"])]),_:3},8,["slug","company","professional"]),r.value?(C(),le(g,{key:0,modelValue:r.value,"onUpdate:modelValue":f[0]||(f[0]=v=>r.value=v),title:"Questionário Triagem"},{default:ue(()=>{var v;return[$("div",qi,[$("p",null,Z((v=t.professional)==null?void 0:v.user.name)+" preparou algumas perguntas antes do atendimento. Deseja prosseguir?",1),$("div",Ui,[X(p,{onClick:c,color:"primary"},{default:ue(()=>f[2]||(f[2]=[gt(" Responder Questionário ")])),_:1})])])]}),_:1},8,["modelValue"])):we("",!0),X(b,{modelValue:a.value,"onUpdate:modelValue":f[1]||(f[1]=v=>a.value=v),slug:t.slug,title:"Questionário","professional-id":(M=t.professional)==null?void 0:M.id,onSubmitted:d},null,8,["modelValue","slug","professional-id"])],64)}}});export{ao as _,Ia as a,Qn as b};
