<?php

namespace App\Http\Controllers;

use App\Models\FinancialData;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class FinancialAnalyticsController extends Controller
{
    /**
     * Get financial analytics data with filters
     */
    public function index(Request $request)
    {
        $request->validate([
            'type' => 'sometimes|in:payment_analysis,cash_flow,dre',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'month' => 'sometimes|integer|between:1,12',
            'year' => 'sometimes|integer|min:2020',
        ]);

        $query = FinancialData::query();

        // Apply filters
        if ($request->has('type')) {
            $query->byType($request->type);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byPeriod($request->start_date, $request->end_date);
        }

        if ($request->has('month') && $request->has('year')) {
            $query->byMonth($request->month, $request->year);
        } elseif ($request->has('year')) {
            $query->byYear($request->year);
        }

        $data = $query->orderBy('period', 'desc')
                     ->orderBy('category')
                     ->get();

        return response()->json([
            'data' => $data,
            'summary' => $this->generateSummary($data),
            'available_periods' => FinancialData::getAvailablePeriods(),
            'available_types' => FinancialData::getAvailableTypes()
        ]);
    }

    /**
     * Get payment analysis data
     */
    public function paymentAnalysis(Request $request)
    {
        $request->validate([
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'month' => 'sometimes|integer|between:1,12',
            'year' => 'sometimes|integer|min:2020',
        ]);

        $query = FinancialData::byType('payment_analysis');

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byPeriod($request->start_date, $request->end_date);
        }

        if ($request->has('month') && $request->has('year')) {
            $query->byMonth($request->month, $request->year);
        } elseif ($request->has('year')) {
            $query->byYear($request->year);
        }

        $data = $query->orderBy('value', 'desc')->get();

        return response()->json([
            'data' => $data,
            'total' => $data->sum('value'),
            'chart_data' => $this->formatChartData($data, 'payment_analysis')
        ]);
    }

    /**
     * Get cash flow data
     */
    public function cashFlow(Request $request)
    {
        $request->validate([
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'year' => 'sometimes|integer|min:2020',
        ]);

        $query = FinancialData::byType('cash_flow');

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byPeriod($request->start_date, $request->end_date);
        }

        if ($request->has('year')) {
            $query->byYear($request->year);
        }

        $data = $query->orderBy('period')->get();

        return response()->json([
            'data' => $data,
            'chart_data' => $this->formatCashFlowChart($data)
        ]);
    }

    /**
     * Get DRE (Income Statement) data
     */
    public function dre(Request $request)
    {
        $request->validate([
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'year' => 'sometimes|integer|min:2020',
        ]);

        $query = FinancialData::byType('dre');

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->byPeriod($request->start_date, $request->end_date);
        }

        if ($request->has('year')) {
            $query->byYear($request->year);
        }

        $data = $query->orderBy('period')->get();

        return response()->json([
            'data' => $data,
            'chart_data' => $this->formatDreChart($data)
        ]);
    }

    /**
     * Upload and process CSV data
     */
    public function uploadCsv(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt',
            'type' => 'required|in:payment_analysis,cash_flow,dre',
            'period' => 'required|date'
        ]);

        $file = $request->file('file');
        $type = $request->type;
        $period = Carbon::parse($request->period);

        try {
            $this->processCsvFile($file, $type, $period);
            
            return response()->json([
                'message' => 'CSV data uploaded and processed successfully',
                'type' => $type,
                'period' => $period->format('Y-m-d')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process CSV file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate summary statistics
     */
    private function generateSummary($data)
    {
        $grouped = $data->groupBy('type');
        
        return $grouped->map(function ($items, $type) {
            return [
                'type' => $type,
                'total_records' => $items->count(),
                'total_value' => $items->sum('value'),
                'avg_value' => $items->avg('value'),
                'latest_period' => $items->max('period')
            ];
        });
    }

    /**
     * Format data for charts
     */
    private function formatChartData($data, $type)
    {
        switch ($type) {
            case 'payment_analysis':
                return [
                    'labels' => $data->pluck('category')->toArray(),
                    'values' => $data->pluck('value')->toArray(),
                    'percentages' => $data->pluck('percentage')->toArray()
                ];
            default:
                return [];
        }
    }

    /**
     * Format cash flow data for charts
     */
    private function formatCashFlowChart($data)
    {
        $monthlyData = $data->groupBy(function ($item) {
            return Carbon::parse($item->period)->format('Y-m');
        });

        $labels = [];
        $predicted = [];
        $actual = [];

        foreach ($monthlyData as $month => $items) {
            $labels[] = Carbon::createFromFormat('Y-m', $month)->format('M/Y');
            
            $monthData = $items->first()->data ?? [];
            $predicted[] = $monthData['predicted'] ?? 0;
            $actual[] = $monthData['actual'] ?? 0;
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Previsto',
                    'data' => $predicted,
                    'borderColor' => 'rgb(75, 192, 192)',
                    'backgroundColor' => 'rgba(75, 192, 192, 0.2)'
                ],
                [
                    'label' => 'Realizado',
                    'data' => $actual,
                    'borderColor' => 'rgb(255, 99, 132)',
                    'backgroundColor' => 'rgba(255, 99, 132, 0.2)'
                ]
            ]
        ];
    }

    /**
     * Format DRE data for charts
     */
    private function formatDreChart($data)
    {
        $monthlyData = $data->groupBy(function ($item) {
            return Carbon::parse($item->period)->format('Y-m');
        });

        $labels = [];
        $revenue = [];
        $expenses = [];
        $profit = [];

        foreach ($monthlyData as $month => $items) {
            $labels[] = Carbon::createFromFormat('Y-m', $month)->format('M/Y');
            
            $revenueItem = $items->where('category', 'Receita Líquida de Vendas')->first();
            $expenseItem = $items->where('category', 'Despesas Operacionais')->first();
            $profitItem = $items->where('category', 'Lucro / Prejuízo Final')->first();
            
            $revenue[] = $revenueItem ? abs($revenueItem->value) : 0;
            $expenses[] = $expenseItem ? abs($expenseItem->value) : 0;
            $profit[] = $profitItem ? $profitItem->value : 0;
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Receita',
                    'data' => $revenue,
                    'backgroundColor' => 'rgba(75, 192, 192, 0.6)'
                ],
                [
                    'label' => 'Despesas',
                    'data' => $expenses,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.6)'
                ],
                [
                    'label' => 'Lucro',
                    'data' => $profit,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.6)'
                ]
            ]
        ];
    }

    /**
     * Process CSV file and store data
     */
    private function processCsvFile($file, $type, $period)
    {
        $content = file_get_contents($file->getPathname());
        $lines = explode("\n", $content);
        
        // Remove existing data for this type and period
        FinancialData::where('type', $type)
                   ->where('period', $period->format('Y-m-d'))
                   ->delete();

        foreach ($lines as $index => $line) {
            if ($index === 0 || empty(trim($line))) continue; // Skip header and empty lines
            
            $data = str_getcsv($line, ';');
            
            if (count($data) < 2) continue;
            
            $this->processDataRow($data, $type, $period);
        }
    }

    /**
     * Process individual data row
     */
    private function processDataRow($data, $type, $period)
    {
        switch ($type) {
            case 'payment_analysis':
                $this->processPaymentAnalysisRow($data, $period);
                break;
            case 'cash_flow':
                $this->processCashFlowRow($data, $period);
                break;
            case 'dre':
                $this->processDreRow($data, $period);
                break;
        }
    }

    /**
     * Process payment analysis row
     */
    private function processPaymentAnalysisRow($data, $period)
    {
        if (count($data) >= 4) {
            FinancialData::create([
                'type' => 'payment_analysis',
                'category' => $data[0],
                'value' => $this->parseValue($data[1]),
                'percentage' => $this->parseValue($data[2]),
                'period' => $period,
                'month' => $period->month,
                'year' => $period->year,
                'data' => [
                    'monthly_value' => $this->parseValue($data[3])
                ]
            ]);
        }
    }

    /**
     * Process cash flow row
     */
    private function processCashFlowRow($data, $period)
    {
        if (count($data) >= 3) {
            $monthlyData = [];
            for ($i = 1; $i < count($data); $i += 2) {
                if (isset($data[$i]) && isset($data[$i + 1])) {
                    $monthlyData[] = [
                        'predicted' => $this->parseValue($data[$i]),
                        'actual' => $this->parseValue($data[$i + 1])
                    ];
                }
            }

            FinancialData::create([
                'type' => 'cash_flow',
                'category' => $data[0],
                'value' => $this->parseValue($data[1]),
                'period' => $period,
                'month' => $period->month,
                'year' => $period->year,
                'data' => [
                    'monthly_data' => $monthlyData,
                    'predicted' => $this->parseValue($data[1]),
                    'actual' => isset($data[2]) ? $this->parseValue($data[2]) : 0
                ]
            ]);
        }
    }

    /**
     * Process DRE row
     */
    private function processDreRow($data, $period)
    {
        if (count($data) >= 14) {
            $monthlyValues = [];
            for ($i = 1; $i <= 12; $i++) {
                $monthlyValues[] = $this->parseValue($data[$i]);
            }

            FinancialData::create([
                'type' => 'dre',
                'category' => $data[0],
                'value' => $this->parseValue($data[13]), // Total column
                'period' => $period,
                'month' => $period->month,
                'year' => $period->year,
                'data' => [
                    'monthly_values' => $monthlyValues
                ]
            ]);
        }
    }

    /**
     * Parse Brazilian formatted values
     */
    private function parseValue($value)
    {
        if (empty($value) || $value === '0,00') {
            return 0;
        }
        
        // Remove currency symbols and spaces
        $value = preg_replace('/[^\d,.-]/', '', $value);
        
        // Convert Brazilian format to decimal
        $value = str_replace('.', '', $value); // Remove thousands separator
        $value = str_replace(',', '.', $value); // Convert decimal separator
        
        return (float) $value;
    }
}
