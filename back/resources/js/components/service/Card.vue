<template>
    <div
        class="card card-side sm:flex-col w-full shadow-sm sm:w-52 h-24 sm:h-72 bg-base-200 rounded-lg sm:p-0 p-1 px-2"
        :class="`${cardStyles}`"
    >
        <div class="sm:card-body sm:py-2 sm:px-6 ">
            <div class="w-full h-full flex flex-col px-2 sm:p-0 py-1">
                <div class="font-bold line-clamp-2 leading-5 text-base">
                    {{ item.name }}
                </div>
                <span class="text-xs mt-auto">
                    <b>Duração:</b> <br />
                    {{ handleTime(item.duration) }}
                </span>
                <div class="justify-between md:flex hidden">
                    <div class="mt-2 text-xs sm:text-base">
                        Preço:
                        <span class="font-bold"
                            >{{ formatPrice(parseFloat(item.price)) }}
                        </span>
                    </div>
                </div>
            </div>
            <base-button
                color="primary"
                size="sm"
                class="text-xs mt-auto btn-sm text-center hidden sm:block"
                @click="emit('schedule')"
            >
                Agendar
            </base-button>
        </div>
        <div class="ml-auto flex flex-col py-2 sm:hidden">
            <div class="sm:text-base">
                <div class="font-bold mb-2 text-right">
                    {{ formatPrice(parseFloat(item.price)) }}
                </div>
            </div>
            <base-button
                color="primary"
                size="sm"
                class="text-xs mt-auto btn-sm sm:hidden ml-auto"
                @click="emit('schedule')"
            >
                Agendar
            </base-button>
        </div>
    </div>
    <service-modal
        v-if="!!item"
        :modal-state="modalState"
        :close-modal="closeModal"
        type="services"
        :item="item"
    />
</template>
<script setup lang="ts">
import type { Service } from "@/utils/models";
import { handleTime } from "@/utils/date";
import formatPrice from "@/utils/formatPrice";
import VLazyImage from "v-lazy-image";

const emit = defineEmits(["schedule"]);
defineProps<{
    item: Service;
    cardStyles?: string;
}>();

const modalState = ref(false);

function closeModal() {
    modalState.value = false;
}
</script>
