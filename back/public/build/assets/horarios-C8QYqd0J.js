import{_ as T}from"./BaseButton-CxEL1ccK.js";import{_ as Y}from"./Skeleton.vue_vue_type_script_setup_true_lang-BgQVuwFr.js";import{_ as A,a as D}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{l as E,r as f,s as v,h as M,f as F,A as H,o as l,j as y,w as _,a as c,u as t,N as $,b as C,t as N,y as I,c as b,F as O,e as L,d as P,g as S}from"./app-BkKCG4YJ.js";import{b as R,u as k,a as U}from"./BaseLayout--Fu-QUOh.js";import{r as q}from"./ArrowLeftIcon-CzJvhcLt.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const z={class:"px-3 pb-3"},G={key:0,class:"grid grid-cols-3 sm:grid-cols-5 md:grid-cols-7 gap-2"},J={key:1,class:"text-center mt-4 text-lg font-bold text-accent-content"},ae=E({__name:"horarios",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(r){const u=f(!1);R();const{addHour:V,cart:w}=k(),p=f([]),m=f(!1),n=f(v().format("YYYY-MM-DD"));function B(o){V({hour:o,date:n.value}),$.get(`/${r.slug}/cart`)}function j(o,e){const a=v(`${n.value}T${o}`),i=a.add(e,"minute");return w.some(d=>{const s=v(`${d.date}T${d.hour}`),g=s.add(d.service.duration,"minute");return a.isBefore(g)&&i.isAfter(s)})}async function h(){m.value=!0,u.value=!1;try{const o=k(),{data:e}=await U.get(`${r.company.slug}/slots`,{params:{professionalId:o.professional.id,serviceId:o.service.id,date:n.value}});p.value=e.map(a=>{const i=j(a.hour,o.service.duration);return{...a,locked:a.locked||i}})}catch{u.value=!0}finally{m.value=!1}}M(async()=>{h()});const x=F(()=>!!k().professional.id);return H(n,h),(o,e)=>{const a=D,i=Y,d=T;return l(),y(A,{slug:r.slug,company:r.company,professional:r.professional},{default:_(()=>[c("div",null,[c("div",{onClick:e[0]||(e[0]=s=>t($).get(`/${r.slug}/${t(x)?"profissionais":"servicos"}`)),class:"ml-2 mb-1 cursor-pointer font-bold gap-1 text-lg text-accent-content flex items-center mt-3 md:mt-5"},[C(t(q),{class:"w-5 h-5"}),c("span",null,"Alterar "+N(t(x)?"profissional":"serviço"),1)]),c("div",z,[e[2]||(e[2]=c("div",{class:"text-lg font-bold text-accent-content"}," Escolha o dia ",-1)),C(a,{modelValue:t(n),"onUpdate:modelValue":e[1]||(e[1]=s=>I(n)?n.value=s:null),type:"date",class:"my-2","input-classes":"border-black focus:border-black"},null,8,["modelValue"]),e[3]||(e[3]=c("div",{class:"text-lg font-bold mb-3 text-accent-content"}," Horários disponíveis ",-1)),t(u)?S("",!0):(l(),b("div",G,[t(m)?(l(),y(i,{key:0,number:21,classes:"h-12"})):(l(!0),b(O,{key:1},L(t(p),(s,g)=>(l(),y(d,{onClick:K=>B(s.hour),color:"primary",disabled:s.locked,key:g},{default:_(()=>[P(N(s.hour),1)]),_:2},1032,["onClick","disabled"]))),128))])),!t(m)&&(t(p).length===0||t(u))?(l(),b("div",J," Não há horários disponíveis para este dia. ")):S("",!0)])])]),_:1},8,["slug","company","professional"])}}});export{ae as default};
