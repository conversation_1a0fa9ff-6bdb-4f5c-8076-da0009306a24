import{o as n,c as r,a as s,l as v,t as a,d as i,g as u,u as l,s as c,b as g}from"./app-BkKCG4YJ.js";import{f as x}from"./formatPrice-DFW6Dy3T.js";function b(t,o){return n(),r("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"fill-rule":"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z","clip-rule":"evenodd"})])}const h={class:"flex flex-row elevated-card rounded-lg p-2 relative items-stretch px-1 py-2"},w={class:"flex flex-col w-[calc(100%-32px)] overflow-hidden ml-2"},p={class:"truncate leading-tight mb-auto"},C={key:0,class:"truncate leading-tight text-sm"},y={class:"truncate leading-tight text-sm"},N={class:"truncate leading-tight text-sm"},k={class:"text-sm"},V=v({__name:"CartCard",props:{item:{type:Object,required:!0},cart:{type:Boolean,default:!1}},emits:["delete"],setup(t,{emit:o}){const d=o;return(B,e)=>{var m;return n(),r("div",h,[e[6]||(e[6]=s("div",null,[s("img",{src:"/assets/schedule.jpg",class:"w-20 h-20 aspect-square"})],-1)),s("div",w,[s("div",p,[s("b",null,a(t.item.service.name),1)]),(m=t.item.professional)!=null&&m.name?(n(),r("span",C,[e[2]||(e[2]=i(" Atendente: ")),s("b",null,a(t.item.professional.name),1)])):u("",!0),s("span",y,[e[3]||(e[3]=i(" Dia: ")),s("b",null,a(l(c)(t.item.date).format("DD/MM")),1)]),s("span",N,[e[4]||(e[4]=i(" Horário: ")),s("b",null,a(t.item.hour)+" - "+a(l(c)().hour(Number(t.item.hour.split(":")[0])).minute(Number(t.item.hour.split(":")[1])).add(t.item.service.duration,"minutes").format("HH:mm")),1)]),s("span",k,[e[5]||(e[5]=i(" Preço: ")),s("b",null,a(l(x)(Number(t.item.service.price))),1)])]),t.cart?(n(),r("button",{key:0,class:"p-2 bg-base-200 rounded-md z-20 absolute top-0 right-0",onClick:e[1]||(e[1]=f=>d("delete",t.item.id))},[g(l(b),{class:"w-4",onClick:e[0]||(e[0]=f=>d("delete",t.item.id))})])):u("",!0)])}}});export{V as _};
