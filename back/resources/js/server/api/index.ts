import axios from "axios";
import { useUser } from "@/store/user";
import type { Cash, Order, Service } from "@/utils/models";
// import { useToast } from "vue-toast-notification";
import { useCompanyStore } from "@/store/company";
import { useToast } from "vue-toast-notification";
import { router } from "@inertiajs/vue3";
export const api = axios.create({});

const errors = [
    "Cannot read properties of undefined (reading 'id')",
    "You must be logged in to access this page.",
];
api.interceptors.request.use(
    (config) => {
        config.baseURL = "/api/";
        const userStore = useUser();
        const token = userStore.token;
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error),
);
api.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        if (error.response) {
            const toast = useToast();
            const company = useCompanyStore();
            const { status, data } = error.response;
            console.log(status);
            if (
                status === 403 ||
                status === 401 ||
                (status === 500 && errors[1].includes(data.message))
            ) {
                useUser().token = "";
                if (data?.error === "Invalid credentials") {
                    return;
                }
                router.get(`/${company.slug}/login`, {
                    redirect: route().current(),
                });
                toast.error("Sessão expirada", {
                    position: "top",
                });
            }
        }
        return Promise.reject(error);
    },
);
export async function getAllServices(url: string) {
    return api.get(url);
}

type iData = Service | Cash | Order;
export function getProfessionals(slug: string) {
    return api.get(`/${slug}/professionals`);
}
export function getSlots() {
    //
}
export function getServices(url: string) {
    async function create(data: iData) {
        try {
            const response = await api.post(url, data);
            return response.data;
        } catch (err) {
            throw err;
        }
    }

    async function deleteById(id: number) {
        try {
            const response = await api.delete(`${url}/${id}`);
            return response.data;
        } catch (err) {
            throw err;
        }
    }

    async function editById(id: number) {
        try {
            const response = await api.put(`${url}/${id}`);
            return response.data;
        } catch (err) {
            throw err;
        }
    }

    async function getAll() {
        try {
            const response = await api.get(url);
            return response.data;
        } catch (err) {
            throw err;
        }
    }

    return { create, getAll, editById, deleteById, api };
}
