<?php

use NotificationChannels\PusherPushNotifications\PusherChannel;
use NotificationChannels\PusherPushNotifications\PusherMessage;
use Illuminate\Notifications\Notification;

class AppointmentMade extends Notification
{
    public $message;
    public function __construct(string $message)
    {
        $this->message = $message;
    }

    public function via($notifiable)
    {
        return [PusherChannel::class];
    }

    public function toPushNotification()
    {
        return PusherMessage::create()
            ->web()
            ->title('Novo agendamento')
            ->body($this->message)
            ->setOption('interests', ['notify']);
    }
}