<template>
    <base-dialog :loading="loading" hide-close :title="title" v-model="modelValue" class="modal-fullscreen">
        <div class="px-2 pt-2 flex flex-col gap-4 w-full h-full overflow-y-auto">
            <div v-if="questions.length === 0 && !loading" class="text-center py-4">
                Nenhuma pergunta encontrada.
            </div>
            <div v-if="loading" class="flex justify-center py-4">
                <div class="loading loading-spinner loading-lg"></div>
            </div>
            <div v-for="(question, index) in questions" :key="index" class="mb-6 w-full" v-else>
                <div class="font-bold mb-3 text-lg">{{ question.label }}</div>

                <!-- Text input for string type questions -->
                <input-base
                    v-if="question.type === 'string'"
                    v-model="answers[question.id]"
                    :placeholder="'Digite sua resposta'"
                    class="w-full"
                />

                <!-- Switch for boolean/switch type questions -->
                <div v-else-if="question.type === 'switch'" class="form-control w-full">
                    <label class="label cursor-pointer justify-start gap-4">
                        <input
                            type="checkbox"
                            class="toggle toggle-primary"
                            v-model="answers[question.id]"
                        />
                        <span class="label-text">{{ answers[question.id] ? 'Sim' : 'Não' }}</span>
                    </label>
                </div>

                <!-- Textarea for textarea type questions -->
                <textarea
                    v-else-if="question.type === 'textarea'"
                    v-model="answers[question.id]"
                    class="textarea textarea-bordered w-full"
                    :placeholder="'Digite sua resposta'"
                    rows="4"
                ></textarea>

                <!-- Select for multiple type questions -->
                <select
                    v-else-if="question.type === 'multiple'"
                    class="select select-bordered w-full"
                    v-model="answers[question.id]"
                >
                    <option disabled value="">Selecione uma opção</option>
                    <option
                        v-for="(option, optIndex) in question.options"
                        :key="optIndex"
                        :value="option"
                    >
                        {{ option }}
                    </option>
                </select>

                <!-- Default text input for any other type -->
                <input-base
                    v-else
                    v-model="answers[question.id]"
                    :placeholder="'Digite sua resposta'"
                    class="w-full"
                />
            </div>
            <div class="flex justify-end gap-2 mt-4 mb-4">
                <base-button @click="modelValue = false" color="ghost">
                    Cancelar
                </base-button>
                <base-button @click="submitAnswers" color="primary" :loading="submitting">
                    Enviar
                </base-button>
            </div>
        </div>
    </base-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { api } from '@/server/api';
import { useToast } from 'vue-toast-notification';

// Define question interface
interface Question {
    id: number;
    label: string;
    type: string;
    options: string | null;
    isRequired: boolean;
    company_id: number;
    company_professional_id?: number;
}

const props = defineProps({
    slug: {
        type: String,
        required: true
    },
    title: {
        type: String,
        default: 'Questionário'
    },
    professionalId: {
        type: Number,
        default: null
    }
});
const modelValue = defineModel();
const emit = defineEmits(['submitted']);
const $toast = useToast();

const questions = ref<Question[]>([]);
const answers = ref<Record<number, any>>({});
const loading = ref(false);
const submitting = ref(false);

// Function to parse options from string to array
const parseOptions = (options: string | null): string[] => {
    if (!options) return [];

    try {
        // Try to parse as JSON
        return []
    } catch (e) {
        // If not valid JSON, return empty array
        console.error('Error parsing options:', e);
        return [];
    }
};

const fetchQuestions = async () => {
    loading.value = true;
    try {
        // Add professional_id parameter if provided
        const params = props.professionalId ? { professional_id: props.professionalId } : {};
        const { data } = await api.get(`/${props.slug}/questions`, { params });
        questions.value = data;

        // Initialize answers object
        data.forEach((question: Question) => {
            if (question.type === 'multiple') {
                answers.value[question.id] = '';
            } else if (question.type === 'switch') {
                answers.value[question.id] = false;
            } else if (question.type === 'checkbox') {
                answers.value[question.id] = [];
            } else {
                answers.value[question.id] = '';
            }
        });
    } catch (error) {
        console.error('Error fetching questions:', error);
        $toast.error('Erro ao carregar as perguntas');
    } finally {
        loading.value = false;
    }
};

const submitAnswers = async () => {
    submitting.value = true;
    try {
        // Submit each answer individually
        for (const questionId of Object.keys(answers.value)) {
            const question = questions.value.find(q => q.id === parseInt(questionId));
            if (!question) continue;

            const answerValue = typeof answers.value[parseInt(questionId)] === 'object'
                ? JSON.stringify(answers.value[parseInt(questionId)])
                : String(answers.value[parseInt(questionId)]);

            await api.post(`/answers`, {
                question_text: question.label,
                answer: answerValue,
                company_slug: props.slug
            });
        }

        // $toast.success('Respostas enviadas com sucesso!');
        emit('submitted');
        modelValue.value = false;
    } catch (error) {
        console.error('Error submitting answers:', error);
        $toast.error('Erro ao enviar as respostas');
    } finally {
        submitting.value = false;
    }
};

watch(() => modelValue.value, (newVal) => {
    if (newVal) {
        fetchQuestions();
    }
});

onMounted(() => {
    if (modelValue.value) {
        fetchQuestions();
    }
});
</script>

<style scoped>
.modal-fullscreen :deep(.modal-box) {
    max-width: 100vw;
    width: 100vw;
    max-height: 100vh;
    height: 100vh;
    border-radius: 0;
}
</style>
