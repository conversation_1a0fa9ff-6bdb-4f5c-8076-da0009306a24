<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;

class NotificationsController extends Controller
{
    public function getNotifications(Request $request)
    {
        $notifications = Notification::where('user_id', auth()->id())->orderBy('created_at', 'desc')->get();
        return response()->json($notifications);
    }
    public function readNotification(Request $request)
    {
        $data = $request->only(['notifications']);
        foreach ($data['notifications'] as $notification) {
            $notification = Notification::find($notification);
            $notification->read = true;
            $notification->save();
        }
        return response()->json(['message' => 'Notifications read']);
    }
}
