<template>
    <Head title="Profissionais" />
    <BaseLayout>
        <div id="landing-page" class="flex bg-base-100 px-0 w-full flex-col text-accent-content h-layout">
            <div class="relative">
                <div class="w-full h-36 sm:h-[25vh] relative">
                    <div class="carousel w-full h-full">
                        <img :src="company.banner" alt="Banner" class="w-full bg-black h-full object-fill" />
                    </div>
                </div>
                <section class="md:py-5 px-5 bg-base-100 shadow-md rounded-lg">
                    <div
                        class="text-lg leading-7 pb-3 sm:text-xl pl-2 mt-1 ml-24 sm:ml-36 font-semibold text-accent-content drop-shadow-lg">
                        <div v-if="company.solo_professional">
                            {{ professional.user.name }}
                        </div>
                        <div v-else>
                            {{ company.name }}
                        </div>
                        <div class="flex mt-1">
                            <a :href="`https://wa.me/55${company.whatsapp}`" v-if="company.whatsapp" target="_blank">
                                <img src="/icons/whatsapp.svg" class="w-7" alt="" />
                            </a>
                            <a :href="`https://www.instagram.com/${company.instagram}`" v-if="company.instagram"
                                target="_blank">
                                <img src="/icons/instagram.svg" class="w-7 ml-2" alt="" />
                            </a>
                        </div>
                    </div>
                </section>
                <div
                    class="absolute bottom-0 sm:left-8 z-10 left-1 w-28 h-28 sm:w-28 sm:h-28 border-4 border-white rounded-full overflow-hidden bg-base-100">
                    <img v-if="company.solo_professional" :src="`/${professional.user.profile_photo_path}`" class="" />
                    <AppLogo v-else class="w-full h-full object-scale-down" size="192" />
                </div>
            </div>
            <div class="px-3">
                <div class="font-semibold text-lg my-2">
                    Especialidades
                </div>
                <div class="flex gap-2 flex-wrap">
                    <div v-for="(item, index) in professional.specialties?.split(',')" :key="index"
                        class="badge text-ellipsis badge-primary gap-1 p-3">
                        {{ item }}
                    </div>
                </div>
                <div class="mt-5 font-semibold text-lg">Sobre</div>
                <div v-if="company.solo_professional">
                    {{ professional.about }}
                </div>
                <div v-else>
                    {{ company.about }}
                </div>
                <div class="mt-3 font-semibold text-lg">Serviços</div>
                <div class="carousel mt-5 w-full pb-10">
                    <div v-for="(service, i) in topServices" :id="`slide-${i}`" :key="i"
                        class="carousel-item relative w-full flex items-center">
                        <a :href="`#slide-${calcNavPrev(i)}`" class="btn btn-circle btn-xs">❮</a>
                        <div class="flex-1 w-full text-center">
                            <div class="text-lg font-semibold">
                                {{ service?.name }}
                            </div>
                            <div>
                                {{ formatPrice(service?.price) }}
                            </div>
                            <div>
                                Duração: {{ handleTime(service?.duration) }}
                            </div>
                            <base-button color="primary" @click="handleSchedule(service)" size="sm"
                                class="mt-2">Agendar</base-button>
                        </div>
                        <a :href="`#slide-${calcNavNext(i)}`" class="btn btn-circle btn-xs">❯</a>
                    </div>
                </div>
            </div>
        </div>
    </BaseLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Head } from '@inertiajs/vue3';
import BaseLayout from '@/Layouts/BaseLayout.vue';
import { handleTime } from "@/utils/date";
import { api } from "@/server/api";
import formatPrice from "@/utils/formatPrice";
import { useSchedule } from "@/store/schedule";
import { router } from "@inertiajs/vue3";
import AppLogo from '@/Components/AppLogo.vue';

const { filterProductsByGame, addService, addVariant } = useSchedule();

onMounted(async () => {
    const { data } = await api(`/${slug}/most-scheduled`);
    topServices.value = data;
});

const topServices = ref([]);
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
    professional: {
        type: Object,
    },
});

const selectVariant = ref();
const showFullText = ref(false);
const maxAboutLength = 250;
const selectedVariant = ref();
const selectedService = ref();
const loading = ref(true);

const toggleText = () => {
    showFullText.value = !showFullText.value;
};

function handleVariant(e) {
    addService(selectedService.value);
    addVariant(selectedVariant.value);
    if (company.solo_professional) router.get(`/${slug}/horarios`);
    else router.get(`/${slug}/profissionais`);
}

function handleSchedule(service) {
    selectedService.value = service;
    addService(service);
    if (company.solo_professional) router.get(`/${slug}/horarios`);
    else router.get(`/${slug}/profissionais`);
}

const truncatedAbout = computed(() => {
    if (!company.value.about) {
        return "";
    }
    if (company.value.about.length > maxAboutLength) {
        return showFullText.value
            ? company.value.about
            : company.value.about.slice(0, maxAboutLength) + "...";
    }
    return company.value.about;
});

const bannerImages = computed(() => {
    const url = [
        "https://marketplace.canva.com/EAF_ZFGfAwE/1/0/1600w/canva-banner-para-twitch-montanha-vintage-retr%C3%B4-roxo-nqw7QjAVpKo.jpg",
    ];
    return url;
});

const calcNavPrev = (index) => {
    if (index === 0) {
        return `${bannerImages.value.length}`;
    }
    return `${index - 1}`;
};

const calcNavNext = (index) => {
    if (index === bannerImages.value.length) {
        return `0`;
    }
    return `${index + 1}`;
};

const businessHours = computed(() => {
    const dias = {
        sunday_time: "DOM",
        monday_time: "SEG",
        tuesday_time: "TER",
        wednesday_time: "QUA",
        thursday_time: "QUI",
        friday_time: "SEX",
        saturday_time: "SÁB",
    };

    const resultado = Object.entries(dias).map(([chave, dia]) => {
        const horario = company[chave];
        return {
            day: dia,
            hour: horario ? horario.replace("-", " às ") : "Fechado",
        };
    });

    return resultado;
});

const activeTab = ref("contact");
</script>
