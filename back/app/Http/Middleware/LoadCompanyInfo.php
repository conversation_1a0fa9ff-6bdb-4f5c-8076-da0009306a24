<?php

namespace App\Http\Middleware;

use App\Models\CompanyProfessional;
use Closure;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class LoadCompanyInfo
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {
        $user = auth('sanctum')->user();

        if ($user) {
            $companyId = Cache::remember(
                'company_id_' . $user->id,
                now()->addHours(1),
                function () use ($user) {
                    $professional = CompanyProfessional::where('user_id', $user->id)->first();
                    return $professional ? $professional->company_id : null;
                }
            );
            $request->attributes->set('company_id', $companyId);
        }

        return $next($request);
    }
}
