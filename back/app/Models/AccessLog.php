<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccessLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_type',
        'action',
        'resource',
        'method',
        'ip_address',
        'user_agent',
        'session_id',
        'response_time',
        'status_code',
        'request_data',
        'response_data',
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'response_time' => 'integer',
        'status_code' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByUserType($query, $userType)
    {
        return $query->where('user_type', $userType);
    }

    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function scopeSuccessful($query)
    {
        return $query->whereBetween('status_code', [200, 299]);
    }

    public function scopeErrors($query)
    {
        return $query->where('status_code', '>=', 400);
    }

    // Static methods for logging
    public static function logAccess($userId = null, $userType = 'guest', $action = 'page_view', $resource = null, $method = 'GET', $responseTime = null, $statusCode = 200, $requestData = null, $responseData = null)
    {
        return self::create([
            'user_id' => $userId,
            'user_type' => $userType,
            'action' => $action,
            'resource' => $resource,
            'method' => $method,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'response_time' => $responseTime,
            'status_code' => $statusCode,
            'request_data' => $requestData,
            'response_data' => $responseData,
        ]);
    }

    public static function logLogin($userId, $userType)
    {
        return self::logAccess($userId, $userType, 'login', '/login', 'POST');
    }

    public static function logLogout($userId, $userType)
    {
        return self::logAccess($userId, $userType, 'logout', '/logout', 'POST');
    }

    public static function logApiCall($userId, $userType, $resource, $method, $responseTime, $statusCode, $requestData = null, $responseData = null)
    {
        return self::logAccess($userId, $userType, 'api_call', $resource, $method, $responseTime, $statusCode, $requestData, $responseData);
    }
}
