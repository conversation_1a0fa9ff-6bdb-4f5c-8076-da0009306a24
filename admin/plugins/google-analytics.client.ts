export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig();
  const measurementId = config.public.GOOGLE_ANALYTICS_MEASUREMENT_ID;

  if (!measurementId) {
    console.warn('Google Analytics Measurement ID not configured');
    return;
  }

  // Load Google Analytics script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
  document.head.appendChild(script);

  // Initialize gtag
  window.dataLayer = window.dataLayer || [];
  function gtag(...args: any[]) {
    window.dataLayer.push(args);
  }
  
  gtag('js', new Date());
  gtag('config', measurementId);

  // Make gtag available globally
  window.gtag = gtag;

  // Track page views on route changes
  const router = useRouter();
  router.afterEach((to) => {
    gtag('config', measurementId, {
      page_path: to.fullPath,
    });
  });
});
