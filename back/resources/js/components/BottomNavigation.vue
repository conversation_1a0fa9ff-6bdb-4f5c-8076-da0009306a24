<template>
    <div class="btm-nav flex md:hidden h-[64px] fixed bottom-0 left-0 right-0 z-50">
        <button
            v-for="{ icon, to, label, name } in routes"
            :key="to"
            :class="
                name === route().current()
                    ? 'active bg-primary border-primary-content text-primary-content'
                    : 'bg-base-200'
            "
            class="relative"
            @click="router.get(to)"
        >
            <!-- <span
        v-if="to === '/chat' && chatStore.newMessage"
        class="absolute top-0 right-0 flex h-3 w-3"
      >
        <span
          class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"
        ></span>
        <span
          class="relative inline-flex rounded-full h-3 w-3 bg-primary"
        ></span>
      </span> -->
            <component
                :class="
                    name === route().current() ? 'fill-primary-content' : ''
                "
                :is="icon"
                class="w-5 h-5"
            ></component>
            <span
                :class="
                    name === route().current() ? 'text-primary-content' : ''
                "
                class="text-xs"
                >{{ label }}</span
            >
        </button>
    </div>
</template>
<script setup lang="ts">
import { router } from "@inertiajs/vue3";

import { useCompanyStore } from "@/store/company";
import {
    BookmarkSquareIcon,
    UserIcon,
    ScissorsIcon,
    ShoppingCartIcon,
    // config icon
    Cog8ToothIcon,
    CalendarDaysIcon,
    HomeIcon,
} from "@heroicons/vue/24/solid";
const companyStore = useCompanyStore();
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
});
const routes = [
    {
        to: `/${slug}`,
        label: "Início",
        icon: HomeIcon,
        name: "index",
    },
    {
        to: `/${slug}/servicos`,
        label: "Agendar",
        icon: CalendarDaysIcon,
        name: "servicos",
    },
    {
        to: `/${slug}/pedidos`,
        label: "Historico",
        icon: BookmarkSquareIcon,
        name: "pedidos",
    },
    // {
    //   to: "/feed",
    //   label: "Feed",
    //   icon: CameraIcon,
    // },
    {
        to: `/${slug}/perfil`,
        label: "Perfil",
        icon: UserIcon,
        name: "perfil",
    },
    // {
    //     to: `/${slug}/cart`,
    //     label: "Carrinho",
    //     icon: ShoppingCartIcon,
    //     name: "cart",
    // },
    // {
    //     to: `/${slug}/config`,
    //     label: "Configurações",
    //     icon: Cog8ToothIcon,
    //     name: "config",
    // },
];

// const drawer = useDrawer();
// const chatStore = useChat();
</script>
<style lang="scss">
.btm-nav {
    button {
        color: hsl(var(--bc));
    }
}
</style>
