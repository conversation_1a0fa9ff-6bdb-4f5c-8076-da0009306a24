<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Psi+</title>
    <link name="manifest" rel="manifest" href="/manifest.json" />
    <meta name="description" content="Terapia acessível para todos, busque o profissional ideal e agende já sua consulta.">
    <meta name="keywords" content="Psi+, Psicologo, Terapia, Agendamento, Psychologist">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="Psi+">
    <meta property="og:description" content="Terapia acessível para todos, busque o profissional ideal e agende já sua consulta.">
    <meta property="og:image" content="{{ asset('/icons/logo.png') }}">
    <meta property="og:image:width" content="512">
    <meta property="og:image:height" content="512">
    <meta property="og:site_name" content="">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="{{ url()->current() }}">
    <meta name="twitter:title" content="Psi+">
    <meta name="twitter:description" content="Terapia acessível para todos, busque o profissional ideal e agende já sua consulta.">
    <meta name="twitter:image" content="{{ asset('/icons/logo.png') }}">

    <!-- WhatsApp -->
    <meta property="og:image:alt" content="Psi+ logo">
    <meta property="og:image:type" content="image/webp">

    <!-- Schema.org / Google -->
    <meta itemprop="name" content="Psi+">
    <meta itemprop="description" content="Terapia acessível para todos, busque o profissional ideal e agende já sua consulta.">
    <meta itemprop="image" content="{{ asset('/icons/logo.png') }}">

    <link rel="icon" href="{{ asset('/icons/logo.png') }}" type="image/icon type">

    <!-- Fonts -->
    {{-- <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" /> --}}

    <!-- Google Analytics -->
    @if(config('services.google_analytics.measurement_id'))
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.measurement_id') }}"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '{{ config('services.google_analytics.measurement_id') }}');
    </script>
    @endif

    <!-- Scripts -->
    @routes
    @vite(['resources/js/app.js', "resources/js/Pages/{$page['component']}.vue"])
    @inertiaHead
</head>

<body class="font-sans antialiased">
    @inertia
</body>

</html>
