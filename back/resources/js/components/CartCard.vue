<template>
  <div class="flex flex-row elevated-card rounded-lg p-2 relative items-stretch px-1 py-2">
    <div>
      <img
        src="/assets/schedule.jpg"
        class="w-20 h-20 aspect-square"
      />
    </div>
    <div class="flex flex-col w-[calc(100%-32px)] overflow-hidden ml-2">
      <div class="truncate leading-tight mb-auto">
        <!-- Serviço: -->
        <b>
          {{ item.service.name }}
        </b>
      </div>
      <span
        v-if="item.professional?.name"
        class="truncate leading-tight text-sm"
      >
        Atendente:
        <b>
          {{ item.professional.name }}
        </b>
      </span>
      
      <span class="truncate leading-tight text-sm">
        Dia:
        <b>
          {{ dayjs(item.date).format("DD/MM") }} 
        </b>
      </span>
      <span class="truncate leading-tight text-sm">
        Hor<PERSON><PERSON>:
        <b>
          {{ item.hour }} -
          {{
            dayjs()
              .hour(Number(item.hour.split(":")[0]))
              .minute(Number(item.hour.split(":")[1]))
              .add(item.service.duration, "minutes")
              .format("HH:mm")
          }}
        </b>
      </span>
      <!-- <span class="text-sm">
        Duração:
        <b>
          {{ handleTime(item.service.duration) }}
        </b>
      </span> -->
      <!-- <span class="text-xs">
        {{ $t("cart.item.priceUnit") }}:
        {{ formatPrice(Number(item.price)) }}</span
      > -->
      <span class="text-sm">
        Preço:
        <b>
          {{ formatPrice(Number(item.service.price)) }}
        </b>
      </span>
    </div>
    <button
      v-if="cart"
      class="p-2 bg-base-200 rounded-md z-20 absolute top-0 right-0"
      @click="emit('delete', item.id)"
    >
      <TrashIcon class="w-4" @click="emit('delete', item.id)" />
    </button>
  </div>
</template>
<script setup lang="ts">
import { TrashIcon } from "@heroicons/vue/24/solid";
import dayjs from "dayjs";
import type { Schedule } from "@/utils/models";
import formatPrice from "@/utils/formatPrice";
import { handleTime } from "@/utils/date";

defineProps({
  item: {
    type: Object as PropType<Schedule>,
    required: true,
  },
  cart: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["delete"]);
</script>
