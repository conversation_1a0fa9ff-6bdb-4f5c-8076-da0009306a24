<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div>
            
            <div @click="router.get(`/${slug}/servicos`)" class="ml-5 cursor-pointer font-bold gap-1 text-lg text-accent-content flex items-center mt-3 md:mt-5">
                <ArrowLeftIcon class="w-5 h-5" /> <span>Alterar Serviço</span>
            </div>
            <div
                class="flex flex-wrap sm:gap-10 gap-1 justify-center sm:mt-5 p-1 mt-2 overflow-auto mx-1"
            >
                <professional-card
                    v-for="(professional, i) in professionals"
                    :key="i"
                    :professional="professional"
                    @schedule="handleSchedule(professional)"
                />
            </div>
            <Loading :show="loading && !cartDrawerActive" />
        </div>
    </AppLayout>
</template>

<script lang="ts" setup>
import Loading from "@/components/Loading.vue";
import { useSchedule } from "@/store/schedule";
import type { Professional } from "@/utils/models";
import { getProfessionals } from "@/server/api";
import AppLayout from "@/Layouts/AppLayout.vue";
import { router } from "@inertiajs/vue3";
import { storeToRefs } from "pinia";
import { ArrowLeftIcon } from "@heroicons/vue/24/solid";
const { loading, cartDrawerActive } = storeToRefs(useSchedule());
const { addProfessional } = useSchedule();
const professionals = ref<Professional[]>();
function handleSchedule(professional: Professional) {
    addProfessional(professional);
    router.get(`/${slug}/horarios`);
}
async function fetchProfessionals() {
    const { data } = await getProfessionals(slug);
    professionals.value = data;
}
const { slug } = defineProps({
    slug: { type: String },
    company: { type: Object },
    professional: { type: Object },
});

onMounted(() => {
    fetchProfessionals();
});
</script>
