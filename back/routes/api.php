<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\SuperAdminAuthController;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\SuperAdminMiddleware;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

// Guest routes (authentication)
Route::group(['middleware' => 'guest'], function () {
    Route::post('/register', [AuthController::class, 'registerUser'])->name('api.register');
    Route::get('/login', [AuthController::class, 'login'])->name('api.login');
    Route::post('/login', [AuthController::class, 'loginUser'])->name('api.login.post');
    Route::post('/login-professional', [AuthController::class, 'loginAdmin'])->name('api.login.professional');
    Route::post('/login-super-admin', [SuperAdminAuthController::class, 'login'])->name('api.login.super_admin');
    Route::post('/decrypt-user-data', [AuthController::class, 'decryptUserData']);
    Route::post('/complete-registration', [AuthController::class, 'completeRegistration']);
    Route::get('/check-user', [AuthController::class, 'checkUser']);

    // Password reset routes
    Route::post('/forgot-password', function (Request $request) {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT ? $status : '';
    })->name('api.password.email');

    Route::post('/reset-password', function (Request $request) {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );
        return $status === Password::PASSWORD_RESET ? response()->json(true, 200) : response()->json('', 500);
    })->name('api.password.update');
});

// Authenticated user routes
Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::get('/profile', function (Request $request) {
        return $request->user();
    });

    Route::put('/profile', function (Request $request) {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'string|email|max:255',
            'phone' => 'required|string|max:255',
        ]);

        $userId = Auth::id();
        $user = User::find($userId);
        $user->name = $request->name;
        if(isset($request->email)) $user->email = $request->email;
        $user->phone = $request->phone;
        $user->save();
        return $user;
    });
});

// Admin routes
Route::group(['middleware' => ['auth:sanctum', AdminMiddleware::class]], function () {
    // Financial Analytics routes
    Route::prefix('financial')->group(function () {
        Route::get('/', [App\Http\Controllers\FinancialAnalyticsController::class, 'index']);
        Route::get('/payment-analysis', [App\Http\Controllers\FinancialAnalyticsController::class, 'paymentAnalysis']);
        Route::get('/cash-flow', [App\Http\Controllers\FinancialAnalyticsController::class, 'cashFlow']);
        Route::get('/dre', [App\Http\Controllers\FinancialAnalyticsController::class, 'dre']);
        Route::post('/upload-csv', [App\Http\Controllers\FinancialAnalyticsController::class, 'uploadCsv']);
    });
});

// Super Admin Routes
Route::group(['prefix' => 'super-admin', 'middleware' => ['auth:sanctum', SuperAdminMiddleware::class]], function () {
    // Authentication
    Route::post('/logout', [SuperAdminAuthController::class, 'logout']);
    Route::get('/user', [SuperAdminAuthController::class, 'user']);
    Route::put('/profile', [SuperAdminAuthController::class, 'updateProfile']);

    // Super Admin Management
    Route::get('/admins', [SuperAdminAuthController::class, 'getSuperAdmins']);
    Route::post('/admins', [SuperAdminAuthController::class, 'createSuperAdmin']);
    Route::delete('/admins/{user}', [SuperAdminAuthController::class, 'deleteSuperAdmin']);
});
