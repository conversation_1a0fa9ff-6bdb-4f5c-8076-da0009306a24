<?php

namespace App\Http\Middleware;

use App\Models\Analytics;
use App\Models\AccessLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class AnalyticsMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        
        $response = $next($request);
        
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000); // Convert to milliseconds

        // Track the request
        $this->trackRequest($request, $response, $responseTime);

        return $response;
    }

    private function trackRequest(Request $request, Response $response, int $responseTime)
    {
        try {
            $user = Auth::user();
            $userId = $user ? $user->id : null;
            $userType = $user ? $user->type : 'guest';
            $companyId = $request->get('company_id');

            // Determine action type
            $action = $this->getActionType($request);
            
            // Log access
            AccessLog::logAccess(
                $userId,
                $userType,
                $action,
                $request->getPathInfo(),
                $request->getMethod(),
                $responseTime,
                $response->getStatusCode(),
                $this->sanitizeRequestData($request),
                null // We don't log response data for privacy
            );

            // Track analytics for specific events
            if ($this->shouldTrackAnalytics($request, $action)) {
                $this->trackAnalyticsEvent($request, $userId, $companyId, $action);
            }

        } catch (\Exception $e) {
            // Silently fail to avoid breaking the application
            \Log::error('Analytics tracking failed: ' . $e->getMessage());
        }
    }

    private function getActionType(Request $request): string
    {
        $method = $request->getMethod();
        $path = $request->getPathInfo();

        // Special cases
        if (str_contains($path, '/login')) {
            return 'login_attempt';
        }
        
        if (str_contains($path, '/logout')) {
            return 'logout';
        }
        
        if (str_contains($path, '/register')) {
            return 'registration';
        }

        // API calls
        if (str_starts_with($path, '/api/')) {
            return 'api_call';
        }

        // Default to page view for GET requests
        if ($method === 'GET') {
            return 'page_view';
        }

        return strtolower($method) . '_request';
    }

    private function shouldTrackAnalytics(Request $request, string $action): bool
    {
        // Don't track certain paths
        $excludePaths = [
            '/api/analytics',
            '/api/super-admin',
            '/health',
            '/up',
        ];

        $path = $request->getPathInfo();
        
        foreach ($excludePaths as $excludePath) {
            if (str_starts_with($path, $excludePath)) {
                return false;
            }
        }

        // Track important events
        $trackableActions = [
            'page_view',
            'login_attempt',
            'registration',
            'api_call'
        ];

        return in_array($action, $trackableActions);
    }

    private function trackAnalyticsEvent(Request $request, $userId, $companyId, string $action)
    {
        $eventType = match($action) {
            'page_view' => 'page_view',
            'login_attempt' => 'authentication',
            'registration' => 'user_registration',
            'api_call' => 'api_usage',
            default => 'user_action'
        };

        $eventCategory = match($action) {
            'page_view' => 'navigation',
            'login_attempt' => 'authentication',
            'registration' => 'user_management',
            'api_call' => 'api',
            default => 'interaction'
        };

        Analytics::create([
            'event_type' => $eventType,
            'event_category' => $eventCategory,
            'event_action' => $action,
            'event_label' => $request->getPathInfo(),
            'user_id' => $userId,
            'company_id' => $companyId,
            'session_id' => session()->getId(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'page_url' => $request->fullUrl(),
            'referrer' => $request->headers->get('referer'),
            'metadata' => [
                'method' => $request->getMethod(),
                'query_params' => $request->query(),
                'is_ajax' => $request->ajax(),
                'is_mobile' => $this->isMobile($request->userAgent()),
            ],
        ]);
    }

    private function sanitizeRequestData(Request $request): array
    {
        $data = $request->all();
        
        // Remove sensitive data
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'current_password',
            'token',
            'api_key',
            'secret',
            'credit_card',
            'cvv',
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }

    private function isMobile(string $userAgent = null): bool
    {
        if (!$userAgent) {
            return false;
        }

        $mobileKeywords = [
            'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 
            'BlackBerry', 'Windows Phone', 'Opera Mini'
        ];

        foreach ($mobileKeywords as $keyword) {
            if (str_contains($userAgent, $keyword)) {
                return true;
            }
        }

        return false;
    }
}
