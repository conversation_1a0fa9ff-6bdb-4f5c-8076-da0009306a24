<?php

namespace App\Services;

use Google\Analytics\Data\V1beta\BetaAnalyticsDataClient;
use Google\Analytics\Data\V1beta\DateRange;
use Google\Analytics\Data\V1beta\Dimension;
use Google\Analytics\Data\V1beta\Metric;
use Google\Analytics\Data\V1beta\RunReportRequest;
use Carbon\Carbon;

class GoogleAnalyticsService
{
    private $client;
    private $propertyId;

    public function __construct()
    {
        $this->propertyId = config('services.google_analytics.property_id');
        
        if (config('services.google_analytics.credentials_path')) {
            putenv('GOOGLE_APPLICATION_CREDENTIALS=' . config('services.google_analytics.credentials_path'));
            $this->client = new BetaAnalyticsDataClient();
        }
    }

    public function isConfigured(): bool
    {
        return !empty($this->propertyId) && !empty($this->client);
    }

    /**
     * Get basic analytics data
     */
    public function getBasicAnalytics($startDate = null, $endDate = null): array
    {
        if (!$this->isConfigured()) {
            return $this->getMockData();
        }

        try {
            $startDate = $startDate ?? Carbon::now()->subDays(30)->toDateString();
            $endDate = $endDate ?? Carbon::now()->toDateString();

            $request = (new RunReportRequest())
                ->setProperty('properties/' . $this->propertyId)
                ->setDateRanges([
                    new DateRange([
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                    ]),
                ])
                ->setMetrics([
                    new Metric(['name' => 'activeUsers']),
                    new Metric(['name' => 'sessions']),
                    new Metric(['name' => 'screenPageViews']),
                    new Metric(['name' => 'bounceRate']),
                    new Metric(['name' => 'averageSessionDuration']),
                ]);

            $response = $this->client->runReport($request);
            
            return $this->formatBasicAnalytics($response);
            
        } catch (\Exception $e) {
            \Log::error('Google Analytics API error: ' . $e->getMessage());
            return $this->getMockData();
        }
    }

    /**
     * Get page views over time
     */
    public function getPageViewsOverTime($startDate = null, $endDate = null): array
    {
        if (!$this->isConfigured()) {
            return $this->getMockPageViews();
        }

        try {
            $startDate = $startDate ?? Carbon::now()->subDays(30)->toDateString();
            $endDate = $endDate ?? Carbon::now()->toDateString();

            $request = (new RunReportRequest())
                ->setProperty('properties/' . $this->propertyId)
                ->setDateRanges([
                    new DateRange([
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                    ]),
                ])
                ->setDimensions([
                    new Dimension(['name' => 'date']),
                ])
                ->setMetrics([
                    new Metric(['name' => 'screenPageViews']),
                    new Metric(['name' => 'activeUsers']),
                ]);

            $response = $this->client->runReport($request);
            
            return $this->formatPageViewsOverTime($response);
            
        } catch (\Exception $e) {
            \Log::error('Google Analytics API error: ' . $e->getMessage());
            return $this->getMockPageViews();
        }
    }

    /**
     * Get top pages
     */
    public function getTopPages($startDate = null, $endDate = null, $limit = 10): array
    {
        if (!$this->isConfigured()) {
            return $this->getMockTopPages();
        }

        try {
            $startDate = $startDate ?? Carbon::now()->subDays(30)->toDateString();
            $endDate = $endDate ?? Carbon::now()->toDateString();

            $request = (new RunReportRequest())
                ->setProperty('properties/' . $this->propertyId)
                ->setDateRanges([
                    new DateRange([
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                    ]),
                ])
                ->setDimensions([
                    new Dimension(['name' => 'pagePath']),
                    new Dimension(['name' => 'pageTitle']),
                ])
                ->setMetrics([
                    new Metric(['name' => 'screenPageViews']),
                    new Metric(['name' => 'activeUsers']),
                ])
                ->setLimit($limit);

            $response = $this->client->runReport($request);
            
            return $this->formatTopPages($response);
            
        } catch (\Exception $e) {
            \Log::error('Google Analytics API error: ' . $e->getMessage());
            return $this->getMockTopPages();
        }
    }

    /**
     * Get user demographics
     */
    public function getUserDemographics($startDate = null, $endDate = null): array
    {
        if (!$this->isConfigured()) {
            return $this->getMockDemographics();
        }

        try {
            $startDate = $startDate ?? Carbon::now()->subDays(30)->toDateString();
            $endDate = $endDate ?? Carbon::now()->toDateString();

            $request = (new RunReportRequest())
                ->setProperty('properties/' . $this->propertyId)
                ->setDateRanges([
                    new DateRange([
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                    ]),
                ])
                ->setDimensions([
                    new Dimension(['name' => 'country']),
                    new Dimension(['name' => 'deviceCategory']),
                ])
                ->setMetrics([
                    new Metric(['name' => 'activeUsers']),
                    new Metric(['name' => 'sessions']),
                ]);

            $response = $this->client->runReport($request);
            
            return $this->formatDemographics($response);
            
        } catch (\Exception $e) {
            \Log::error('Google Analytics API error: ' . $e->getMessage());
            return $this->getMockDemographics();
        }
    }

    private function formatBasicAnalytics($response): array
    {
        $data = [
            'active_users' => 0,
            'sessions' => 0,
            'page_views' => 0,
            'bounce_rate' => 0,
            'avg_session_duration' => 0,
        ];

        if ($response->getRows()->count() > 0) {
            $row = $response->getRows()[0];
            $metrics = $row->getMetricValues();
            
            $data['active_users'] = (int) $metrics[0]->getValue();
            $data['sessions'] = (int) $metrics[1]->getValue();
            $data['page_views'] = (int) $metrics[2]->getValue();
            $data['bounce_rate'] = round((float) $metrics[3]->getValue() * 100, 2);
            $data['avg_session_duration'] = round((float) $metrics[4]->getValue(), 2);
        }

        return $data;
    }

    private function formatPageViewsOverTime($response): array
    {
        $data = [];
        
        foreach ($response->getRows() as $row) {
            $dimensions = $row->getDimensionValues();
            $metrics = $row->getMetricValues();
            
            $data[] = [
                'date' => $dimensions[0]->getValue(),
                'page_views' => (int) $metrics[0]->getValue(),
                'active_users' => (int) $metrics[1]->getValue(),
            ];
        }

        return $data;
    }

    private function formatTopPages($response): array
    {
        $data = [];
        
        foreach ($response->getRows() as $row) {
            $dimensions = $row->getDimensionValues();
            $metrics = $row->getMetricValues();
            
            $data[] = [
                'path' => $dimensions[0]->getValue(),
                'title' => $dimensions[1]->getValue(),
                'page_views' => (int) $metrics[0]->getValue(),
                'active_users' => (int) $metrics[1]->getValue(),
            ];
        }

        return $data;
    }

    private function formatDemographics($response): array
    {
        $countries = [];
        $devices = [];
        
        foreach ($response->getRows() as $row) {
            $dimensions = $row->getDimensionValues();
            $metrics = $row->getMetricValues();
            
            $country = $dimensions[0]->getValue();
            $device = $dimensions[1]->getValue();
            $users = (int) $metrics[0]->getValue();
            
            if (!isset($countries[$country])) {
                $countries[$country] = 0;
            }
            $countries[$country] += $users;
            
            if (!isset($devices[$device])) {
                $devices[$device] = 0;
            }
            $devices[$device] += $users;
        }

        return [
            'countries' => $countries,
            'devices' => $devices,
        ];
    }

    // Mock data for when GA is not configured
    private function getMockData(): array
    {
        return [
            'active_users' => rand(100, 1000),
            'sessions' => rand(150, 1200),
            'page_views' => rand(500, 5000),
            'bounce_rate' => rand(30, 70),
            'avg_session_duration' => rand(60, 300),
        ];
    }

    private function getMockPageViews(): array
    {
        $data = [];
        for ($i = 30; $i >= 0; $i--) {
            $data[] = [
                'date' => Carbon::now()->subDays($i)->toDateString(),
                'page_views' => rand(50, 500),
                'active_users' => rand(20, 200),
            ];
        }
        return $data;
    }

    private function getMockTopPages(): array
    {
        return [
            ['path' => '/', 'title' => 'Home', 'page_views' => 1500, 'active_users' => 800],
            ['path' => '/servicos', 'title' => 'Serviços', 'page_views' => 800, 'active_users' => 400],
            ['path' => '/login', 'title' => 'Login', 'page_views' => 600, 'active_users' => 300],
            ['path' => '/agenda', 'title' => 'Agenda', 'page_views' => 400, 'active_users' => 200],
            ['path' => '/perfil', 'title' => 'Perfil', 'page_views' => 300, 'active_users' => 150],
        ];
    }

    private function getMockDemographics(): array
    {
        return [
            'countries' => [
                'Brazil' => 800,
                'United States' => 100,
                'Portugal' => 50,
                'Argentina' => 30,
            ],
            'devices' => [
                'mobile' => 600,
                'desktop' => 300,
                'tablet' => 80,
            ],
        ];
    }
}
