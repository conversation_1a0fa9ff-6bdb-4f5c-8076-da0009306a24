<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Analytics de Usuários</h1>
          <div class="flex gap-2">
            <select-base
              v-model="selectedPeriod"
              :options="periodOptions"
              class="min-w-48"
            />
            <base-button @click="refreshData" :loading="loading" size="sm">
              <ArrowPathIcon class="w-4 h-4 mr-2" />
              Atualizar
            </base-button>
          </div>
        </div>

        <div v-if="loading" class="flex justify-center py-20">
          <loading />
        </div>

        <div v-else class="space-y-6">
          <!-- User Stats Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                  <UsersIcon class="w-6 h-6 text-blue-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-blue-800">{{ userStats.total_users || 0 }}</p>
                  <p class="text-sm text-gray-600">Total de Usuários</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                  <UserGroupIcon class="w-6 h-6 text-green-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-green-800">{{ userStats.active_users || 0 }}</p>
                  <p class="text-sm text-gray-600">Usuários Ativos</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                  <UserGroupIcon class="w-6 h-6 text-purple-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-purple-800">{{ userStats.new_users || 0 }}</p>
                  <p class="text-sm text-gray-600">Novos Usuários</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                  <UserGroupIcon class="w-6 h-6 text-yellow-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-yellow-800">{{ userStats.retention_rate || 0 }}%</p>
                  <p class="text-sm text-gray-600">Taxa de Retenção</p>
                </div>
              </div>
            </div>
          </div>

          <!-- User Analytics Content -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Analytics de Usuários</h2>
            <p class="text-gray-600">Dados detalhados de usuários serão exibidos aqui.</p>
            <div class="mt-4 text-center py-8 text-gray-500">
              📊 Gráficos e estatísticas de usuários em desenvolvimento
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon, UsersIcon, UserGroupIcon } from "@heroicons/vue/24/solid";
import { useToast } from "vue-toast-notification";
import { api, getAnalyticsEndpoint } from "~/server/api";

definePageMeta({
  layout: 'professionals'
});

const toast = useToast();
const loading = ref(false);
const selectedPeriod = ref('week');
const userStats = ref({
  total_users: 0,
  active_users: 0,
  new_users: 0,
  retention_rate: 0
});

const periodOptions = [
  { value: 'day', label: 'Últimos 7 dias' },
  { value: 'week', label: 'Últimas 8 semanas' },
  { value: 'month', label: 'Últimos 12 meses' },
  { value: 'year', label: 'Últimos 5 anos' }
];

async function loadUserStats() {
  try {
    loading.value = true;
    const { data } = await api.get(getAnalyticsEndpoint('/users'), {
      params: { period: selectedPeriod.value }
    });
    userStats.value = data;
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados de usuários');
  } finally {
    loading.value = false;
  }
}

async function refreshData() {
  await loadUserStats();
  toast.success('Dados atualizados!');
}

onMounted(() => {
  loadUserStats();
});
</script>
