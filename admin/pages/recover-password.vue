<template>
  <div class="min-h-screen flex items-center justify-center bg-base-200">
    <div class="w-full max-w-md p-8 shadow-lg bg-base-100 rounded-lg">
      <h2 class="text-2xl font-bold text-center mb-6"><PERSON><PERSON><PERSON><PERSON> senha</h2>
      <form @submit.prevent="submit" class="flex flex-col gap-3">
        <input-base
          type="email"
          v-model="email"
          label="Confirme seu email"
          :error="errors['email']"
        />
        <input-base
          type="password"
          v-model="password"
          :error="errors['password']"
          label="Digite sua nova senha"
        />
        <input-base
          type="password"
          v-model="password_confirmation"
          label="Confirme sua nova senha"
          :error="errors['password_confirmation']"
        />

        <base-button :loading type="submit" class="btn btn-primary w-full">
          Redefinir senha
        </base-button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as yup from "yup";
import { api } from "~/server/api";
import { useForm } from "vee-validate";
import { useToast } from "vue-toast-notification";
const route = useRoute();

const validationSchema = yup.object().shape({
  email: yup.string().required("Campo obrigatório").email("Email inválido"),
  password_confirmation: yup.string().required("Campo obrigatório"),
  password: yup
    .string()
    .required()
    .min(8, "A senha deve ter no mínimo 8 caracteres"),
});
const { handleSubmit, errors, defineField } = useForm({
  validationSchema,
});
const [email] = defineField("email");
const [password] = defineField("password");
const [password_confirmation] = defineField("password_confirmation");
const toast = useToast();
const loading = ref(false);
const router = useRouter();
const submitForgotPassword = async (values: any) => {
  loading.value = true; 
  try {
    const token = route.query.token;
    values.token = token;
    await api.post("/reset-password", values);
    toast.success("Senha alterada");
    router.push("/login");
  } catch {
    toast.error("Ocorreu um erro");
  } finally {
    loading.value = false;
  }
};
const submit = handleSubmit(submitForgotPassword);
</script>
