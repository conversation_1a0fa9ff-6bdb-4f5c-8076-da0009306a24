class FinancialDashboard {
    constructor() {
        this.currentTab = 'overview';
        this.currentFilters = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Filter controls
        const applyFiltersBtn = document.getElementById('apply-filters');
        const clearFiltersBtn = document.getElementById('clear-filters');
        const retryBtn = document.getElementById('retry-btn');

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.loadCurrentTabData();
            });
        }

        // Upload form
        const uploadForm = document.getElementById('upload-form');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFileUpload();
            });
        }
    }

    switchTab(tabName) {
        // Update active tab button
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update active tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;
        this.loadCurrentTabData();
    }

    async loadInitialData() {
        try {
            Utils.showLoading();
            const data = await financialAPI.getOverview();
            
            if (data && data.available_periods) {
                Utils.populateYearFilter(data.available_periods);
            }
            
            this.loadCurrentTabData();
        } catch (error) {
            console.error('Error loading initial data:', error);
            Utils.showError('Failed to load initial data. Please try again.');
        } finally {
            Utils.hideLoading();
        }
    }

    async loadCurrentTabData() {
        try {
            Utils.showLoading();
            Utils.hideError();

            switch (this.currentTab) {
                case 'overview':
                    await this.loadOverviewData();
                    break;
                case 'payment-analysis':
                    await this.loadPaymentAnalysisData();
                    break;
                case 'cash-flow':
                    await this.loadCashFlowData();
                    break;
                case 'dre':
                    await this.loadDreData();
                    break;
                case 'upload':
                    // No data loading needed for upload tab
                    break;
            }
        } catch (error) {
            console.error(`Error loading ${this.currentTab} data:`, error);
            Utils.showError(`Failed to load ${this.currentTab} data. Please try again.`);
        } finally {
            Utils.hideLoading();
        }
    }

    async loadOverviewData() {
        const data = await financialAPI.getOverview(this.currentFilters);
        
        if (data) {
            this.updateOverviewSummary(data.summary);
            chartManager.createOverviewChart(data);
        }
    }

    async loadPaymentAnalysisData() {
        const data = await financialAPI.getPaymentAnalysis(this.currentFilters);
        
        if (data) {
            this.updatePaymentAnalysisTable(data.data);
            if (data.chart_data) {
                chartManager.createPaymentPieChart(data.chart_data);
                chartManager.createPaymentBarChart(data.chart_data);
            }
        }
    }

    async loadCashFlowData() {
        const data = await financialAPI.getCashFlow(this.currentFilters);
        
        if (data) {
            this.updateCashFlowTable(data.data);
            if (data.chart_data) {
                chartManager.createCashFlowChart(data.chart_data);
            }
        }
    }

    async loadDreData() {
        const data = await financialAPI.getDre(this.currentFilters);
        
        if (data) {
            this.updateDreTable(data.data);
            if (data.chart_data) {
                chartManager.createDreChart(data.chart_data);
            }
        }
    }

    updateOverviewSummary(summary) {
        if (!summary) return;

        let totalRecords = 0;
        let totalValue = 0;
        let avgValue = 0;
        let latestPeriod = '';

        Object.values(summary).forEach(item => {
            totalRecords += item.total_records || 0;
            totalValue += item.total_value || 0;
            if (item.latest_period && item.latest_period > latestPeriod) {
                latestPeriod = item.latest_period;
            }
        });

        avgValue = totalRecords > 0 ? totalValue / totalRecords : 0;

        document.getElementById('total-records').textContent = totalRecords.toLocaleString();
        document.getElementById('total-value').textContent = Utils.formatCurrency(totalValue);
        document.getElementById('avg-value').textContent = Utils.formatCurrency(avgValue);
        document.getElementById('latest-period').textContent = Utils.formatDate(latestPeriod);
    }

    updatePaymentAnalysisTable(data) {
        const tbody = document.querySelector('#payment-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        data.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.category}</td>
                <td>${Utils.formatCurrency(item.value)}</td>
                <td>${Utils.formatPercentage(item.percentage)}</td>
                <td>${Utils.formatDate(item.period)}</td>
            `;
            tbody.appendChild(row);
        });
    }

    updateCashFlowTable(data) {
        const tbody = document.querySelector('#cash-flow-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        data.forEach(item => {
            const predicted = item.data?.predicted || 0;
            const actual = item.data?.actual || 0;
            const variance = actual - predicted;
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.category}</td>
                <td>${Utils.formatCurrency(predicted)}</td>
                <td>${Utils.formatCurrency(actual)}</td>
                <td class="${variance >= 0 ? 'positive' : 'negative'}">${Utils.formatCurrency(variance)}</td>
                <td>${Utils.formatDate(item.period)}</td>
            `;
            tbody.appendChild(row);
        });
    }

    updateDreTable(data) {
        const tbody = document.querySelector('#dre-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        data.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.category}</td>
                <td>${Utils.formatCurrency(item.value)}</td>
                <td>${Utils.formatDate(item.period)}</td>
            `;
            tbody.appendChild(row);
        });
    }

    applyFilters() {
        this.currentFilters = Utils.getFilters();
        this.loadCurrentTabData();
    }

    clearFilters() {
        Utils.clearFilters();
        this.currentFilters = {};
        this.loadCurrentTabData();
    }

    async handleFileUpload() {
        const fileInput = document.getElementById('csv-file');
        const typeSelect = document.getElementById('data-type');
        const periodInput = document.getElementById('period');

        const file = fileInput.files[0];
        const type = typeSelect.value;
        const period = periodInput.value;

        if (!file || !type || !period) {
            Utils.showUploadResult('Please fill in all fields.', false);
            return;
        }

        try {
            Utils.showLoading();
            const result = await financialAPI.uploadCsv(file, type, period);
            
            if (result) {
                Utils.showUploadResult('File uploaded successfully!', true);
                
                // Reset form
                document.getElementById('upload-form').reset();
                
                // Refresh data if we're on a relevant tab
                if (this.currentTab === type.replace('_', '-') || this.currentTab === 'overview') {
                    setTimeout(() => {
                        this.loadCurrentTabData();
                    }, 1000);
                }
            }
        } catch (error) {
            console.error('Upload error:', error);
            Utils.showUploadResult('Failed to upload file. Please try again.', false);
        } finally {
            Utils.hideLoading();
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (auth.isAuthenticated()) {
        new FinancialDashboard();
    }
});
