<template>
    <form class="flex flex-col gap-3" @submit="submit">
        <div class="flex flex-col sm:grid sm:grid-cols-2 gap-3 w-full">
            <input-base class="col-span-2" v-model="name" :error="errors['name']" label="Nome" />
            <input-base v-model="email" :error="errors['email']" label="Email" class="" :disabled="fixedField === 'email'" />
            <input-base v-model="phone" :error="errors['phone']" label="Telefone" data-maska="## #####-####" class="" :disabled="fixedField === 'phone'" />
            <div class="flex w-full col-span-2 gap-3">
                <input-base :error="errors['password']" v-model="password" label="Senha" type="password" />
                <input-base :error="errors['password_confirmation']" v-model="password_confirmation" label="Confirmar senha"
                    type="password" />
            </div>
        </div>
        <base-button :loading="loading" type="submit" class="mt-2 w-full"> Completar Cadastro </base-button>
    </form>
</template>
<script setup lang="ts">
import { object, string, ref as yupRef } from "yup";
import { useForm } from "vee-validate";
import { onMounted, ref } from "vue";

const emit = defineEmits(["submit"]);
const validationSchema = object().shape({
    name: string().required("O nome é obrigatório"),
    email: string().email("Email inválido").required("Email é obrigatório"),
    phone: string().required("Número de telefone é obrigatório"),
    password: string()
        .required("A senha é obrigatória")
        .min(6, "A senha deve ter no mínimo 6 caracteres"),
    password_confirmation: string()
        .oneOf([yupRef("password")], "As senhas devem ser iguais")
        .required("Confirmação de senha é obrigatória"),
});

const { handleSubmit, defineField, errors, setFieldValue } = useForm({
    validationSchema,
});

const { userData, loading } = defineProps({
    userData: {
        type: Object,
        default: () => ({}),
    },
    loading: {
        type: Boolean,
        default: false,
    },
});

const [email] = defineField("email");
const [password] = defineField("password");
const [password_confirmation] = defineField("password_confirmation");
const [name] = defineField("name");
const [phone] = defineField("phone");
const fixedField = ref("");

onMounted(() => {
    console.log("Form component received userData:", userData);
    if (userData) {
        if (userData.name) {
            console.log("Setting name:", userData.name);
            setFieldValue("name", userData.name);
        }
        if (userData.email) {
            console.log("Setting email:", userData.email);
            setFieldValue("email", userData.email);
        }
        if (userData.phone) {
            console.log("Setting phone:", userData.phone);
            setFieldValue("phone", userData.phone);
        }
        if (userData.fixed_field) {
            console.log("Setting fixed field:", userData.fixed_field);
            fixedField.value = userData.fixed_field;
        }
    } else {
        console.warn("No userData provided to the form component");
    }
});

function handleFormSubmit(values) {
    const formData = {
        ...values,
        company_id: userData?.company_id || null,
    };
    emit("submit", formData);
}

const submit = handleSubmit(handleFormSubmit);
</script>
