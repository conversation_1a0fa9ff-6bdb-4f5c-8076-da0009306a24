import { defineStore } from "pinia";
interface UserInfo {
  name: string;
  email: string;
  image: string;
  phone: string;
  role?: string;
}
export const useLoginStore = defineStore("login", {
  state: () => ({
    token: "",
    userInfo: {} as UserInfo,
    isSuperAdmin: false,
    superAdminToken: "", // Store super admin token in Pinia instead of localStorage
  }),
  getters: {
    isLoggedIn: (state) => !!(state.token.length || state.superAdminToken.length),
    hasAnalyticsAccess: (state) => {
      // Allow access for super admins and regular admins/professionals
      return state.isSuperAdmin || state.userInfo.role === 'admin' || state.userInfo.role === 'professional';
    },
    effectiveToken: (state) => state.superAdminToken || state.token,
  },
  actions: {
    login(token: string, isSuperAdmin = false) {
      if (isSuperAdmin) {
        this.superAdminToken = token;
        this.token = ""; // Clear regular token when using super admin
      } else {
        this.token = token;
        this.superAdminToken = ""; // Clear super admin token when using regular login
      }
      this.isSuperAdmin = isSuperAdmin;
      // Navigation is handled by the calling component
      // connectToSocket();
    },
    logout() {
      this.token = "";
      this.superAdminToken = "";
      this.userInfo = {} as UserInfo;
      this.isSuperAdmin = false;
      // disconnectFromSocket();
      // Navigate to login page
      navigateTo("/login");
    },
    setUser(user: UserInfo) {
      this.userInfo = user;
    },
    setSuperAdmin(isSuperAdmin: boolean) {
      this.isSuperAdmin = isSuperAdmin;
    },
    // Method to check if store needs hydration and restore state
    hydrateFromPersistedState() {
      // This method will be called by the auth plugin to ensure proper hydration
      // Pinia persistence should handle this automatically, but we can add logic here if needed
      if (this.superAdminToken && !this.isSuperAdmin) {
        this.isSuperAdmin = true;
      }
    },
  },
  persist: true,
});
