<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Financial Overview</h1>
          <div class="flex gap-2">
            <button @click="refreshData" class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
            <NuxtLink to="/analytics" class="btn btn-ghost btn-sm">
              ← Back to Dashboard
            </NuxtLink>
          </div>
        </div>

        <!-- Filter Controls -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
          <div class="flex flex-wrap gap-4 items-end">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Data Type</span>
              </label>
              <select v-model="filters.type" class="select select-bordered select-sm">
                <option value="">All Types</option>
                <option value="payment_analysis">Payment Analysis</option>
                <option value="cash_flow">Cash Flow</option>
                <option value="dre">DRE</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Year</span>
              </label>
              <select v-model="filters.year" class="select select-bordered select-sm">
                <option value="">All Years</option>
                <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Month</span>
              </label>
              <select v-model="filters.month" class="select select-bordered select-sm">
                <option value="">All Months</option>
                <option value="1">January</option>
                <option value="2">February</option>
                <option value="3">March</option>
                <option value="4">April</option>
                <option value="5">May</option>
                <option value="6">June</option>
                <option value="7">July</option>
                <option value="8">August</option>
                <option value="9">September</option>
                <option value="10">October</option>
                <option value="11">November</option>
                <option value="12">December</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Start Date</span>
              </label>
              <input v-model="filters.start_date" type="date" class="input input-bordered input-sm">
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">End Date</span>
              </label>
              <input v-model="filters.end_date" type="date" class="input input-bordered input-sm">
            </div>
            <button @click="applyFilters" class="btn btn-primary btn-sm">Apply</button>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">Clear</button>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-lg"></span>
        </div>

        <div v-else>
          <!-- Summary Cards by Type -->
          <div v-if="summaryByType" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div v-for="(summary, type) in summaryByType" :key="type" class="bg-white rounded-lg shadow p-6">
              <div class="stat">
                <div class="stat-title">{{ getTypeLabel(type) }}</div>
                <div class="stat-value text-primary">{{ summary.total_records || 0 }}</div>
                <div class="stat-desc">{{ formatCurrency(summary.total_value || 0) }}</div>
              </div>
            </div>
          </div>

          <!-- Overview Chart -->
          <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">Data Distribution by Type</h3>
            <div v-if="chartData.labels?.length" class="h-64">
              <canvas ref="overviewChart"></canvas>
            </div>
            <div v-else class="text-center py-8 text-gray-500">
              No data available
            </div>
          </div>

          <!-- Recent Data Table -->
          <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-6 border-b">
              <h3 class="text-lg font-semibold">Recent Financial Data</h3>
            </div>
            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Category</th>
                    <th>Value</th>
                    <th>Period</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="!overviewData.data?.length">
                    <td colspan="4" class="text-center py-8 text-gray-500">
                      No data available
                    </td>
                  </tr>
                  <tr v-for="item in overviewData.data?.slice(0, 20)" :key="item.id">
                    <td>
                      <span class="badge badge-outline">{{ getTypeLabel(item.type) }}</span>
                    </td>
                    <td class="font-medium">{{ item.category }}</td>
                    <td>{{ formatCurrency(item.value) }}</td>
                    <td>{{ formatDate(item.period) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

definePageMeta({
  layout: 'professionals'
});

const toast = useToast();
const loading = ref(false);
const overviewData = ref({ data: [] });
const summaryByType = ref(null);
const chartData = ref({ labels: [], datasets: [] });
const availableYears = ref([]);

const filters = ref({
  type: '',
  year: '',
  month: '',
  start_date: '',
  end_date: ''
});

const overviewChart = ref(null);
let overviewChartInstance = null;

async function loadOverviewData() {
  try {
    loading.value = true;
    const params = Object.fromEntries(
      Object.entries(filters.value).filter(([_, value]) => value !== '')
    );
    
    const { data } = await api.get('/financial/', { params });
    overviewData.value = data;
    summaryByType.value = data.summary;
    
    if (data.available_periods) {
      availableYears.value = [...new Set(data.available_periods.map((p: any) => p.year))].sort((a, b) => b - a);
    }
    
    // Prepare chart data
    prepareChartData(data.summary);
    
    nextTick(() => {
      createChart();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados do overview');
  } finally {
    loading.value = false;
  }
}

function prepareChartData(summary) {
  if (!summary) {
    chartData.value = { labels: [], datasets: [] };
    return;
  }

  const labels = [];
  const values = [];
  const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'];

  Object.entries(summary).forEach(([type, data]: [string, any], index) => {
    labels.push(getTypeLabel(type));
    values.push(Math.abs(data.total_value || 0));
  });

  chartData.value = {
    labels,
    datasets: [{
      data: values,
      backgroundColor: colors.slice(0, labels.length),
      borderWidth: 2,
      borderColor: '#fff'
    }]
  };
}

function createChart() {
  if (overviewChartInstance) {
    overviewChartInstance.destroy();
  }

  if (!overviewChart.value || !chartData.value.labels?.length) return;

  const ctx = overviewChart.value.getContext('2d');
  
  overviewChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: chartData.value,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = formatCurrency(context.parsed);
              return `${label}: ${value}`;
            }
          }
        }
      }
    }
  });
}

function applyFilters() {
  loadOverviewData();
}

function clearFilters() {
  filters.value = {
    type: '',
    year: '',
    month: '',
    start_date: '',
    end_date: ''
  };
  loadOverviewData();
}

function refreshData() {
  loadOverviewData();
}

function getTypeLabel(type: string) {
  const labels = {
    'payment_analysis': 'Payment Analysis',
    'cash_flow': 'Cash Flow',
    'dre': 'DRE'
  };
  return labels[type] || type;
}

function formatCurrency(value: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

function formatDate(dateString: string) {
  if (!dateString) return '-';
  return new Intl.DateTimeFormat('pt-BR').format(new Date(dateString));
}

onMounted(() => {
  loadOverviewData();
});

onUnmounted(() => {
  if (overviewChartInstance) {
    overviewChartInstance.destroy();
  }
});
</script>
