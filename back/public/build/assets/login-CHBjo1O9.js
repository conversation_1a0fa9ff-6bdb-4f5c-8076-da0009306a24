import{_ as A}from"./BaseButton-CxEL1ccK.js";import{a as D,_ as N}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{c as T,a as S,u as U}from"./vee-validate-BgJnH8aY.js";import{l as $,r as h,A as B,o as _,c as C,b as u,u as n,y as k,a as t,w,i as F,d as I,q as P,j as q,g as L,B as j,N as V,z}from"./app-BkKCG4YJ.js";import{b as H,c as O,_ as R,a as M}from"./BaseLayout--Fu-QUOh.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const W={class:"text-right text-primary"},G=$({__name:"Form",props:{slug:{type:String},loading:{type:Boolean}},emits:["submit"],setup(i,{emit:d}){H();const r=h(!1),g=d,b=T().shape({identifier:S().required("Campo obrigatório").when(([],p)=>{if(!r.value)return p.email("Email inválido")}),password:S().required("Campo obrigatório")}),{handleSubmit:x,defineField:l,errors:c}=U({validationSchema:b}),[s]=l("identifier"),[e]=l("password");function a(){r.value=s.value.includes("(")||/^\d+$/.test(s.value.replace(/\D/g,""))}function v(p){const{identifier:o,password:f}=p;g("submit",{identifier:o,isPhone:r.value,password:f})}B(s,()=>{a()});const y=x(v);return(p,o)=>{const f=D,E=A;return _(),C("form",{class:"flex flex-col gap-3",onSubmit:o[2]||(o[2]=(...m)=>n(y)&&n(y)(...m))},[u(f,{modelValue:n(s),"onUpdate:modelValue":o[0]||(o[0]=m=>k(s)?s.value=m:null),name:"identifier",error:n(c).identifier,label:"Email ou Telefone","data-maska":r.value?"(##) #####-####":void 0},null,8,["modelValue","error","data-maska"]),u(f,{error:n(c).password,modelValue:n(e),"onUpdate:modelValue":o[1]||(o[1]=m=>k(e)?e.value=m:null),name:"password",label:"Senha",type:"password"},null,8,["error","modelValue"]),t("div",W,[u(n(F),{href:"register"},{default:w(()=>o[3]||(o[3]=[t("span",{class:"inline-block font-black hover:text-primary hover:underline hover:cursor-pointer transition duration-200"}," Cadastre-se ",-1)])),_:1})]),u(E,{type:"submit",class:"mt-2 w-full",loading:i.loading},{default:w(()=>o[4]||(o[4]=[I(" Entrar ")])),_:1},8,["loading"])],32)}}}),J={class:"flex items-center justify-center h-full"},K={class:"card mx-auto w-full max-w-5xl shadow-xl"},Q={class:"grid md:grid-cols-2 grid-cols-1 px-2 rounded-xl"},X={class:"pt-4 pb-6 sm:py-12 px-4 sm:px-10 bg-base-200 rounded-xl md:rounded-l-none md:rounded-r-xl"},Y={key:0,class:"text-red-500 font-bold bg-white rounded-xl text-center mb-2"},ie=$({__name:"login",props:{slug:{type:String},company:{type:Object}},setup(i){const d=h(!1),r=P.useToast(),g=O(),b={"E_INVALID_AUTH_PASSWORD: Password mis-match":"Senha incorreta","E_INVALID_AUTH_UID: User not found":"Usuário não encontrado"};function x(s){return b[s]||s}const l=h(!1);async function c(s){var e;try{const a=route().queryParams.redirect||i.slug?"servicos":"/";console.log(a),d.value=!0,l.value=!1;const{data:v}=await M.post("/login",s);g.login(v.token),r.success("Login realizado com sucesso!",{position:"top"}),i.slug?V.get(`/${i.company.slug}/${a}?checkForm=true`):V.get(`/${a}`)}catch(a){l.value=!0,z.isAxiosError(a)&&r.error(x((e=a.response)==null?void 0:e.data.errors[0].message),{position:"top"})}finally{d.value=!1}}return(s,e)=>{const a=G;return _(),q(j(i.slug?N:R),{slug:i.slug,company:i.company},{default:w(()=>[t("div",J,[t("div",K,[e[2]||(e[2]=t("div",{class:"hero min-h-full rounded-l-xl px-4 hidden md:hidden"},[t("img",{src:"/icons/logo.png",alt:"Dashwind Admin Template",class:"w-72 inline-block mb-10 -mt-16 md:pt-0"})],-1)),t("div",Q,[e[1]||(e[1]=t("div",{class:"hero min-h-full rounded-l-xl bg-base-200"},[t("img",{src:"/icons/logo.png",alt:"Dashwind Admin Template",size:"512",class:"w-96 md:pt-0 hidden md:block"})],-1)),t("div",X,[e[0]||(e[0]=t("h2",{class:"text-2xl font-semibold mb-2 text-center"}," Login ",-1)),n(l)?(_(),C("div",Y," Email e senha não coincidem ")):L("",!0),u(a,{onSubmit:c,loading:n(d)},null,8,["loading"])])])])])]),_:1},8,["slug","company"])}}});export{ie as default};
