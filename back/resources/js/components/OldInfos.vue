<!-- <div class="flex justify-center space-x-4">
                    <button
                        v-if="company.address || company.phone || company.email"
                        @click="activeTab = 'contact'"
                        :class="{
                            'btn btn-primary': activeTab === 'contact',
                            'btn-outline text-primary': activeTab !== 'contact',
                        }"
                        class="py-2 px-4 rounded"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="size-6"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                            />
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"
                            />
                        </svg>
                    </button>

                    <button
                        @click="activeTab = 'about'"
                        :class="{
                            'btn btn-primary': activeTab === 'about',
                            'btn-outline text-primary': activeTab !== 'about',
                        }"
                        class="py-2 px-4 rounded"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="size-6"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                            />
                        </svg>
                    </button>

                    <button
                        v-if="company.socialMedias"
                        @click="activeTab = 'social'"
                        :class="{
                            'btn btn-primary': activeTab === 'social',
                            'btn-outline text-primary': activeTab !== 'social',
                        }"
                        class="py-2 px-4 rounded"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="size-6"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
                            />
                        </svg>
                    </button>
                </div> -->

<!-- <div
                    v-if="activeTab === 'contact'"
                    class="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-6"
                >
                    <div
                        v-if="company.address"
                        class="flex items-center text-sm text-gray-700 md:justify-center"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            class="size-6 mr-4"
                        >
                            <path
                                fill-rule="evenodd"
                                d="m11.54 22.351.07.04.028.016a.76.76 0 0 0 .723 0l.028-.015.071-.041a16.975 16.975 0 0 0 1.144-.742 19.58 19.58 0 0 0 2.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 0 0-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 0 0 2.682 2.282 16.975 16.975 0 0 0 1.145.742ZM12 13.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
                                clip-rule="evenodd"
                            />
                        </svg>

                        <div>
                            <h3 class="font-medium">Endereço</h3>
                            <p>{{ company.address }}</p>
                        </div>
                    </div>

                    <div
                        v-if="company.phone"
                        class="flex items-center text-sm text-gray-700 md:justify-center"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            class="size-6 mr-4"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z"
                                clip-rule="evenodd"
                            />
                        </svg>

                        <div>
                            <h3 class="font-medium">Telefone</h3>
                            <p>{{ company.phone.join(", ") }}</p>
                        </div>
                    </div>

                    <div
                        v-if="company.email"
                        class="flex items-center text-sm text-gray-700 md:justify-center"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            class="size-6 mr-4"
                        >
                            <path
                                d="M1.5 8.67v8.58a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V8.67l-8.928 5.493a3 3 0 0 1-3.144 0L1.5 8.67Z"
                            />
                            <path
                                d="M22.5 6.908V6.75a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3v.158l9.714 5.978a1.5 1.5 0 0 0 1.572 0L22.5 6.908Z"
                            />
                        </svg>

                        <div>
                            <h3 class="font-medium">Email</h3>
                            <p>{{ company.email }}</p>
                        </div>
                    </div>
                </div> -->

<!-- <div
                    v-if="activeTab === 'about'"
                    class="mt-6 text-sm text-gray-700"
                >
                    <div v-if="company.rating" class="flex items-center mb-4">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="#FFD700"
                            class="size-6"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z"
                                clip-rule="evenodd"
                            />
                        </svg>

                        <p class="font-medium text-lg">
                            {{ company.rating.value }}
                            <span class="text-sm text-gray-500">
                                ({{ company.rating.total }} avaliações)
                            </span>
                        </p>
                    </div>

                    <p v-if="company.about">
                        {{ truncatedAbout }}
                        <button
                            v-if="company.about.length > maxAboutLength"
                            @click="toggleText"
                            class="text-primary mt-2 ml-1"
                        >
                            {{
                                showFullText ? "Mostrar Menos" : "Mostrar Mais"
                            }}
                        </button>
                    </p>

                    <div
                        v-if="businessHours && businessHours.length"
                        class="mt-4 max-w-full overflow-x-hidden"
                    >
                        <h3 class="font-medium text-lg mb-4 text-center">
                            Horários de Funcionamento
                        </h3>
                        <div
                            class="hidden sm:grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-3"
                        >
                            <div
                                v-for="(schedule, index) in businessHours"
                                :key="index"
                                class="p-4 border rounded-lg text-center shadow-md"
                                :class="
                                    schedule.hour === 'Fechado'
                                        ? 'bg-red-100 text-red-600'
                                        : 'bg-green-100 text-green-600'
                                "
                            >
                                <h4 class="font-bold text-lg">
                                    {{ schedule.day }}
                                </h4>
                                <p class="mt-2 text-base">
                                    {{ schedule.hour }}
                                </p>
                            </div>
                        </div>

                        <div class="sm:hidden">
                            <div
                                class="carousel carousel-end space-x-4 max-w-full"
                            >
                                <div
                                    v-for="(schedule, index) in businessHours"
                                    :key="index"
                                    class="carousel-item w-2/5 p-4 border rounded-lg text-center shadow-md flex flex-col"
                                    :class="
                                        schedule.hour === 'Fechado'
                                            ? 'bg-red-100 text-red-600'
                                            : 'bg-green-100 text-green-600'
                                    "
                                >
                                    <h4 class="font-bold text-lg">
                                        {{ schedule.day }}
                                    </h4>
                                    <p class="mt-2 text-base">
                                        {{ schedule.hour }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    v-if="activeTab === 'social'"
                    class="flex mt-6 text-center space-x-4 justify-center"
                >
                    <div
                        v-if="
                            company.socialMedias &&
                            company.socialMedias.facebook
                        "
                    >
                        <a
                            :href="company.socialMedias.facebook"
                            target="_blank"
                            class="inline-block p-6 rounded-lg bg-blue-600 hover:bg-blue-500 text-white w-20 h-20 flex items-center justify-center"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                x="0px"
                                y="0px"
                                width="100"
                                height="100"
                                viewBox="0 0 50 50"
                                style="fill: #ffffff"
                            >
                                <path
                                    d="M25,3C12.85,3,3,12.85,3,25c0,11.03,8.125,20.137,18.712,21.728V30.831h-5.443v-5.783h5.443v-3.848 c0-6.371,3.104-9.168,8.399-9.168c2.536,0,3.877,0.188,4.512,0.274v5.048h-3.612c-2.248,0-3.033,2.131-3.033,4.533v3.161h6.588 l-0.894,5.783h-5.694v15.944C38.716,45.318,47,36.137,47,25C47,12.85,37.15,3,25,3z"
                                ></path>
                            </svg>
                        </a>
                    </div>
                    <div
                        v-if="
                            company.socialMedias &&
                            company.socialMedias.instagram
                        "
                    >
                        <a
                            :href="company.socialMedias.instagram"
                            target="_blank"
                            class="inline-block p-6 rounded-lg bg-pink-600 hover:bg-pink-500 text-white w-20 h-20 flex items-center justify-center"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                x="0px"
                                y="0px"
                                width="100"
                                height="100"
                                viewBox="0 0 64 64"
                                style="fill: #ffffff"
                            >
                                <path
                                    d="M 21.580078 7 C 13.541078 7 7 13.544938 7 21.585938 L 7 42.417969 C 7 50.457969 13.544938 57 21.585938 57 L 42.417969 57 C 50.457969 57 57 50.455062 57 42.414062 L 57 21.580078 C 57 13.541078 50.455062 7 42.414062 7 L 21.580078 7 z M 47 15 C 48.104 15 49 15.896 49 17 C 49 18.104 48.104 19 47 19 C 45.896 19 45 18.104 45 17 C 45 15.896 45.896 15 47 15 z M 32 19 C 39.17 19 45 24.83 45 32 C 45 39.17 39.169 45 32 45 C 24.83 45 19 39.169 19 32 C 19 24.831 24.83 19 32 19 z M 32 23 C 27.029 23 23 27.029 23 32 C 23 36.971 27.029 41 32 41 C 36.971 41 41 36.971 41 32 C 41 27.029 36.971 23 32 23 z"
                                ></path>
                            </svg>
                        </a>
                    </div>
                    <div
                        v-if="
                            company.socialMedias && company.socialMedias.twitter
                        "
                    >
                        <a
                            :href="company.socialMedias.twitter"
                            target="_blank"
                            class="inline-block p-6 rounded-lg bg-gray-900 hover:bg-gray-700 text-white w-20 h-20 flex items-center justify-center"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                x="0px"
                                y="0px"
                                width="100"
                                height="100"
                                viewBox="0 0 50 50"
                                style="fill: #ffffff"
                            >
                                <path
                                    d="M 11 4 C 7.134 4 4 7.134 4 11 L 4 39 C 4 42.866 7.134 46 11 46 L 39 46 C 42.866 46 46 42.866 46 39 L 46 11 C 46 7.134 42.866 4 39 4 L 11 4 z M 13.085938 13 L 21.023438 13 L 26.660156 21.009766 L 33.5 13 L 36 13 L 27.789062 22.613281 L 37.914062 37 L 29.978516 37 L 23.4375 27.707031 L 15.5 37 L 13 37 L 22.308594 26.103516 L 13.085938 13 z M 16.914062 15 L 31.021484 35 L 34.085938 35 L 19.978516 15 L 16.914062 15 z"
                                ></path>
                            </svg>
                        </a>
                    </div>
                </div> -->
