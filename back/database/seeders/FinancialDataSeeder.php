<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FinancialData;
use Carbon\Carbon;

class FinancialDataSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Clear existing data
        FinancialData::truncate();

        // Sample Payment Analysis Data
        $this->seedPaymentAnalysis();

        // Sample Cash Flow Data
        $this->seedCashFlow();

        // Sample DRE Data
        $this->seedDre();
    }

    private function seedPaymentAnalysis()
    {
        $paymentData = [
            ['Cartão de Crédito', 45.67, 45.67, 22500.00],
            ['PIX', 25.33, 25.33, 12500.00],
            ['Boleto', 15.20, 15.20, 7500.00],
            ['Transferência', 8.90, 8.90, 4400.00],
            ['Dinheiro', 4.90, 4.90, 2100.00]
        ];

        $period = Carbon::now()->startOfMonth();

        foreach ($paymentData as $data) {
            FinancialData::create([
                'type' => 'payment_analysis',
                'category' => $data[0],
                'value' => $data[3],
                'percentage' => $data[1],
                'period' => $period,
                'month' => $period->month,
                'year' => $period->year,
                'data' => [
                    'monthly_value' => $data[3]
                ]
            ]);
        }
    }

    private function seedCashFlow()
    {
        $cashFlowData = [
            ['Receitas Operacionais', 50000, 48000],
            ['Vendas de Produtos', 35000, 33500],
            ['Prestação de Serviços', 15000, 14500],
            ['Despesas Operacionais', -25000, -26000],
            ['Salários e Encargos', -15000, -15200],
            ['Aluguel', -3000, -3000],
            ['Utilities', -2000, -2100],
            ['Marketing', -5000, -5700],
            ['Resultado Operacional', 25000, 22000]
        ];

        $period = Carbon::now()->startOfMonth();

        foreach ($cashFlowData as $data) {
            FinancialData::create([
                'type' => 'cash_flow',
                'category' => $data[0],
                'value' => $data[1],
                'period' => $period,
                'month' => $period->month,
                'year' => $period->year,
                'data' => [
                    'predicted' => $data[1],
                    'actual' => $data[2]
                ]
            ]);
        }
    }

    private function seedDre()
    {
        $dreData = [
            ['Receitas Operacionais', [48656.40, 46161.10, 41691.10, 42341.10, 40191.10, 40191.10, 40191.10, 40191.10, 40191.10, 40191.10, 40191.10, 41655.10], 501842.50],
            ['Receita Líquida de Vendas', [42582.48, 46085.49, 41691.09, 42341.09, 40191.09, 40191.09, 40191.09, 40191.09, 40191.09, 40191.10, 40191.10, 41655.10], 495692.90],
            ['Custos Operacionais', [-6522.36, -5868.37, -4987.42, -4987.42, -4987.42, -4987.42, -4987.42, -4987.42, -4987.42, -3766.12, -698.87, -698.87], -52466.53],
            ['Lucro Bruto', [36060.12, 40217.12, 36703.67, 37353.67, 35203.67, 35203.67, 35203.67, 35203.67, 35203.67, 36424.98, 39492.23, 40956.23], 443226.37],
            ['Despesas Operacionais', [-25929.48, -23272.59, -20958.19, -20678.19, -20678.19, -20808.19, -20623.19, -19723.19, -19853.19, -19723.19, -17628.94, -16007.92], -245884.45],
            ['Lucro / Prejuízo Operacional', [10130.64, 16944.53, 15745.48, 16675.48, 14525.48, 14395.48, 14580.48, 15480.48, 15350.48, 16701.79, 21863.29, 24948.31], 197341.92],
            ['Lucro / Prejuízo Final', [9922.88, 17145.24, 15761.31, 16691.31, 14541.31, 14411.31, 15615.05, 16515.05, 15808.55, 17056.46, 22217.96, 25302.98], 200989.41]
        ];

        $period = Carbon::now()->startOfMonth();

        foreach ($dreData as $data) {
            FinancialData::create([
                'type' => 'dre',
                'category' => $data[0],
                'value' => $data[2],
                'period' => $period,
                'month' => $period->month,
                'year' => $period->year,
                'data' => [
                    'monthly_values' => $data[1]
                ]
            ]);
        }
    }
}
