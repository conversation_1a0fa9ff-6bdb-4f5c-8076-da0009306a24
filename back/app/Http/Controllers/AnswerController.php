<?php

namespace App\Http\Controllers;

use App\Models\Answer;
use App\Models\Question;
use App\Models\Company;
use App\Models\CompanyUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AnswerController extends Controller
{
    /**
     * Obter todas as respostas para uma pergunta específica.
     */
    public function index(Question $question)
    {
        $answers = $question->answers;
        return response()->json($answers);
    }

    /**
     * Get all answers for a specific company user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAnswersByCompanyUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_user_id' => 'required|exists:company_users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $companyUser = CompanyUser::find($request->company_user_id);

        if (!$companyUser->form_finished) {
            return response()->json(['error' => 'User has not completed the form'], 400);
        }

        $answers = Answer::where('company_user_id', $request->company_user_id)->get();

        return response()->json([
            'data' => $answers,
            'user' => $companyUser->user
        ]);
    }

    /**
     * Store a new answer in the database.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'question_text' => 'required|string',
            'answer' => 'required|string',
            'company_slug' => 'required|string|exists:companies,slug',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Find the company by slug
        $company = Company::where('slug', $request->company_slug)->first();
        $user_id = Auth::id();
        if (!$company) {
            return response()->json(['error' => 'Company not found'], 404);
        }

        // Find the company_user record using user_id and company_id
        $companyUser = CompanyUser::where('user_id', $user_id)
            ->where('company_id', $company->id)
            ->first();

        if (!$companyUser) {
            return response()->json(['error' => 'User is not associated with this company'], 404);
        }

        // Create a new answer
        $answer = Answer::create([
            'question_text' => $request->question_text,
            'answer' => $request->answer,
            'company_user_id' => $companyUser->id,
            'slug' => $request->company_slug
        ]);

        // Update form_finished to true
        $companyUser->form_finished = true;
        $companyUser->save();

        return response()->json([
            'message' => 'Answer stored successfully',
            'data' => $answer
        ], 201);
    }
}