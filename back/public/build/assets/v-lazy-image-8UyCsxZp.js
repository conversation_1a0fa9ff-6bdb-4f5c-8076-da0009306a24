import{r as p,E as g,f as i,h as O,G as P,H as l}from"./app-BkKCG4YJ.js";var _=Object.defineProperty,b=Object.defineProperties,m=Object.getOwnPropertyDescriptors,d=Object.getOwnPropertySymbols,y=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?_(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,I=(e,r)=>{for(var t in r||(r={}))y.call(r,t)&&u(e,t,r[t]);if(d)for(var t of d(r))w.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>b(e,m(r)),j={props:{src:{type:String,required:!0},srcPlaceholder:{type:String,default:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"},srcset:{type:String},intersectionOptions:{type:Object,default:()=>({})},usePicture:{type:Boolean,default:!1}},inheritAttrs:!1,setup(e,{attrs:r,slots:t,emit:n}){const o=p(null),s=g({observer:null,intersected:!1,loaded:!1}),f=i(()=>s.intersected&&e.src?e.src:e.srcPlaceholder),v=i(()=>s.intersected&&e.srcset?e.srcset:!1),c=()=>{o.value&&o.value.getAttribute("src")!==e.srcPlaceholder&&(s.loaded=!0,n("load",o.value))},A=()=>n("error",o.value);return O(()=>{"IntersectionObserver"in window&&(s.observer=new IntersectionObserver(a=>{a[0].isIntersecting&&(s.intersected=!0,s.observer.disconnect(),n("intersect"))},e.intersectionOptions),s.observer.observe(o.value))}),P(()=>{"IntersectionObserver"in window&&s.observer&&s.observer.disconnect()}),()=>{const a=l("img",h(I({ref:o,src:f.value,srcset:v.value||null},r),{class:[r.class,"v-lazy-image",{"v-lazy-image-loaded":s.loaded}],onLoad:c,onError:A}));return e.usePicture?l("picture",{ref:o,onLoad:c},s.intersected?[t.default,a]:[a]):a}}};export{j as i};
