<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div class="p-3 min-h-full flex flex-col relative">
            <div class="flex gap-6 md:gap-12 flex-col md:flex-row-reverse">
                <!-- <div class="space-y-8 lg:w-96 justify-between">
                    <div class="elevated-card">
                        <div class="md:text-xl font-bold mb-4">
                            <PERSON><PERSON> de pagamento
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center gap-4" v-for="method in paymentsMethods" :key="method.value">
                                <input type="radio" :name="method.value" :id="method.value" :value="method.value"
                                    v-model="payment" />
                                <label :for="method.value" class="md:text-xl">{{
                                    method.name
                                    }}</label>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="gap-1 flex flex-col flex-1 p-1">
                    <!-- <div class="flex justify-between font-bold md:text-xl mb-2">
          Produtos
        </div> -->
                    <div v-for="(item, i) in drawerCart.cart" :key="i">
                        <cart-card :item="item" class="shadow-none" />
                    </div>
                </div>
            </div>
            <div class="elevated-card flex flex-row justify-between mt-auto">
                <div class="font-bold text-lg my-auto md:text-xl">
                    Total: {{ formatPrice(totalPrice) }}
                </div>
                <base-button @click="onSubmit" :loading class="w-auto md:text-lg !py-1 md:pb-1">
                    Finalizar
                </base-button>
            </div>
        </div>
    </AppLayout>
</template>
<script setup lang="ts">
import { useSchedule } from "@/store/schedule";
import { useToast } from "vue-toast-notification";
import { useUser } from "@/store/user";
import { router } from "@inertiajs/vue3";
import formatPrice from "@/utils/formatPrice";
import AppLayout from "@/Layouts/AppLayout.vue";
const toast = useToast();
const user = useUser();

const paymentsMethods = [
    // {
    //   name: "creditCard",
    //   value: "credit-card",
    //   icon: "/icons/creditcard.svg",
    // },
    // {
    //   name: "pix",
    //   value: "pix",
    //   icon: "/icons/pix.png",
    // },
    {
        name: "Pagar no local",
        value: "local",
    },
];
const loading = ref()
const payment = ref("local");
const drawerCart = useSchedule();
const totalPrice = computed(() =>
    drawerCart.cart.reduce((accumulator, currentItem) => {
        const itemTotal = parseFloat(
            Number(currentItem.service.price).toString(),
        );
        return accumulator + itemTotal;
    }, 0),
);
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
    professional: {
        type: Object,
    },
});
async function onSubmit() {
    loading.value = true
    try {
        if (!user.isLoggedIn) {
            toast.error("Você precisa estar logado para fazer um pedido!", { position: "top" });
            router.get(`/${slug}/login?redirect=checkout`);
            drawerCart.cartDrawerActive = false;
            return;
        }
        await drawerCart.postCart(slug);
        toast.success("Pedido criado com sucesso!!!", { position: "top" });
        router.get("pedidos");
    } catch (err) {
        return;
    }
    finally {
        loading.value = false
    }
}
</script>
