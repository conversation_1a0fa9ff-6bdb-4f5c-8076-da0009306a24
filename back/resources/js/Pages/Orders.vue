<template>
    <Head title="Pedidos" />
    <BaseLayout>
        <div class="py-6 w-full bg-[#eee] h-layout">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 px-4">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h1 class="text-2xl font-semibold text-gray-900">Seus Pedidos</h1>

                    <div class="mt-6">
                        <div v-if="orders.length === 0" class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            <p class="mt-4 text-gray-500">Você ainda não tem pedidos.</p>
                            <Link :href="route('professionals')" class="btn btn-primary mt-4">
                                Buscar profissionais
                            </Link>
                        </div>

                        <div v-else>
                            <div class="overflow-x-auto">
                                <table class="table w-full">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Data</th>
                                            <th>Profissional</th>
                                            <th>Serviço</th>
                                            <th>Status</th>
                                            <th>Total</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="order in orders" :key="order.id">
                                            <td>{{ order.id }}</td>
                                            <td>{{ formatDate(order.created_at) }}</td>
                                            <td>{{ order.professional_name }}</td>
                                            <td>{{ order.service_name }}</td>
                                            <td>
                                                <span class="badge" :class="getStatusClass(order.status)">
                                                    {{ getStatusLabel(order.status) }}
                                                </span>
                                            </td>
                                            <td>{{ formatPrice(order.total) }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline">Detalhes</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </BaseLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import BaseLayout from '@/Layouts/BaseLayout.vue';
import { ref } from 'vue';
import formatPrice from '@/utils/formatPrice';

// Sample data - replace with actual data from your backend
const orders = ref([]);

const props = defineProps({
    orders: {
        type: Array,
        default: () => [],
    },
});

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
}

function getStatusClass(status) {
    const statusClasses = {
        'pending': 'badge-warning',
        'completed': 'badge-success',
        'cancelled': 'badge-error',
        'processing': 'badge-info',
    };
    return statusClasses[status] || 'badge-ghost';
}

function getStatusLabel(status) {
    const statusLabels = {
        'pending': 'Pendente',
        'completed': 'Concluído',
        'cancelled': 'Cancelado',
        'processing': 'Em processamento',
    };
    return statusLabels[status] || status;
}
</script>
