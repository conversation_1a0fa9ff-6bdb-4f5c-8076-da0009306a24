<template>
    <div :class="[modelValue && 'modal-open', $attrs.class]" class="modal">
        <div class="modal-box overflow-visible text-accent-content">
            <div v-if="title" class="text-2xl text-center -mt-1 mb-2">
                {{ title }}
            </div>
            <div v-if="!hideClose" @click="() => {
                if (loading) return
                emit('update:modelValue', false)
            }" class="absolute -right-2 -top-2 text-xl cursor-pointer z-50">
                <XCircleIcon class="w-9 h-9" />
            </div>
            <slot name="default" />
        </div>
    </div>
</template>
<script setup lang="ts">
import { XCircleIcon } from "@heroicons/vue/24/solid";

defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: undefined,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    hideClose: {
        type: Boolean,
        default: false,
    },
});

// Make sure to use useAttrs() if you need to access attrs in the script
defineOptions({
    inheritAttrs: false
});

const emit = defineEmits(["update:modelValue"]);
</script>

<style scoped>
/* Add any additional styling here */
</style>
