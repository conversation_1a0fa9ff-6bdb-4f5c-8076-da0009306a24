import{_ as P}from"./BaseButton-CxEL1ccK.js";import{f as u,o as a,c as o,a as e,g as y,h as z,r as c,b as f,u as h,Z as F,w as x,t as i,j as T,F as g,e as _,d as V,N as k}from"./app-BkKCG4YJ.js";import{u as C,a as L,_ as O}from"./BaseLayout--Fu-QUOh.js";import{h as D}from"./date-Kj-P0Std.js";import{f as G}from"./formatPrice-DFW6Dy3T.js";const Q={class:"flex items-center"},I=["src","width","height"],M={key:0,class:"ml-2 text-xl font-bold text-primary"},U={__name:"AppLogo",props:{size:{type:[Number,String],default:40},showText:{type:Boolean,default:!0}},setup(t){const b=u(()=>"/img/logo.png");return(p,S)=>(a(),o("div",Q,[e("img",{src:b.value,width:t.size,height:t.size,alt:"Psy+ Logo"},null,8,I),t.showText?(a(),o("span",M,"Psy+")):y("",!0)]))}},Z={id:"landing-page",class:"flex bg-base-100 px-0 w-full flex-col text-accent-content h-layout"},q={class:"relative"},K={class:"w-full h-36 sm:h-[25vh] relative"},R={class:"carousel w-full h-full"},X=["src"],H={class:"md:py-5 px-5 bg-base-100 shadow-md rounded-lg"},J={class:"text-lg leading-7 pb-3 sm:text-xl pl-2 mt-1 ml-24 sm:ml-36 font-semibold text-accent-content drop-shadow-lg"},W={key:0},Y={key:1},tt={class:"flex mt-1"},et=["href"],st=["href"],at={class:"absolute bottom-0 sm:left-8 z-10 left-1 w-28 h-28 sm:w-28 sm:h-28 border-4 border-white rounded-full overflow-hidden bg-base-100"},ot=["src"],lt={class:"px-3"},nt={class:"flex gap-2 flex-wrap"},it={key:0},ct={key:1},rt={class:"carousel mt-5 w-full pb-10"},dt=["id"],ut=["href"],mt={class:"flex-1 w-full text-center"},ft={class:"text-lg font-semibold"},ht=["href"],$=250,xt={__name:"Professionals",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(t){const{filterProductsByGame:b,addService:p,addVariant:S}=C();z(async()=>{const{data:l}=await L(`/${t.slug}/most-scheduled`);v.value=l});const v=c([]);c();const N=c(!1);c();const B=c();c(!0);function j(l){B.value=l,p(l),t.company.solo_professional?k.get(`/${t.slug}/horarios`):k.get(`/${t.slug}/profissionais`)}u(()=>t.company.value.about?t.company.value.about.length>$?N.value?t.company.value.about:t.company.value.about.slice(0,$)+"...":t.company.value.about:"");const w=u(()=>["https://marketplace.canva.com/EAF_ZFGfAwE/1/0/1600w/canva-banner-para-twitch-montanha-vintage-retr%C3%B4-roxo-nqw7QjAVpKo.jpg"]),A=l=>l===0?`${w.value.length}`:`${l-1}`,E=l=>l===w.value.length?"0":`${l+1}`;return u(()=>Object.entries({sunday_time:"DOM",monday_time:"SEG",tuesday_time:"TER",wednesday_time:"QUA",thursday_time:"QUI",friday_time:"SEX",saturday_time:"SÁB"}).map(([m,d])=>{const s=t.company[m];return{day:d,hour:s?s.replace("-"," às "):"Fechado"}})),c("contact"),(l,n)=>{const m=P;return a(),o(g,null,[f(h(F),{title:"Profissionais"}),f(O,null,{default:x(()=>{var d;return[e("div",Z,[e("div",q,[e("div",K,[e("div",R,[e("img",{src:t.company.banner,alt:"Banner",class:"w-full bg-black h-full object-fill"},null,8,X)])]),e("section",H,[e("div",J,[t.company.solo_professional?(a(),o("div",W,i(t.professional.user.name),1)):(a(),o("div",Y,i(t.company.name),1)),e("div",tt,[t.company.whatsapp?(a(),o("a",{key:0,href:`https://wa.me/55${t.company.whatsapp}`,target:"_blank"},n[0]||(n[0]=[e("img",{src:"/icons/whatsapp.svg",class:"w-7",alt:""},null,-1)]),8,et)):y("",!0),t.company.instagram?(a(),o("a",{key:1,href:`https://www.instagram.com/${t.company.instagram}`,target:"_blank"},n[1]||(n[1]=[e("img",{src:"/icons/instagram.svg",class:"w-7 ml-2",alt:""},null,-1)]),8,st)):y("",!0)])])]),e("div",at,[t.company.solo_professional?(a(),o("img",{key:0,src:`/${t.professional.user.profile_photo_path}`,class:""},null,8,ot)):(a(),T(U,{key:1,class:"w-full h-full object-scale-down",size:"192"}))])]),e("div",lt,[n[3]||(n[3]=e("div",{class:"font-semibold text-lg my-2"}," Especialidades ",-1)),e("div",nt,[(a(!0),o(g,null,_((d=t.professional.specialties)==null?void 0:d.split(","),(s,r)=>(a(),o("div",{key:r,class:"badge text-ellipsis badge-primary gap-1 p-3"},i(s),1))),128))]),n[4]||(n[4]=e("div",{class:"mt-5 font-semibold text-lg"},"Sobre",-1)),t.company.solo_professional?(a(),o("div",it,i(t.professional.about),1)):(a(),o("div",ct,i(t.company.about),1)),n[5]||(n[5]=e("div",{class:"mt-3 font-semibold text-lg"},"Serviços",-1)),e("div",rt,[(a(!0),o(g,null,_(v.value,(s,r)=>(a(),o("div",{id:`slide-${r}`,key:r,class:"carousel-item relative w-full flex items-center"},[e("a",{href:`#slide-${A(r)}`,class:"btn btn-circle btn-xs"},"❮",8,ut),e("div",mt,[e("div",ft,i(s==null?void 0:s.name),1),e("div",null,i(h(G)(s==null?void 0:s.price)),1),e("div",null," Duração: "+i(h(D)(s==null?void 0:s.duration)),1),f(m,{color:"primary",onClick:gt=>j(s),size:"sm",class:"mt-2"},{default:x(()=>n[2]||(n[2]=[V("Agendar")])),_:2},1032,["onClick"])]),e("a",{href:`#slide-${E(r)}`,class:"btn btn-circle btn-xs"},"❯",8,ht)],8,dt))),128))])])])]}),_:1})],64)}}};export{xt as default};
