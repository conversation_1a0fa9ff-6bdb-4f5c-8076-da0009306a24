<template>
    <div
        class="card items-center card-side sm:flex-col w-full shadow-sm sm:w-44 h-20 sm:h-56 bg-base-200 rounded-lg sm:p-2 p-1"
        :class="`${cardStyles}`"
    >
        <v-lazy-image
            class="w-16 h-16 sm:w-24 sm:h-24 rounded-full"
            :src="professionalPhoto"
        />
        <div class="sm:card-body sm:py-2 sm:px-6 sm:w-full">
            <div class="w-full h-full flex flex-col px-2 sm:p-0 py-1">
                <div class="font-bold line-clamp-2 text-base sm:text-center">
                    {{ professional.user.name }}
                    <span v-if="professional.hasQuestionary" class="badge badge-sm badge-primary ml-1" title="Possui questionário">Q</span>
                </div>
            </div>
            <base-button
                color="black"
                size="sm"
                class="text-xs mt-auto btn-outline text-center hidden sm:block w-full"
                @click="emit('schedule')"
            >
                Agendar
            </base-button>
        </div>
        <base-button
            color="black"
            size="sm"
            class="text-xs mt-auto btn-outline sm:hidden ml-auto my-auto"
            @click="emit('schedule')"
        >
            Agendar
        </base-button>
    </div>
</template>
<script setup lang="ts">
import type { Professional } from "@/utils/models";
import VLazyImage from "v-lazy-image";
import { computed } from "vue";
const emit = defineEmits(["schedule"]);
const props = defineProps<{
    professional: Professional;
    cardStyles?: string;
}>();
const professionalPhoto = computed(() => {
    if (props.professional.user.profile_photo_path) {
        return `/${props.professional.user.profile_photo_path}`;
    } else {
        return props.professional.user.profile_photo_url;
    }
});
</script>
