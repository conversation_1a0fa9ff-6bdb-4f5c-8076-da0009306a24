<template>
    <component :is="slug ? AppLayout : BaseLayout" :slug="slug" :company="company">
        <div class="flex items-center justify-center h-full">
            <div class="card mx-auto w-full max-w-5xl shadow-xl">
                <div class="hero min-h-full rounded-l-xl px-4 hidden md:hidden">
                    <img src="/icons/logo.png" alt="Dashwind Admin Template" class="w-72 inline-block  -mt-8 md:pt-0" />
                </div>
                <div class="grid md:grid-cols-2 grid-cols-1 px-2 rounded-xl">
                    <div class="hero min-h-full rounded-l-xl bg-base-200">
                        <img src="/icons/logo.png" alt="Dashwind Admin Template" class="w-96 md:pt-0 hidden md:block" />
                    </div>
                    <div
                        class="pt-4 pb-6 sm:py-12 px-4 sm:px-10 bg-base-200 rounded-xl md:rounded-l-none md:rounded-r-xl">
                        <h2 class="text-2xl font-semibold mb-2 text-center">
                            Cadastre-se
                        </h2>
                        <register-form @submit="handleFormSubmit" :loading="loading" />
                    </div>
                </div>
            </div>
        </div>
    </component>
</template>
<script setup lang="ts">
import { useUser } from "@/store/user";
import { api } from "@/server/api";
import axios from "axios";
import { useToast } from "vue-toast-notification";
import AppLayout from "@/Layouts/AppLayout.vue";
import { router } from "@inertiajs/vue3";
import BaseLayout from "@/Layouts/BaseLayout.vue";

const auth = useUser();
const toast = useToast();
const loading = ref(false);
const errors = {
    "User already exists": "Usuário já existe",
};

type ErrorKey = keyof typeof errors;

function mapErrorToMessage(error: ErrorKey) {
    return errors[error] || error;
}

const { slug } = defineProps({
    slug: { type: String },
    company: { type: Object },
});
async function handleFormSubmit(event: { email: string; password: string; phone: string }) {
    try {
        loading.value = true;
        await api.post("/register", event);
        const userToLogin = {
            identifier: event.phone,
            password: event.password,
            isPhone: true,
        };
        const { data: dataLogin } = await api.post("/login", userToLogin);
        auth.login(dataLogin.token);
        toast.success("Cadastro realizado com sucesso!", { position: "top" });
        if (slug) router.get(`/${slug}/servicos`);
        else {
            router.get(`/`);
        }
    } catch (err) {
        if (axios.isAxiosError(err)) {
            toast.error(mapErrorToMessage(err.response?.data.message), { position: "top" });
        }
    } finally {
        loading.value = false;
    }
}
</script>
