<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class FinancialData extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'category',
        'value',
        'percentage',
        'period',
        'month',
        'year',
        'data'
    ];

    protected $casts = [
        'data' => 'array',
        'value' => 'decimal:2',
        'percentage' => 'decimal:2',
        'period' => 'date'
    ];

    // Scopes for filtering
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('period', [$startDate, $endDate]);
    }

    public function scopeByMonth($query, $month, $year = null)
    {
        $query = $query->where('month', $month);
        if ($year) {
            $query = $query->where('year', $year);
        }
        return $query;
    }

    public function scopeByYear($query, $year)
    {
        return $query->where('year', $year);
    }

    // Helper methods
    public static function getAvailableTypes()
    {
        return [
            'payment_analysis' => 'Análise de Pagamentos',
            'cash_flow' => 'Fluxo de Caixa',
            'dre' => 'DRE Mensal'
        ];
    }

    public static function getAvailablePeriods()
    {
        return self::selectRaw('DISTINCT YEAR(period) as year, MONTH(period) as month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'year' => $item->year,
                    'month' => $item->month,
                    'label' => Carbon::createFromDate($item->year, $item->month, 1)->format('M/Y')
                ];
            });
    }
}
