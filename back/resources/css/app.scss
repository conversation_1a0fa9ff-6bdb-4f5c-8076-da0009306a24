@tailwind base;
@tailwind components;
@tailwind utilities;
@layer components {
    .elevated-card {
        @apply card bg-base-200 shadow-lg py-3 px-5;
    }
}
[x-cloak] {
    display: none;
}
html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
}

#app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.drawer {
    height: 100vh !important;
}

.navbar {
    padding: 2px 10px !important;
    min-height: 48px !important;
}
.input {
    height: 2.3rem !important;
}
#quantity.input {
    height: 128px !important;
}
.select {
    height: 2.5rem !important;
    min-height: 2.5rem !important;
}
::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
}
/* Track */
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: red;
    border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #b30000;
}

* {
    scrollbar-color: hsl(var(--b2, var(--b1)) / 1) rgb(198, 198, 198);
}
*:hover {
    scrollbar-color: hsl(var(--b2, var(--b1)) / 1) rgb(198, 198, 198);
}
html {
    scrollbar-gutter: auto !important;
}
.label {
    padding-bottom: 0.3rem;
    font-weight: 500;
}
@media (min-width: 640px) {
    .card.image-full {
        z-index: 0 !important;
    }
}

/* .drawer-side {
    overflow-y: visible !important;
} */
.input {
    background-color: white !important  ;
    color: black !important;
    color-scheme: dark;
}
.tabs {
    a {
        color: oklch(var(--ac)) !important;
    }
}
.drawer-h {
    height: 100svh !important;
}
/* Layout variables */
:root {
    --header-height: 56px;
    --bottom-nav-height: 64px;
}

/* Bottom navigation styles */
.btm-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--bottom-nav-height);
    z-index: 50;
}

/* Main content area that adjusts for bottom nav */
.h-layout {
    min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
    overflow-y: auto;
}

/* For desktop where bottom nav is hidden */
@media (min-width: 768px) {
    .h-layout {
        padding-bottom: 0;
        min-height: calc(100vh - var(--header-height));
    }
}
@media (max-width: 768px) {
    .h-bottom {
        height: calc(100% - var(--bottom-nav-height));
    }
}
.h-bottom > div {
    overflow-y: auto;
    height: 100%;
}
