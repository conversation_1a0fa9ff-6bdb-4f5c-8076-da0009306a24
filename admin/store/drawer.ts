import {
  BriefcaseIcon,
  BuildingStorefrontIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  DevicePhoneMobileIcon,
  ScissorsIcon,
  ShoppingBagIcon,
  UserGroupIcon,
} from "@heroicons/vue/24/solid";
import { ClipboardDocumentListIcon } from "@heroicons/vue/24/outline";
import { defineStore } from "pinia";
import { computed, ref } from "vue";
export type Routes = {
  to?: string;
  label: string;
  icon: typeof CurrencyDollarIcon | string;
  items?: {
    label: string;
    to: string;
  }[];
  ref?: string;
};
const routes = computed(() => {
  const routes: Routes[] = [
    {
      to: "/analytics/overview",
      label: "Visão Geral",
      icon: ClipboardDocumentListIcon,
    },
    {
      to: "/analytics/usuarios",
      label: "Usuários",
      icon: UserGroupIcon,
    },
    {
      to: "/analytics/agendamentos",
      label: "Agendamentos",
      icon: CalendarDaysIcon,
    },
    {
      to: "/analytics/receita",
      label: "Receita",
      icon: <PERSON>urrencyDollarIcon,
    },
    {
      to: "/analytics/acesso",
      label: "Acesso",
      icon: BuildingStorefrontIcon,
    },
    {
      to: "/analytics/bugs",
      label: "Bugs",
      icon: ScissorsIcon,
    },
    {
      to: "/analytics/google",
      label: "Google Analytics",
      icon: ClipboardDocumentListIcon,
    },
    {
      to: "/profissionais",
      label: "Profissionais",
      icon: BriefcaseIcon,
    },
    {
      to: "/pacientes",
      label: "Pacientes",
      icon: UserGroupIcon,
    },
    {
      to: "/solicitacoes",
      label: "Solicitações",
      icon: ClipboardDocumentListIcon,
    },
    {
      to: "/cores",
      label: "Meu aplicativo",
      ref: "appRef",
      icon: DevicePhoneMobileIcon,
      // items: [
      //   {
      //     label: "Cores",
      //     to: "/cores",
      //   },
      //   {
      //     label: "Pagina inicial",
      //     to: "/landing-page",
      //   },
      // ],
    },
    {
      label: "Finanças",
      icon: CurrencyDollarIcon,
      ref: "financasRef",

      items: [
        {
          label: "Contas a pagar",
          to: "/contas",
        },
        {
          label: "Relatórios",
          to: "/relatorio",
        },
        {
          label: "Movimentações",
          to: "/movimentacoes",
        },
      ],
    },
    {
      label: "Estoque",
      icon: ShoppingBagIcon,
      ref: "stockRef",
      items: [
        {
          label: "Produtos",
          to: "/produtos",
        },
        {
          label: "Movimentações",
          to: "/movimentacao-produtos",
        },
      ],
    },
    // {
    //   to: "",
    //   label: "Estoque",
    //   icon: ShoppingBagIcon,
    // },
  ];
  // if (user.isWorker) {
  //   return routes.filter((route) => !routesToRestrict.includes(route.label));
  // }
  return routes;
});
export const useDrawer = defineStore("drawer", {
  state: () => ({
    active: false,
    toggledBar: false,
    // routes,
  }),
  actions: {
    toggleActive() {
      this.active = !this.active;
    },
  },
  persist: true,
});
