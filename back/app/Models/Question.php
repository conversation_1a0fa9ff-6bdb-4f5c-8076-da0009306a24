<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Question extends Model
{
    protected $fillable = [
        'label',
        'type',
        'options',
        'isRequired',
        // 'company_id',
        'company_professional_id'
    ];

    protected $casts = [
        'isRequired' => 'boolean',
        'options' => 'array'
    ];

    public function answers(): HasMany
    {
        return $this->hasMany(Answer::class);
    }

    /**
     * Relationship with CompanyProfessional.
     */
    public function companyProfessional()
    {
        return $this->belongsTo(CompanyProfessional::class, 'company_professional_id');
    }
}