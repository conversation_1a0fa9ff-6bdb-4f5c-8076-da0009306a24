import { defineStore } from "pinia";
import { router } from "@inertiajs/vue3";
export const useUser = defineStore("users", {
    state: () => ({
        token: "",
        form_completed: false,
    }),
    getters: {
        isLoggedIn: (state) => !!state.token.length,
    },
    actions: {
        login(token: string) {
            this.token = token;
            // router.get(`/${slug}/login`);
        },
        logout(slug: string) {
            this.token = "";
            this.form_completed = false;
            // router.get(`/${slug}/login`);
        },
    },
    persist: true,
});
