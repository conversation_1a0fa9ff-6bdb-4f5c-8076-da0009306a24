<template>
    <form class="flex flex-col gap-3" @submit="submit">
        <input-base
            v-model="identifier"
            name="identifier"
            :error="errors['identifier']"
            label="Email ou Telefone"
            :data-maska="isPhone ? '(##) #####-####' : undefined"
        />
        <input-base
            :error="errors['password']"
            v-model="password"
            name="password"
            label="Senha"
            type="password"
        />
        <div class="text-right text-primary">
            <Link :href="`register`">
                <span
                    class="inline-block font-black hover:text-primary hover:underline hover:cursor-pointer transition duration-200"
                >
                    Cadastre-se
                </span>
            </Link>
        </div>
        <base-button type="submit" class="mt-2 w-full" :loading>
            Entrar
        </base-button>
    </form>
</template>
<script setup lang="ts">
import { string, object } from "yup";
import { useForm } from "vee-validate";
import { Link } from "@inertiajs/vue3";
import { useCompanyStore } from "@/store/company";
import { ref, computed } from "vue";

const companyStore = useCompanyStore();
const isPhone = ref(false);

const emit = defineEmits(["submit"]);

const phoneRegex = /^\(\d{2}\)\s\d{5}-\d{4}$/;
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const validationSchema = object().shape({
    identifier: string()
        .required("Campo obrigatório")
        .when(([], schema) => {
            if (!isPhone.value) return schema.email("Email inválido");
        }),
    password: string().required("Campo obrigatório"),
});

const { handleSubmit, defineField, errors } = useForm({
    validationSchema,
});

const [identifier] = defineField("identifier");
const [password] = defineField("password");

function handleIdentifierChange() {
    isPhone.value =
        identifier.value.includes("(") ||
        /^\d+$/.test(identifier.value.replace(/\D/g, ""));
}

function handleFormSubmit(values: any) {
    const { identifier, password } = values;
    const isValidPhone = phoneRegex.test(identifier);
    const isValidEmail = emailRegex.test(identifier);

    emit("submit", {
        identifier: identifier,
        isPhone: isPhone.value,
        password,
    });
}

const { slug } = defineProps({
    slug: {
        type: String,
    },
    loading: {
        type: Boolean,
    },
});
watch(identifier, () => {
    handleIdentifierChange();
});
const submit = handleSubmit(handleFormSubmit);
</script>
