<template>
  <div>
    <!-- <div class="text-lg">{{ title }} às <b class="text-sm">{{ dayjs(start).format('HH:mm') }} - {{
            dayjs(end).format('HH:mm') }}</b>
        </div> -->
    <div class="text-lg mt-3">Serviço: {{ title }}</div>
    <div class="my-1 text-sm">Paciente: {{ client_name }}</div>
    <div class="my-5">
      <input-base
        v-model="status"
        label="Status"
        :options="[
          {
            label: 'Finalizado',
            value: 'finished',
          },
          {
            label: 'Cancelado',
            value: 'canceled',
          },
        ]"
      />
      <div v-if="error" class="text-sm text-red-600">
        Selecione o status do atendimento
      </div>
    </div>
    <base-button @click="handleConfirm" :loading class="w-full mt-2"
      >Confirmar</base-button
    >
  </div>
</template>
<script setup lang="ts">
const error = ref(false);
const status = ref();
const emit = defineEmits(["finish"]);
defineProps({
  id: {
    type: Number,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  loading: {
    type: Boolean,
    required: true,
  },
  client_id: {
    type: Number,
    required: true,
  },
  client_name: {
    type: String,
    required: true,
  },
});
function handleConfirm() {
  if (!status.value) {
    error.value = true;
    return;
  }
  emit("finish", status.value);
}
watch(status, () => {
  error.value = false;
});
</script>
