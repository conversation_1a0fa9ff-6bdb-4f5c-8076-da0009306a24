<template>
    <div class="elevated-card rounded-lg px-3 py-1 shadow-xl transition-shadow">
        <div class="flex items-center justify-between font-semibold text-lg border-b border-gray-400 pb-1">
            <div>Pedido #{{ order.id }}</div>
            <div class="font-bold">
                {{
                    dayjs(order.appointments[0]?.start_date)?.format("DD/MM/YYYY")
                }}
            </div>
        </div>
        <div class="mb-3 mt-2 divide-y space-y-2">
            <div v-for="(item, i) in order.appointments" :key="i" class="">
                <div class="flex gap-x-1" :class="i !== 0 ? 'mt-2' : ''">
                    <span>Serviço:</span>
                    <div>{{ item.service.name }}</div>
                    <b>{{ dayjs(item.start_date).format("HH:mm") }} </b>
                    -
                    <b>{{ dayjs(item.end_date).format("HH:mm") }} </b>
                </div>
                <div class="flex gap-x-1 leading-tight mt-2">
                    Profissional:
                    <b class="">
                        <Link :href="`/${item.professional.slug}`">
                            {{ getFirstAndLastName(item?.professional.user.name) }}
                        </Link>
                    </b>
                </div>
            </div>
        </div>
        <div class="flex justify-between mt-auto pb-2">
            <div class="flex">
                <div class="font-bold text-lg mr-1">Total:</div>
                <div class="text-lg font-semibold">R${{ order.total }}</div>
            </div>

            <!--<div v-if="activeTab === 'active'" class="float-right space-x-4 flex">
         <ChatBubbleLeftEllipsisIcon
          class="w-5 sm:w-10 hover:btn-ghost sm:p-2 transition-colors rounded"
          @click="toOrder(order as ItemOrder)"
        /> -->
            <!-- <ArrowsPointingOutIcon
          class="w-5 sm:w-10 hover:btn-ghost sm:p-2 transition-colors rounded"
        />
      </div>-->
        </div>
        <div v-if="activeTab === 'active'" class="flex justify-between pb-1">
            <!-- <div class="font-bold uppercase cursor-pointer" @click="finishModal = true">Finalizar</div> -->
            <base-button class="btn-sm ml-auto" @click="finishModal = true">Finalizar</base-button>
        </div>
        <base-dialog :loading title="Finalizar pedido" v-model="finishModal">
            <div class="px-2 pt-2">
                Deseja realmente concluir seu pedido?
            </div>
            <div class="flex justify-end items-center mt-5">
                <base-button :loading class=" btn-ghost font-bold uppercase"
                    @click="finishModal = false">Cancelar</base-button>
                <base-button :loading @click="finishOrder">Confirmar</base-button>
            </div>
        </base-dialog>
    </div>
</template>

<script setup lang="ts">
import { api } from "@/server/api";
import { Link } from "@inertiajs/vue3";
import dayjs from "dayjs";
const props = defineProps({
    order: {
        type: Object as PropType<newOrder>,
        required: true,
    },
    activeTab: {
        type: String,
        required: true,
        default: "active",
    },
});
const emit = defineEmits(['refresh'])
const finishModal = ref(false)
const loading = ref(false)
function getFirstAndLastName(name: string) {
    const names = name.split(" ");
    return `${names[0]} ${names[names.length - 1]}`;
}
async function finishOrder() {
    loading.value = true
    try {
        await api.post('/finish-my-order/' + props.order.id)
        finishModal.value = false
        emit('refresh')
    }
    catch (e) {
        console.log(e)
    }
    finally {
        loading.value = false
    }
}
// function toOrder(order: ItemOrder) {
//   router.push({
//     path: "/chat",
//     query: {
//       redirect: "true",
//     },
//   });
//   // eslint-disable-next-line
//   chatStore.selectedChat = order as any;
// }
</script>
