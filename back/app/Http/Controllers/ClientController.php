<?php

namespace App\Http\Controllers;

use App\Mail\AccountCreated;
use App\Models\Company;
use App\Models\CompanyProfessional;
use App\Models\CompanyUser;
use App\Models\Question;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ClientController extends Controller
{
    /**
     * Exibe os slots disponíveis.
     */
    public function getCustomers(Request $request)
    {
        $companyID = $request->get('company_id');
        $filterName = $request->query('name'); // Filtrar por nome
        $sortBy = $request->query('sort_by', 'name'); // Ordenar por 'name' por padrão
        $sortDirection = $request->query('direction', 'asc'); // Ordem crescente por padrão
        $perPage = $request->query('per_page', 10); // Valor padrão de 10
        $page = $request->query('page', 1); // Valor padrão de 1

        $query = CompanyUser::where('company_id', $companyID)->with('user');

        if ($filterName) {
            $query->whereHas('user', function ($q) use ($filterName) {
                $q->where('name', 'like', '%' . $filterName . '%');
            });
        }

        if (in_array($sortBy, ['name', 'total_spent'])) {
            if ($sortBy === 'name') {
                $query->join('users', 'company_users.user_id', '=', 'users.id')
                    ->orderBy('users.name', $sortDirection);
            } elseif ($sortBy === 'total_spent') {
                $query->orderBy('company_users.total_spent', $sortDirection);
            }
        }

        $customers = $query->paginate($perPage, ['company_users.*'], 'page', $page);

        $customers->getCollection()->transform(function ($item) {
            return [
                'id' => $item->id,
                'user' => $item->user,
                'total_spent' => $item->total_spent,
                'total_scheduled' => $item->total_scheduled,
                'form_finished' => $item->form_finished,
            ];
        });

        return response()->json($customers, 200);
    }
    public function getCustomer(string $id)
    {
        $customer = CompanyUser::where('id', $id)->with('user')->firstOrFail();
        $customer->form_finished = (bool) $customer->form_finished; // Ensure boolean type
        return response()->json($customer, 200);
    }
    // Quando tiver agencias, criaremos uma nova tabela de finished_form para quem não for solo_professional
    public function userHasCompletedForm(Request $request)
    {
        $company = Company::where('slug', $request->get('slug'))->first();
        $user = CompanyUser::where('user_id', Auth::id())->where('company_id', $company->id)->first();
        if (!$user) {
            return response()->json(null, 200);
        }
        return response()->json(['form_completed' => $user->form_finished], 200);
    }
    public function getForm(string $slug)
    {
        $company = Company::where('slug', $slug)->firstOrFail();
        $questions = Question::where('company_id', $company->id)->get();
        return response()->json($questions, 200);
    }
    public function createUserAndClient(Request $request)
    {
        $newUser = [
            'phone' => $request->get('phone'),
            'name' => $request->get('name'),
            'email' => $request->get('email'),
            'password' => *****************,
            'type' => 'user',
            'finished_register' => 0
        ];
        $user = User::create($newUser);
        CompanyUser::create([
            'user_id' => $user->id,
            'company_id' => $request->get('company_id'),
            'total_spent' => 0,
            'total_scheduled' => 0,
            'form_finished' => false,
        ]);
        Log::info('User created: ' . $user->name);
        if($request->get('email')) {
            Log::info('Sending account creation email to: ' . $user->email);
            try {
                $admin = CompanyProfessional::with('user')->where('company_id', $request->get('company_id'))->first();
                if ($admin) {
                    Mail::to($user->email)->send(new AccountCreated($user, $admin));
                } else {
                    Log::error('No professional found for company_id: ' . $request->get('company_id'));
                }
            } catch (\Exception $e) {
                // Log the error but don't stop execution
                Log::error('Failed to send account creation email: ' . $e->getMessage());
            }
        }

        return response()->json('Cliente criado com sucesso', 201);
    }
    public function vinculateExistingUserAsClient(Request $request)
    {
        CompanyUser::create([
            'user_id' => $request->get('user_id'),
            'company_id' => $request->get('company_id'),
            'total_spent' => 0,
            'total_scheduled' => 0,
            'form_finished' => false,
        ]);
        return response()->json('Cliente vinculado com sucesso', 201);
    }


}
