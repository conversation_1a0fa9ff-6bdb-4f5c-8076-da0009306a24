export const meses = [
  { id: 0, nome: "Janeiro" },
  { id: 1, nome: "Fevereiro" },
  { id: 2, nome: "Março" },
  { id: 3, nome: "Abril" },
  { id: 4, nome: "Mai<PERSON>" },
  { id: 5, nome: "Jun<PERSON>" },
  { id: 6, nome: "Julho" },
  { id: 7, nome: "Agosto" },
  { id: 8, nome: "Setembro" },
  { id: 9, nome: "Outubro" },
  { id: 10, nome: "Novembro" },
  { id: 11, nome: "Dezembro" },
];
export const anos = ["2023", "2024", "2025"];

export function handleTime(minutos: number | string) {
  if (typeof minutos === "string") {
    minutos = parseFloat(minutos);
  }
  const horas = Math.floor(minutos / 60);
  const minutosRestantes = minutos % 60;

  if (horas === 0) {
    return `${minutos} minuto(s)`;
  } else if (minutosRestantes === 0) {
    return `${horas} hora(s)`;
  } else {
    return `${horas} hora(s) e ${minutosRestantes} minuto(s)`;
  }
}

// export { meses, anos };
