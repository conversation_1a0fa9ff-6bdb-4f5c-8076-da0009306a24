# Recursive Appointments Feature

## Overview

The Recursive Appointments feature allows professionals to create multiple appointments automatically for the next 3 months based on a recurring pattern. This is useful for patients who need regular appointments (weekly, biweekly, or monthly).

## How It Works

### 1. Creating Recursive Appointments

When creating an appointment, you can mark it as recursive by setting `isRecurrent: true` and specifying a `recursiveValue`:

```javascript
POST /api/appointments
{
  "professionalId": 1,
  "clientId": 2,
  "serviceId": 3,
  "startTime": "08:00",
  "date": "2025-01-28",
  "isRecurrent": true,
  "recursiveValue": "weekly"  // Options: "weekly", "biweekly", "monthly"
}
```

This will:
- Create the initial appointment for the specified date
- Automatically create additional appointments for the next 3 months based on the recursive pattern
- Each appointment will have its own order and can be managed independently

### 2. Recursive Patterns

- **Weekly**: Creates appointments every week on the same day and time
- **Biweekly**: Creates appointments every 2 weeks on the same day and time
- **Monthly**: Creates appointments every month on the same day and time

### 3. Time Slot Availability

The slot availability system:
1. Only shows available slots for the next 3 months
2. Checks company daily pauses (existing functionality)
3. Checks all existing appointments (including recursive ones)
4. Automatically skips time slots that are already occupied

### 4. Appointment Management

Each recursive appointment is created as a separate appointment with:
- Its own order and billing
- Individual status management
- Ability to be cancelled or rescheduled independently

## Database Schema

### Appointments Table
- Added `is_recurrent` boolean field (default: false)
- Added `recursive_value` enum field ('weekly', 'biweekly', 'monthly', nullable)

### Note on Recurrent Pauses Table
The `recurrent_pauses` table still exists but is no longer used in the current implementation. It can be kept for potential future features or removed if not needed.

## Use Cases

1. **Weekly Therapy Sessions**: A patient needs therapy every Tuesday at 8:00 AM for 3 months
2. **Biweekly Check-ups**: A patient needs check-ups every 2 weeks at the same time
3. **Monthly Consultations**: A patient needs monthly follow-up appointments

## Frontend Integration

To integrate this feature in the frontend:

1. Add a checkbox "Recursive Appointment" when creating appointments
2. Add a dropdown for recursive pattern selection (weekly, biweekly, monthly)
3. Display all created appointments in the calendar view
4. Show a confirmation dialog indicating how many appointments will be created
5. Limit date selection to the next 3 months only

## API Endpoints Summary

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/appointments` | Create appointment (with isRecurrent and recursiveValue options) |
| GET | `/{slug}/slots` | Get available slots (limited to next 3 months) |

All endpoints require appropriate authentication.

## Example Response

When creating a recursive appointment, the system will:
1. Create the initial appointment
2. Attempt to create additional appointments for the next 3 months
3. Skip any time slots that are already occupied
4. Return the initial appointment details

The created appointments can be viewed through existing appointment listing endpoints.
