<template>
    <div :class="`form-control w-full`">
        <label
            :class="
                error
                    ? 'text-error'
                    : `label-text text-base-content ${labelStyle} mb-1`
            "
            class="text-sm"
        >
            <div style="display: inline;" :class="labelBg">
                {{ label }}
            </div>
        </label>
        <div class="relative">
            <currency v-if="isCurrency" v-model="modelValue" />
            <!-- <packs-auto-complete-select
        v-else-if="isAutoComplete"
        v-bind="config"
        :inputStyle="inputStyle"
        :selected="itemsSelected"
        :items="itemsToSelected"
        @update:selected="updateSelected"
        /> -->
            <!-- <select-base
                    v-else-if="isSelect"
                    v-bind="config"
                    :inputStyle="inputStyle"
                    :value="value"
                    :bordered="bordered"
                    @update:modelValue="updateModel"
                    :initial-value="value"
                    :options="props.options"
                    :class="inputClasses"
                /> -->
            <input
                v-else
                v-maska
                :data-maska="dataMaska"
                @maska="handleMask"
                v-model="maskedValue"
                class="input w-full bordered"
                :class="[
                    error ? 'input-error ' : `${color} ${bgColor}`,
                    bordered && 'input-bordered',
                    inputStyle,
                    inputClasses,
                ]"
                :type="type"
                :disabled="disabled"
                :readonly="readonly"
            />
            <slot name="append" />
        </div>
        <div class="text-error text-xs" v-if="error">
            {{ error }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { type MaskaDetail, type MaskType } from "maska";
import { defineModel } from "vue";
import { vMaska } from "maska";
const modelValue = defineModel();
const props = defineProps({
    label: String,
    labelStyle: String,
    classes: String,
    labelBg: String,
    type: {
        type: String,
        default: "text",
    },
    size: {
        type: String,
        default: "md",
    },
    value: {
        type: [String, Number],
    },
    error: {
        default: undefined,
        type: String,
    },
    placeholder: {
        type: String,
        default: "",
    },
    color: {
        type: String,
        default: "base-content",
    },
    bgColor: {
        type: String,
        default: "base-100",
    },
    bordered: {
        type: Boolean,
        default: true,
    },
    min: {
        type: [String, Number],
    },
    max: {
        type: [String, Number],
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    isCurrency: { type: Boolean },
    options: { type: Array },
    itemsSelected: { type: Object },
    itemsToSelected: { type: Object },
    dataMaska: { type: String as () => MaskType },
    unmasked: { type: String as () => MaskType },
    dataMaskaReversed: { type: Boolean },
    inputClasses: { type: String },
    optionLabel: { type: String, default: undefined },
    optionValue: { type: String, default: undefined },
});
const inputStyle = computed(() => {
    const prefix = isSelect.value ? "select" : "input";
    return `${prefix}-${props.size} `;
});

// const isAutoComplete = computed(() => !!props.itemsSelected); // Use !! to convert to a boolean value
const isSelect = computed(() => !!props.options?.length); // Use !! to convert to a boolean value

const config = computed<object>(() => {
    const newProps = {
        ...props,
    };
    return convertToKebabCase(newProps);
});

const emit = defineEmits(["update:modelValue", "update:selected"]);
function convertToKebabCase<T>(obj: Record<string, T>): Record<string, T> {
    const convertedObj = {} as Record<string, T>;
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const kebabKey = key
                .replace(/([a-z])([A-Z])/g, "$1-$2")
                .toLowerCase();
            convertedObj[kebabKey] = obj[key];
        }
    }
    return convertedObj;
}
const maskedValue = ref(modelValue.value);
function handleMask(e: CustomEvent<MaskaDetail>) {
    modelValue.value = e.detail.unmasked;
}
watch(modelValue, (val) => {
    if (!props.dataMaska || !maskedValue.value) maskedValue.value = val;
});
function updateModel(e: string) {
    emit("update:modelValue", e);
}
</script>
