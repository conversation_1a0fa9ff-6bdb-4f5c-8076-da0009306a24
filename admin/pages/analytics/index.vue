<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Financial Analytics Dashboard</h1>
          <div class="flex gap-2">
            <button @click="refreshData" class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
          </div>
        </div>

        <!-- Filter Controls -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
          <div class="flex flex-wrap gap-4 items-end">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Year</span>
              </label>
              <select v-model="filters.year" class="select select-bordered select-sm">
                <option value="">All Years</option>
                <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Month</span>
              </label>
              <select v-model="filters.month" class="select select-bordered select-sm">
                <option value="">All Months</option>
                <option value="1">January</option>
                <option value="2">February</option>
                <option value="3">March</option>
                <option value="4">April</option>
                <option value="5">May</option>
                <option value="6">June</option>
                <option value="7">July</option>
                <option value="8">August</option>
                <option value="9">September</option>
                <option value="10">October</option>
                <option value="11">November</option>
                <option value="12">December</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Start Date</span>
              </label>
              <input v-model="filters.start_date" type="date" class="input input-bordered input-sm">
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">End Date</span>
              </label>
              <input v-model="filters.end_date" type="date" class="input input-bordered input-sm">
            </div>
            <button @click="applyFilters" class="btn btn-primary btn-sm">Apply</button>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">Clear</button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <NuxtLink to="/analytics/overview" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-blue-600 text-2xl mr-3">📊</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">Visão Geral</p>
                <p class="text-lg font-bold text-gray-800">Overview</p>
              </div>
            </div>
          </NuxtLink>

          <NuxtLink to="/analytics/payment-analysis" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-green-600 text-2xl mr-3">💳</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">Análise de Pagamentos</p>
                <p class="text-lg font-bold text-gray-800">Payment Analysis</p>
              </div>
            </div>
          </NuxtLink>

          <NuxtLink to="/analytics/cash-flow" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-purple-600 text-2xl mr-3">💰</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">Fluxo de Caixa</p>
                <p class="text-lg font-bold text-gray-800">Cash Flow</p>
              </div>
            </div>
          </NuxtLink>

          <NuxtLink to="/analytics/dre" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-orange-600 text-2xl mr-3">📈</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">DRE</p>
                <p class="text-lg font-bold text-gray-800">Income Statement</p>
              </div>
            </div>
          </NuxtLink>
        </div>

        <!-- Summary Cards -->
        <div v-if="summaryData" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="stat">
              <div class="stat-title">Total Records</div>
              <div class="stat-value text-primary">{{ summaryData.total_records || 0 }}</div>
            </div>
          </div>
          <div class="bg-white rounded-lg shadow p-6">
            <div class="stat">
              <div class="stat-title">Total Value</div>
              <div class="stat-value text-success">{{ formatCurrency(summaryData.total_value || 0) }}</div>
            </div>
          </div>
          <div class="bg-white rounded-lg shadow p-6">
            <div class="stat">
              <div class="stat-title">Average Value</div>
              <div class="stat-value text-info">{{ formatCurrency(summaryData.avg_value || 0) }}</div>
            </div>
          </div>
          <div class="bg-white rounded-lg shadow p-6">
            <div class="stat">
              <div class="stat-title">Latest Period</div>
              <div class="stat-value text-accent">{{ formatDate(summaryData.latest_period) }}</div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-lg font-semibold mb-4">Financial Analytics Dashboard</h2>
          <p class="text-gray-600 mb-4">Bem-vindo ao painel de analytics financeiro! Escolha uma das opções acima para visualizar dados específicos.</p>

          <div class="mt-4 text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
            📊 Dashboard de Analytics Financeiro funcionando corretamente!
            <p class="text-sm mt-2">Clique nos cards acima para navegar para diferentes seções.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";

definePageMeta({
  layout: 'professionals'
});

const toast = useToast();
const loading = ref(false);
const summaryData = ref(null);
const availableYears = ref([]);

const filters = ref({
  year: '',
  month: '',
  start_date: '',
  end_date: ''
});

async function loadOverviewData() {
  try {
    loading.value = true;
    const params = Object.fromEntries(
      Object.entries(filters.value).filter(([_, value]) => value !== '')
    );

    const { data } = await api.get('/financial/', { params });

    if (data.summary) {
      // Calculate aggregated summary from all types
      let totalRecords = 0;
      let totalValue = 0;
      let latestPeriod = '';

      Object.values(data.summary).forEach((item: any) => {
        totalRecords += item.total_records || 0;
        totalValue += item.total_value || 0;
        if (item.latest_period && item.latest_period > latestPeriod) {
          latestPeriod = item.latest_period;
        }
      });

      summaryData.value = {
        total_records: totalRecords,
        total_value: totalValue,
        avg_value: totalRecords > 0 ? totalValue / totalRecords : 0,
        latest_period: latestPeriod
      };
    }

    if (data.available_periods) {
      availableYears.value = [...new Set(data.available_periods.map((p: any) => p.year))].sort((a, b) => b - a);
    }
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados do overview');
  } finally {
    loading.value = false;
  }
}

function applyFilters() {
  loadOverviewData();
}

function clearFilters() {
  filters.value = {
    year: '',
    month: '',
    start_date: '',
    end_date: ''
  };
  loadOverviewData();
}

function refreshData() {
  loadOverviewData();
}

function formatCurrency(value: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

function formatDate(dateString: string) {
  if (!dateString) return '-';
  return new Intl.DateTimeFormat('pt-BR').format(new Date(dateString));
}

onMounted(() => {
  loadOverviewData();
});
</script>
