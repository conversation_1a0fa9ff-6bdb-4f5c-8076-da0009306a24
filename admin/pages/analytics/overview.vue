<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Visão Geral do Sistema</h1>
          <div class="flex gap-2">
            <select-base
              v-model="selectedPeriod"
              :options="periodOptions"
              class="min-w-48"
            />
            <base-button @click="refreshData" :loading="loading" size="sm">
              <ArrowPathIcon class="w-4 h-4 mr-2" />
              Atualizar
            </base-button>
          </div>
        </div>

        <div v-if="loading" class="flex justify-center py-20">
          <loading />
        </div>

        <div v-else class="space-y-6">
          <!-- Main Stats Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                  <UsersIcon class="w-6 h-6 text-blue-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-blue-800">{{ overview.total_professionals || 0 }}</p>
                  <p class="text-sm text-gray-600">Profissionais</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                  <BuildingOfficeIcon class="w-6 h-6 text-green-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-green-800">{{ overview.total_companies || 0 }}</p>
                  <p class="text-sm text-gray-600">Empresas</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                  <UserGroupIcon class="w-6 h-6 text-purple-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-purple-800">{{ overview.total_users || 0 }}</p>
                  <p class="text-sm text-gray-600">Usuários</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                  <CurrencyDollarIcon class="w-6 h-6 text-yellow-600" />
                </div>
                <div class="ml-4">
                  <p class="text-2xl font-bold text-yellow-800">{{ formatCurrency(overview.total_revenue || 0) }}</p>
                  <p class="text-sm text-gray-600">Receita Total</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Today's Stats -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Estatísticas de Hoje</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="text-center p-4 bg-blue-50 rounded">
                <div class="text-2xl font-bold text-blue-600">{{ overview.today?.new_users || 0 }}</div>
                <div class="text-sm text-gray-600">Novos Usuários</div>
              </div>
              <div class="text-center p-4 bg-green-50 rounded">
                <div class="text-2xl font-bold text-green-600">{{ overview.today?.new_appointments || 0 }}</div>
                <div class="text-sm text-gray-600">Agendamentos</div>
              </div>
              <div class="text-center p-4 bg-purple-50 rounded">
                <div class="text-2xl font-bold text-purple-600">{{ formatCurrency(overview.today?.revenue || 0) }}</div>
                <div class="text-sm text-gray-600">Receita</div>
              </div>
              <div class="text-center p-4 bg-yellow-50 rounded">
                <div class="text-2xl font-bold text-yellow-600">{{ overview.today?.active_users || 0 }}</div>
                <div class="text-sm text-gray-600">Usuários Ativos</div>
              </div>
            </div>
          </div>

          <!-- System Health -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white rounded-lg shadow p-6">
              <h2 class="text-lg font-semibold mb-4">Saúde do Sistema</h2>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600">CPU</span>
                  <span class="text-sm font-medium">{{ overview.system_health?.cpu || 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${overview.system_health?.cpu || 0}%`"></div>
                </div>

                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600">Memória</span>
                  <span class="text-sm font-medium">{{ overview.system_health?.memory || 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-green-600 h-2 rounded-full" :style="`width: ${overview.system_health?.memory || 0}%`"></div>
                </div>

                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600">Disco</span>
                  <span class="text-sm font-medium">{{ overview.system_health?.disk || 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-yellow-600 h-2 rounded-full" :style="`width: ${overview.system_health?.disk || 0}%`"></div>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
              <h2 class="text-lg font-semibold mb-4">Relatórios de Bug</h2>
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-4 bg-red-50 rounded">
                  <div class="text-2xl font-bold text-red-600">{{ overview.bug_reports?.total || 0 }}</div>
                  <div class="text-sm text-gray-600">Total</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded">
                  <div class="text-2xl font-bold text-yellow-600">{{ overview.bug_reports?.open || 0 }}</div>
                  <div class="text-sm text-gray-600">Abertos</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded">
                  <div class="text-2xl font-bold text-green-600">{{ overview.bug_reports?.resolved || 0 }}</div>
                  <div class="text-sm text-gray-600">Resolvidos</div>
                </div>
                <div class="text-center p-4 bg-red-50 rounded">
                  <div class="text-2xl font-bold text-red-800">{{ overview.bug_reports?.critical || 0 }}</div>
                  <div class="text-sm text-gray-600">Críticos</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  UserGroupIcon,
  BuildingOfficeIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ArrowPathIcon
} from "@heroicons/vue/24/solid";
import { api, getAnalyticsEndpoint } from "~/server/api";
import { useToast } from "vue-toast-notification";

definePageMeta({
  layout: 'professionals'
});

const toast = useToast();
const loading = ref(false);
const overview = ref({});
const selectedPeriod = ref('week');

const periodOptions = [
  { value: 'day', label: 'Últimos 7 dias' },
  { value: 'week', label: 'Últimas 8 semanas' },
  { value: 'month', label: 'Últimos 12 meses' },
  { value: 'year', label: 'Últimos 5 anos' }
];

function formatCurrency(value: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

async function loadOverview() {
  try {
    loading.value = true;
    const { data } = await api.get(getAnalyticsEndpoint('/overview'));
    overview.value = data;
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados de visão geral');
    // Provide fallback data for demo purposes
    overview.value = {
      total_professionals: 45,
      total_companies: 12,
      total_users: 234,
      total_revenue: 15420.50,
      today: {
        new_users: 8,
        new_appointments: 23,
        revenue: 1250.00,
        active_users: 67
      },
      system_health: {
        cpu: 45,
        memory: 62,
        disk: 78
      },
      bug_reports: {
        total: 15,
        open: 3,
        resolved: 12,
        critical: 1
      }
    };
  } finally {
    loading.value = false;
  }
}

async function refreshData() {
  await loadOverview();
}

onMounted(() => {
  loadOverview();
});
</script>
