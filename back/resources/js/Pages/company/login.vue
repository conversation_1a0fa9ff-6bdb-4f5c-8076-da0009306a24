<template>
    <component :is="slug ? AppLayout : BaseLayout" :slug="slug" :company="company">
        <div class="flex items-center justify-center h-full">
            <div class="card mx-auto w-full max-w-5xl shadow-xl">
                <div class="hero min-h-full rounded-l-xl px-4 hidden md:hidden">
                    <img
                        src="/icons/logo.png"
                        alt="Dashwind Admin Template"
                        class="w-72 inline-block mb-10 -mt-16 md:pt-0"
                    />
                </div>
                <div class="grid md:grid-cols-2 grid-cols-1 px-2 rounded-xl">
                    <div class="hero min-h-full rounded-l-xl bg-base-200">
                        <img
                            src="/icons/logo.png"
                            alt="Dashwind Admin Template"
                            size="512"
                            class="w-96 md:pt-0 hidden md:block"
                        />
                    </div>
                    <div
                        class="pt-4 pb-6 sm:py-12 px-4 sm:px-10 bg-base-200 rounded-xl md:rounded-l-none md:rounded-r-xl"
                    >
                        <h2 class="text-2xl font-semibold mb-2 text-center">
                            Login
                        </h2>
                        <div
                            v-if="invalidCredentials"
                            class="text-red-500 font-bold bg-white rounded-xl text-center mb-2"
                        >
                            Email e senha não coincidem
                        </div>
                        <login-form
                            @submit="handleFormSubmit"
                            :loading="loading"
                        />
                    </div>
                </div>
            </div>
        </div>
    </component>
</template>
<script setup lang="ts">
import axios from "axios";
import { useToast } from "vue-toast-notification";
import { api } from "@/server/api";
import { useUser } from "@/store/user";
import AppLayout from "@/Layouts/AppLayout.vue";
import { router } from "@inertiajs/vue3";
import BaseLayout from "@/Layouts/BaseLayout.vue";
import { c } from "vite/dist/node/types.d-aGj9QkWt";

const loading = ref(false);
const toast = useToast();
const userStore = useUser();

const errors = {
    "E_INVALID_AUTH_PASSWORD: Password mis-match": "Senha incorreta",
    "E_INVALID_AUTH_UID: User not found": "Usuário não encontrado",
};

type ErrorKey = keyof typeof errors;

function mapErrorToMessage(error: ErrorKey) {
    return errors[error] || error;
}
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
});
const invalidCredentials = ref(false);
// definePageMeta({
//   auth: {
//     navigateAuthenticatedTo: "/",
//   },
// });
// eslint-disable-next-line
async function handleFormSubmit(e: any) {
    try {
        // check if query params has redirect
        const redirect = route().queryParams.redirect || slug ? "servicos" : "/";
        console.log(redirect)
        loading.value = true;
        invalidCredentials.value = false;
        const { data } = await api.post("/login", e);
        userStore.login(data.token);
        toast.success("Login realizado com sucesso!", { position: "top" });
        if(slug) {
            router.get(`/${company.slug}/${redirect}?checkForm=true`);
        }
        else {
            router.get(`/${redirect}`);
        }
    } catch (err) {
        invalidCredentials.value = true;
        if (axios.isAxiosError(err)) {
            toast.error(
                mapErrorToMessage(err.response?.data.errors[0].message),
                { position: "top" },
            );
        }
    } finally {
        loading.value = false;
    }
}
</script>
