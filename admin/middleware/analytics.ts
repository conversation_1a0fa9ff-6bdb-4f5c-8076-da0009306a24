import { useLoginStore } from "~/store/user";

export default defineNuxtRouteMiddleware((to) => {
  // Only apply to analytics routes
  if (!to.path.startsWith('/analytics')) {
    return;
  }

  // Only run on client side to avoid hydration issues
  if (process.client) {
    const loginStore = useLoginStore();

    // Ensure store is properly hydrated
    loginStore.hydrateFromPersistedState();

    // Check if user is logged in using Pinia store only
    if (!loginStore.isLoggedIn) {
      return navigateTo("/login");
    }

    // For now, allow all logged-in users to access analytics
    // TODO: Re-enable permission check when backend is ready
    // if (!loginStore.hasAnalyticsAccess) {
    //   return navigateTo("/");
    // }
  }
});
