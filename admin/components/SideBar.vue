<template>
  <div :class="toggledBar ? 'w-[60px] overflow-hidden' : 'w-64'"
    class="shadow-xl transition-all ease-in-out bg-base-100 hidden lg:block relative">
    <label for="my-drawer" class="drawer-overlay"></label>
    <ul :class="toggledBar ? 'w-[58px]' : 'w-64'" class="bg-base-100 menu p-0 text-base gap-0 h-full">
      <div class="flex items-center pl-2 pt-3 pb-1 -ml-1">
        <nuxt-img loading="lazy" class="-mt-1 ml-1" :src="`${runtime.public.API_URL}/icons/logo.png`" width="50" />
        <div v-if="!toggledBar" class="text-3xl font-bold ml-2 -mt-1 text-primary">
          Psy +
        </div>
        <!-- <nuxt-img
          loading="lazy"
          :src="`${runtime.public.API_URL}/icons/logo/logo-text${isLight ? '' : '-branco'}.png`"
          width="105"
        /> -->
      </div>
      <li class="my-1" v-for="{ to, label, icon, items, ref, classes } in routes" :key="label">
        <details :ref="ref" @click="toggledBar = false" v-if="items" :open="open">
          <summary class="font-semibold">
            <component :is="icon" class="w-5 h-5 mt-[2px]" :class="toggledBar ? '-ml-[11px]' : ''"></component>
            <span v-if="!toggledBar">
              {{ label }}
            </span>
          </summary>
          <ul>
            <li class="font-semibold cursor-pointer" :class="[item.to === route.path
              ? 'bg-primary hover:bg-primary rounded-r-full border-r-2 border-primary'
              : '', item.to === route.path && isLight ? '!text-white' : 'text-black']
              " v-for="(item, i) in items" :key="i">
              <nuxt-link :class="[item.to === route.path ? isLight ? '!text-white' : 'text-black' : '']"
                :to="item.to">{{ item.label
                }}</nuxt-link>
            </li>
          </ul>
        </details>
        <div v-else @click="router.push(`${to}`)"
          class="flex font-semibold items-center py-2 pr-1 cursor-pointer relative" :class="[
            to === route.path
              ? 'bg-primary hover:bg-primary  rounded-r-full border-r-2 border-primary'
              : '',
            toggledBar ? 'pl-0' : '',
            to === route.path ? isLight ? '!text-white' : 'text-black' : '!'
          ]">
          <custom-icon v-if="typeof icon === 'string'" :url="icon"
            :class="`${classes} ${toggledBar ? 'mx-auto' : '-ml-[2px]'} `"
            :style="{ filter: to === route.path ? isLight ? 'invert(1)' : 'invert(0)' : isLight ? 'invert(0)' : 'invert(0.7)' }"
            class="w-5 h-5"></custom-icon>
          <component v-else :is="icon" :class="toggledBar ? 'mx-auto' : ''" class="w-5 h-5"></component>

          <a v-if="!toggledBar">{{ label }}</a>
        </div>
      </li>
      <div @click="logout()" class="flex font-semibold items-center py-3 cursor-pointer"
        :class="!toggledBar ? 'pl-4 gap-2' : '-ml-1'">
        <ArrowLeftOnRectangleIcon :class="!toggledBar ? '' : 'mx-auto '" class="w-5 h-5"></ArrowLeftOnRectangleIcon>

        <a v-if="!toggledBar">Sair</a>
      </div>
      <!-- <li><a></a></li> -->
    </ul>
  </div>
</template>

<script setup lang="ts">
import { useDrawer } from "@/store/drawer";
import { useLoginStore } from "@/store/user";
const loginStore = useLoginStore();
const router = useRouter();
const route = useRoute();
const open = ref(false);
const drawer = useDrawer();
const runtime = useRuntimeConfig();
const { toggledBar } = storeToRefs(drawer);
function logout() {
  loginStore.logout();
  // Navigation is handled by the store
}
const colorMode = useColorMode();
const isLight = computed(() => colorMode.preference === "light");
const financasRef = ref();
// const appRef = ref();
const stockRef = ref();
watch(toggledBar, (val) => {
  if (val) {
    if (financasRef.value) financasRef.value[0].open = false;
    // appRef.value[0].open = false;
    if (stockRef.value) stockRef.value[0].open = false;
  } else {
    handleToggleOption();
  }
});
function handleToggleOption() {
  const financeiro = ["contas", "relatorio"];
  // const app = ["cores", "landing-page"];
  const stock = ["produtos", "movimentacao-produtos"];
  const routeName = route.path.split("/")[1];
  if (financeiro.includes(routeName)) {
    if (financasRef.value) financasRef.value[0].open = true;
  }
  // if (app.includes(routeName)) {
  //   appRef.value[0].open = true;
  // }
  if (stock.includes(routeName)) {
    if (stockRef.value) stockRef.value[0].open = true;
  }
}
onMounted(() => {
  handleToggleOption();
});
import {
  CalendarDaysIcon,
  CurrencyDollarIcon,
  ArrowLeftOnRectangleIcon,
} from "@heroicons/vue/24/solid";
import {
  ClipboardDocumentListIcon,
  ClipboardIcon,
  UserGroupIcon,
  Cog8ToothIcon,
  ChatBubbleLeftIcon,
  ClockIcon,
} from "@heroicons/vue/24/outline";
export type Routes = {
  to?: string;
  label: string;
  icon: typeof CurrencyDollarIcon | string;
  classes?: string;
  items?: {
    label: string;
    to: string;
  }[];
  ref?: string;
};
const routes = computed(() => {
  const routes: Routes[] = [
    {
      to: "/analytics/overview",
      label: "Visão Geral",
      icon: ClipboardDocumentListIcon,
    },
    {
      to: "/analytics/usuarios",
      label: "Usuários",
      icon: UserGroupIcon,
    },
    {
      to: "/analytics/agendamentos",
      label: "Agendamentos",
      icon: CalendarDaysIcon,
    },
    {
      to: "/analytics/receita",
      label: "Receita",
      icon: CurrencyDollarIcon,
    },
    {
      to: "/analytics/acesso",
      label: "Acesso",
      icon: Cog8ToothIcon,
    },
    {
      to: "/analytics/bugs",
      label: "Bugs",
      icon: ClipboardIcon,
    },
    {
      to: "/analytics/google",
      label: "Google Analytics",
      icon: ClipboardDocumentListIcon,
    },
    {
      to: "/profissionais",
      label: "Profissionais",
      icon: UserGroupIcon,
    },
    {
      to: "/pacientes",
      label: "Pacientes",
      icon: UserGroupIcon,
    },
    {
      to: "/solicitacoes",
      label: "Solicitações",
      icon: ClipboardIcon,
    },
    // {
    //   to: "/cores",
    //   label: "Meu aplicativo",
    //   ref: "appRef",
    //   icon: DevicePhoneMobileIcon,
    //   // items: [
    //   //   {
    //   //     label: "Cores",
    //   //     to: "/cores",
    //   //   },
    //   //   {
    //   //     label: "Pagina inicial",
    //   //     to: "/landing-page",
    //   //   },
    //   // ],
    // },
    // {
    //   label: "Finanças",
    //   icon: CurrencyDollarIcon,
    //   ref: "financasRef",

    //   items: [
    //     {
    //       label: "Contas a pagar",
    //       to: "/contas",
    //     },
    //     {
    //       label: "Relatórios",
    //       to: "/relatorio",
    //     },
    //     {
    //       label: "Movimentações",
    //       to: "/movimentacoes",
    //     },
    //   ],
    // },
    // {
    //   to: "",
    //   label: "Estoque",
    //   icon: ShoppingBagIcon,
    // },
  ];
  // if (user.isWorker) {
  //   return routes.filter((route) => !routesToRestrict.includes(route.label));
  // }
  return routes;
});
// function toggleBar() {
//   toggledBar.value = !toggledBar.value;
// }
</script>
