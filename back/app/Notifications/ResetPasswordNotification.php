<?php

namespace App\Notifications;

use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class ResetPasswordNotification extends Notification
{
    public $token;

    /**
     * Crie uma nova instância de notificação.
     */
    public function __construct($token)
    {
        $this->token = $token;
    }

    /**
     * Determine os canais de entrega da notificação.
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Crie a mensagem de e-mail.
     */
    public function toMail($notifiable)
    {
        $url = env("ADMIN_URL", "");
        $resetUrl = "{$url}/recover-password?token={$this->token}";

        return (new MailMessage)
            ->subject('Recupere sua senha')
            ->greeting('Olá!')
            ->line('Você está recebendo este e-mail porque recebemos um pedido de recuperação de senha para sua conta.')
            ->action('Redefinir Senha', $resetUrl)
            ->line('Se você não solicitou a redefinição de senha, ignore este e-mail.')
            ->salutation('Atenciosamente, Psy+.');
    }
}
