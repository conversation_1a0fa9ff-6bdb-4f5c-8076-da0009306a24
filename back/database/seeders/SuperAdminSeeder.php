<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if super admin already exists
        $existingSuperAdmin = User::where('type', 'super_admin')->first();
        
        if ($existingSuperAdmin) {
            $this->command->info('Super admin already exists: ' . $existingSuperAdmin->email);
            return;
        }

        // Create super admin user
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'phone' => '+55 11 99999-9999',
            'password' => Hash::make('admin123'),
            'type' => 'super_admin',
            'profile_photo_path' => 'https://ui-avatars.com/api/?name=Super+Admin',
            'email_verified_at' => now(),
        ]);

        $this->command->info('Super admin created successfully!');
        $this->command->info('Email: ' . $superAdmin->email);
        $this->command->info('Password: admin123');
        $this->command->warn('Please change the password after first login!');
    }
}
