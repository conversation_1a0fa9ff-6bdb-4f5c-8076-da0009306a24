<template>
    <Head title="Home" />
    <BaseLayout>
        <div class="py-2 md:py-10 bg-[#eee] w-full h-layout">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 px-4">
                <!-- Hero Section -->
                <div class="overflow-hidden shadow-xl bg-primary sm:rounded-lg p-8 text-white mb-8">
                    <div class="flex flex-col md:flex-row items-center">
                        <div class="md:w-2/3">
                            <h1 class="text-3xl md:text-4xl font-bold mb-4">Bem-vindo ao Psy+</h1>
                            <p class="text-lg mb-6">
                                Conectando você aos melhores profissionais de saúde mental para cuidar do seu bem-estar.
                            </p>
                            <Link href="/profissionais" class="btn bg-white text-primary hover:bg-gray-100 border-none">
                                Buscar profissionais
                            </Link>
                        </div>
                        <!-- <div class="md:w-1/3 mt-6 md:mt-0 flex justify-center">
                            <img src="/icons/mental-health.svg" alt="Saúde Mental" class="w-48 h-48" />
                        </div> -->
                    </div>
                </div>

                <!-- Features Section -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white overflow-hidden shadow-md rounded-lg p-6">
                        <div class="flex items-center mb-4 gap-4">
                            <div class="btn btn-primary">
                                <MagnifyingGlassIcon class="w-6 h-6" />
                            </div>
                 
                            <h2 class="text-xl leading-6 font-semibold text-gray-700">Encontre <br> Especialistas</h2>
                        </div>
                        <p class="text-gray-600">
                            Busque profissionais qualificados por especialidade, localização ou disponibilidade.
                        </p>
                    </div>

                    <div class="bg-white overflow-hidden shadow-md rounded-lg p-6">
                        <div class="flex items-center mb-4 gap-4">
                            <div class="btn btn-primary">
                                <CalendarDaysIcon class="w-6 h-6" />
                            </div>
                            <h2 class="text-xl leading-6 font-semibold text-gray-700">Agende <br> Consultas</h2>
                        </div>
                        <p class="text-gray-600">
                            Marque consultas de forma rápida e fácil, escolhendo o horário que melhor se adapta à sua rotina.
                        </p>
                    </div>

                    <div class="bg-white overflow-hidden shadow-md rounded-lg p-6">
                        <div class="flex items-center mb-4 gap-4">
                            <div class="btn btn-primary">
                                <PresentationChartLineIcon class="w-6 h-6" />
                            </div>
                            <h2 class="text-xl leading-6 font-semibold text-gray-700">Acompanhe <br> seu Progresso</h2>
                        </div>
                        <p class="text-gray-600">
                            Tenha acesso ao histórico de consultas e acompanhe sua evolução ao longo do tempo.
                        </p>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="bg-white overflow-hidden shadow-md rounded-lg p-8 text-center">
                    <h2 class="text-2xl font-semibold text-gray-700 mb-4">Comece sua jornada de bem-estar hoje</h2>
                    <p class="text-gray-600 mb-6 max-w-3xl mx-auto">
                        Cuidar da saúde mental é tão importante quanto cuidar da saúde física. Dê o primeiro passo para uma vida mais equilibrada e saudável.
                    </p>
                    <div class="flex justify-center space-x-4">
                        <Link href="/profissionais" class="btn btn-primary">
                            Buscar profissionais
                        </Link>
                        <!-- <Link href="/perfil" class="btn btn-outline btn-primary">
                            Meu perfil
                        </Link> -->
                    </div>
                </div>
            </div>
        </div>

        <footer class="shadow-xl py-8">
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-3 gap-8 text-center md:text-left">
                    <div>
                        <h4 class="font-bold text-lg mb-4">Psy +</h4>
                        <p class="text-sm">Conectando pessoas a profissionais de saúde mental</p>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg mb-4">Links Úteis</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary">Sobre Nós</a></li>
                            <li><a href="#" class="hover:text-primary">Contato</a></li>
                            <li><a href="#" class="hover:text-primary">Blog</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg mb-4">Contato</h4>
                        <p class="text-sm"><EMAIL></p>
                    </div>
                </div>
                <div class="text-center mt-8 text-sm">
                    <p>&copy; 2025 Psy +. Todos os direitos reservados.</p>
                </div>
            </div>
        </footer>
    </BaseLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import BaseLayout from '@/Layouts/BaseLayout.vue';
import { CalendarDaysIcon, MagnifyingGlassIcon } from '@heroicons/vue/24/solid';
import { PresentationChartLineIcon } from '@heroicons/vue/24/outline';

</script>
