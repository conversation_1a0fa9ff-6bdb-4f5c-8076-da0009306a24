<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class CompanyProfessional extends Model
{
    use HasFactory;
    protected $hidden = [
        'company_id',
    ];
    protected $fillable = [
        'user_id',
        'company_id',
        'commission',
        'about',
        'specialties',
        'public',
        'slug',
        'CRP',
        'hasQuestionary'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'hasQuestionary' => 'boolean',
    ];

    /**
     * Relationship with Order.
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Relationship with Questions.
     */
    public function questions()
    {
        return $this->hasMany(Question::class, 'company_professional_id');
    }

    /**
     * Scope a query to filter professionals by name.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $name
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilterByName(Builder $query, $name)
    {
        if ($name) {
            return $query->whereHas('user', function ($q) use ($name) {
                $q->where('name', 'like', '%' . $name . '%');
            });
        }

        return $query;
    }

    /**
     * Scope a query to filter professionals by specialties.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  array|string  $specialties
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilterBySpecialties(Builder $query, $specialties)
    {
        if (empty($specialties)) {
            return $query;
        }

        if (is_string($specialties)) {
            $specialties = explode(',', $specialties);
        }

        return $query->where(function ($q) use ($specialties) {
            foreach ($specialties as $specialty) {
                $q->orWhere('specialties', 'like', '%' . trim($specialty) . '%');
            }
        });
    }

    /**
     * Scope a query to filter professionals by public types.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  array|string  $publicTypes
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilterByPublicTypes(Builder $query, $publicTypes)
    {
        if (empty($publicTypes)) {
            return $query;
        }

        if (is_string($publicTypes)) {
            $publicTypes = explode(',', $publicTypes);
        }

        return $query->where(function ($q) use ($publicTypes) {
            foreach ($publicTypes as $publicType) {
                $q->orWhere('public', 'like', '%' . trim($publicType) . '%');
            }
        });
    }

    /**
     * Scope a query to only include active professionals.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('status', 'active');
    }
}
