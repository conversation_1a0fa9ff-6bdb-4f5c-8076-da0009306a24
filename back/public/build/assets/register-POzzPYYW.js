import{_ as E}from"./BaseButton-CxEL1ccK.js";import{a as N,_ as T}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{c as B,a as f,b as A,u as F}from"./vee-validate-BgJnH8aY.js";import{l as S,o as k,c as j,a,b as n,u as o,y as g,w as y,i as q,d as D,q as O,r as z,j as L,B as P,N as V,z as R}from"./app-BkKCG4YJ.js";import{b as J,c as M,_ as G,a as $}from"./BaseLayout--Fu-QUOh.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const H={class:"flex flex-col sm:grid sm:grid-cols-2 gap-3 w-full"},I={class:"flex w-full col-span-2 gap-3"},K={class:"text-right text-primary"},Q=S({__name:"Form",props:{slug:{type:String},loading:{type:Boolean}},emits:["submit"],setup(i,{emit:x}){J();const b=x,u=B().shape({name:f().required("O nome é obrigatório"),email:f().email("Email inválido"),phone:f().required("Número de telefone é obrigatório"),password:f().required("A senha do produto é obrigatória").min(6,"A senha deve ter no mínimo 6 caracteres"),password_confirmation:f().oneOf([A("password")],"Passwords must match")}),{handleSubmit:w,defineField:d,errors:m}=F({validationSchema:u}),[l]=d("email"),[s]=d("password"),[r]=d("password_confirmation"),[p]=d("name"),[h]=d("phone");function U(v){b("submit",v)}const _=w(U);return(v,e)=>{const c=N,C=E;return k(),j("form",{class:"flex flex-col gap-3",onSubmit:e[5]||(e[5]=(...t)=>o(_)&&o(_)(...t))},[a("div",H,[n(c,{class:"col-span-2",modelValue:o(p),"onUpdate:modelValue":e[0]||(e[0]=t=>g(p)?p.value=t:null),error:o(m).name,label:"Nome"},null,8,["modelValue","error"]),n(c,{modelValue:o(l),"onUpdate:modelValue":e[1]||(e[1]=t=>g(l)?l.value=t:null),error:o(m).email,label:"Email",class:""},null,8,["modelValue","error"]),n(c,{modelValue:o(h),"onUpdate:modelValue":e[2]||(e[2]=t=>g(h)?h.value=t:null),error:o(m).phone,label:"Telefone","data-maska":"## #####-####",class:""},null,8,["modelValue","error"]),a("div",I,[n(c,{error:o(m).password,modelValue:o(s),"onUpdate:modelValue":e[3]||(e[3]=t=>g(s)?s.value=t:null),label:"Senha",type:"password"},null,8,["error","modelValue"]),n(c,{error:o(m).password_confirmation,modelValue:o(r),"onUpdate:modelValue":e[4]||(e[4]=t=>g(r)?r.value=t:null),label:"Confirmar senha",type:"password"},null,8,["error","modelValue"])])]),a("div",K,[n(o(q),{href:"login"},{default:y(()=>e[6]||(e[6]=[a("span",{class:"font-black my-1 inline-block hover:text-primary hover:underline hover:cursor-pointer transition duration-200"}," Já possui registro? Entrar ",-1)])),_:1})]),n(C,{loading:i.loading,type:"submit",class:"mt-2 w-full"},{default:y(()=>e[7]||(e[7]=[D(" Registrar ")])),_:1},8,["loading"])],32)}}}),W={class:"flex items-center justify-center h-full"},X={class:"card mx-auto w-full max-w-5xl shadow-xl"},Y={class:"grid md:grid-cols-2 grid-cols-1 px-2 rounded-xl"},Z={class:"pt-4 pb-6 sm:py-12 px-4 sm:px-10 bg-base-200 rounded-xl md:rounded-l-none md:rounded-r-xl"},ne=S({__name:"register",props:{slug:{type:String},company:{type:Object}},setup(i){const x=M(),b=O.useToast(),u=z(!1),w={"User already exists":"Usuário já existe"};function d(l){return w[l]||l}async function m(l){var s;try{u.value=!0,await $.post("/register",l);const r={identifier:l.phone,password:l.password,isPhone:!0},{data:p}=await $.post("/login",r);x.login(p.token),b.success("Cadastro realizado com sucesso!",{position:"top"}),i.slug?V.get(`/${i.slug}/servicos`):V.get("/")}catch(r){R.isAxiosError(r)&&b.error(d((s=r.response)==null?void 0:s.data.message),{position:"top"})}finally{u.value=!1}}return(l,s)=>{const r=Q;return k(),L(P(i.slug?T:G),{slug:i.slug,company:i.company},{default:y(()=>[a("div",W,[a("div",X,[s[2]||(s[2]=a("div",{class:"hero min-h-full rounded-l-xl px-4 hidden md:hidden"},[a("img",{src:"/icons/logo.png",alt:"Dashwind Admin Template",class:"w-72 inline-block -mt-8 md:pt-0"})],-1)),a("div",Y,[s[1]||(s[1]=a("div",{class:"hero min-h-full rounded-l-xl bg-base-200"},[a("img",{src:"/icons/logo.png",alt:"Dashwind Admin Template",class:"w-96 md:pt-0 hidden md:block"})],-1)),a("div",Z,[s[0]||(s[0]=a("h2",{class:"text-2xl font-semibold mb-2 text-center"}," Cadastre-se ",-1)),n(r,{onSubmit:m,loading:o(u)},null,8,["loading"])])])])])]),_:1},8,["slug","company"])}}});export{ne as default};
