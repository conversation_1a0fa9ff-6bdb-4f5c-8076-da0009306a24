<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Movement extends Model
{
    use HasFactory;
    protected $hidden = [
        'company_id',
    ];
    protected $fillable = [
        'company_id',
        'name',
        'category',
        'type',
        'notes',
        'total',
        'stock_movement',
        'quantity',
        'total_spent',
        'total_earned',
        'reason'
    ];

    /**
     * Relacionamento com o produto.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
