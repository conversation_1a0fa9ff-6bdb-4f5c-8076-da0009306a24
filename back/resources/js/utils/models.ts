export interface Cash {
  id: string;
  name: string;
  quantity: number;
  price: string;
  created_at: string;
  updated_at: string;
}
export interface Finance {
  id?: number;
  name: string;
  value: number;
  isExpense: boolean;
  initialDate: string;
  recurrent: boolean;
  recurrentFinalDate?: string;
  infiniteRecurrency?: boolean;
}
export interface Date {
  month: number;
  year: number;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  price: string;
  duration: number;
  created_at: string;
  updated_at: string;
  products: Product[]
}
export interface Product {
    id?: number;
    name: string;
    base_price: number;
    quantity: number;
    description?: string;
    variants?: Variant[];
    hasVariants?: boolean;
  }
  export interface Variant {
    id: number;
    number: string;
    name: string;
    additional_price: number;
    price: number;
    quantity: number;
    is_active: boolean;
  }
export interface Order {
  id?: number;
  status: string;
  user_id: string;
  chat_id?: string;
  admin_id: null | string;
  subtotal: string;
  discount: string;
  total: string;
  finished_at: null | string;
  created_at: string;
  items: ItemOrder[];
}

export interface ItemOrder {
  id: number;
  order_id: number;
  product_name: string;
  product_id: string;
  product_quantity: number;
  product_price: number;
  product_type: string;
  quantity: number;
  discount: string;
  total: string;
  created_at: string;
  updated_at: string;
  status: string;
  user_name: string;
}
export interface Chat {
  id: string;
  admin_name?: string;
  admin_image?: string;
  user_name: string;
  user_image?: string;
  chatId?: string;
  newMessage?: NewMessage;
  status?: string;
  order?: string;
}
export interface Message {
  message: string;
  chatId: string;
  type: string;
  from: string;
  to: string;
  created_at: string;
}
export interface NewMessage {
  message: string;
  created_at: string;
}
export interface ChatItem {
  id: string;
  admin_name: string;
  user_name: string;
  newMessage?: NewMessage;
  isTyping?: boolean;
}
export interface NewMessage {
  message: string;
  created_at: string;
}

export interface Professional {
  user: {
    name: string;
    id: string;
    image: string;
  },
  id: number,
  hasQuestionary?: boolean
}
export interface Schedule {
  service: Service;
  professional: Professional;
  hour: string;
  date: string;
  id: string;
}
interface OrderItem {
  id: number;
  order_id: number;
  product_name: string;
  product_id: string;
  product_quantity: number;
  product_price: number;
  product_type: string;
  quantity: number;
  discount: string;
  total: string;
  created_at: string;
  updated_at: string;
}

interface Admin {
  id: string;
  email: string;
  image: string | null;
  name: string;
  remember_me_token: string | null;
  is_worker: number;
  created_at: string;
  updated_at: string;
}
interface Appointment {
  start_date: string;
  end_date: string;
  professional: { user: Professional };
  service: Service;
}
export interface newOrder {
  id: number;
  status: string;
  user_id: string;
  appointments: Appointment[];
  admin_id: string;
  subtotal: string;
  discount: string;
  total: string;
  chat_id: string | null;
  finished_at: string | null;
  created_at: string;
  updated_at: string;
  items: OrderItem[];
  admin: Admin;
}
