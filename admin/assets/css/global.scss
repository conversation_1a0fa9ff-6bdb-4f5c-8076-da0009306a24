.h-sm {
  height: calc(100svh - 48px);
}



.h-navigation {
  height: calc(100svh - 168px);
}
.navbar {
  padding: 0px 4px !important;
  min-height: 0px;
}
.input {
  height: 2.4rem !important;
  padding-left: 0.7rem;
  padding-right: 0.7rem;
}
.select {
  height: 2.5rem !important;
  min-height: 2.5rem !important;
}
::-webkit-calendar-picker-indicator {
  filter: invert(1);
  cursor: pointer;
}
/* width */
::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgb(176, 176, 176);
  background: rgb(198, 198, 198);
  border-radius: 5px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: hsl(var(--b2, var(--b1)) / 1);;
  box-shadow: inset 0 0 5px rgb(217, 214, 214);
  border-radius: 5px;
}
.label{
  padding-bottom: 0.3rem;
  font-weight: 500;
}
