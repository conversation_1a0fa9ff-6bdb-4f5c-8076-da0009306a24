<template>
    <div class="drawer" :class="route().current() === 'index' ? 'drawer-h' : ''">
        <input
            id="my-drawer"
            type="checkbox"
            class="drawer-toggle"
            v-model="drawer.active"
        />
        <div class="bg-base-100 drawer-content">
            <slot></slot>
        </div>
        <div class="drawer-side shadow-xl">
            <label for="my-drawer" class="drawer-overlay"></label>
            <div
                class="w-96 h-full bg-base-100 text-base-content divide-gray-500 flex flex-col"
            >
                <ul class="">
                    <!-- Sidebar content here -->
                    <div
                        class="relative"
                        v-for="({ to, label, icon, name }, i) in routes"
                        :key="i"
                    >
                        <li
                            @click="
                                router.get(to);
                                drawer.toggleActive();
                            "
                            class="flex text-lg items-center py-3 pl-3 pr-1 cursor-pointer"
                            :class="
                                name === route().current()
                                    ? 'bg-base-200 border-r-2 border-primary'
                                    : 'hover:bg-base-200'
                            "
                        >
                            <component
                                :is="icon"
                                class="w-5 h-5 mr-2"
                            ></component>

                            <a>{{ label }}</a>
                            <span
                                v-if="name === 'chat' && chatStore.newMessage"
                                class="absolute top-0 right-2 flex h-3 w-3"
                            >
                                <span
                                    class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"
                                ></span>
                                <span
                                    class="relative inline-flex rounded-full h-3 w-3 bg-primary"
                                ></span>
                            </span>
                        </li>
                    </div>

                    <div v-if="user.isLoggedIn">
                        <li
                            @click="
                                user.logout(slug);
                                router.get(`/${slug}/login`);
                                drawer.toggleActive();
                            "
                            class="flex text-lg items-center py-3 pl-3 pr-1 cursor-pointer hover:bg-gray-600"
                        >
                            <ArrowLeftOnRectangleIcon class="w-5 h-5 mr-2" />
                            <a>Sair</a>
                        </li>
                    </div>
                    <div v-else>
                        <li
                            @click="
                                router.get(`/${slug}/login`);
                                drawer.toggleActive();
                            "
                            class="flex text-lg items-center py-3 pl-3 pr-1 cursor-pointer"
                            :class="
                                'login' === route().current()
                                    ? 'bg-base-200 border-r-2 border-primary'
                                    : 'hover:bg-gray-600'
                            "
                        >
                            <ArrowRightOnRectangleIcon class="w-5 h-5 mr-2" />
                            <a>Entrar</a>
                        </li>
                    </div>
                </ul>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useDrawer } from "@/store/drawer";
import {
    ArrowRightOnRectangleIcon,
    ArrowLeftOnRectangleIcon,
} from "@heroicons/vue/24/solid";
import { useChat } from "@/store/chat";
import { useUser } from "@/store/user";
import {
    BookmarkSquareIcon,
    UserIcon,
    ScissorsIcon,
} from "@heroicons/vue/24/solid";
import { router } from "@inertiajs/vue3";

const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
});
const routes = [
    {
        to: `/${slug}/servicos`,
        label: "Agendamento",
        icon: ScissorsIcon,
        name: "servicos"
    },
    {
        to: `/${slug}/pedidos`,
        label: "Agendamentos",
        icon: BookmarkSquareIcon,
        name: "pedidos"
    },
    // {
    //   to: "/feed",
    //   label: "Feed",
    //   icon: CameraIcon,
    // },
    {
        to: `/${slug}/perfil`,
        label: "Perfil",
        icon: UserIcon,
        name: "perfil"
    },
];
const user = useUser();
const chatStore = useChat();
const drawer = useDrawer();
</script>
