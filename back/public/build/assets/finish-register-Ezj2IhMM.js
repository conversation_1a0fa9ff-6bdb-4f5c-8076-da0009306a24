import{_ as O}from"./BaseButton-CxEL1ccK.js";import{a as P,_ as A}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{c as T,a as g,b as L,u as z}from"./vee-validate-BgJnH8aY.js";import{l as E,r as y,h as F,o as x,c as q,a as i,b as c,u as o,y as v,w as j,d as I,q as M,j as C,t as R,g as N,N as G,z as H}from"./app-BkKCG4YJ.js";import{c as J,a as S}from"./BaseLayout--Fu-QUOh.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const K={class:"flex flex-col sm:grid sm:grid-cols-2 gap-3 w-full"},Q={class:"flex w-full col-span-2 gap-3"},W=E({__name:"Form",props:{userData:{type:Object,default:()=>({})},loading:{type:Boolean,default:!1}},emits:["submit"],setup(e,{emit:D}){const b=D,f=T().shape({name:g().required("O nome é obrigatório"),email:g().email("Email inválido").required("Email é obrigatório"),phone:g().required("Número de telefone é obrigatório"),password:g().required("A senha é obrigatória").min(6,"A senha deve ter no mínimo 6 caracteres"),password_confirmation:g().oneOf([L("password")],"As senhas devem ser iguais").required("Confirmação de senha é obrigatória")}),{handleSubmit:d,defineField:t,errors:m,setFieldValue:l}=z({validationSchema:f}),[a]=t("email"),[r]=t("password"),[p]=t("password_confirmation"),[h]=t("name"),[w]=t("phone"),V=y("");F(()=>{console.log("Form component received userData:",e.userData),e.userData?(e.userData.name&&(console.log("Setting name:",e.userData.name),l("name",e.userData.name)),e.userData.email&&(console.log("Setting email:",e.userData.email),l("email",e.userData.email)),e.userData.phone&&(console.log("Setting phone:",e.userData.phone),l("phone",e.userData.phone)),e.userData.fixed_field&&(console.log("Setting fixed field:",e.userData.fixed_field),V.value=e.userData.fixed_field)):console.warn("No userData provided to the form component")});function U($){var u;const s={...$,company_id:((u=e.userData)==null?void 0:u.company_id)||null};b("submit",s)}const k=d(U);return($,s)=>{const u=P,B=O;return x(),q("form",{class:"flex flex-col gap-3",onSubmit:s[5]||(s[5]=(...n)=>o(k)&&o(k)(...n))},[i("div",K,[c(u,{class:"col-span-2",modelValue:o(h),"onUpdate:modelValue":s[0]||(s[0]=n=>v(h)?h.value=n:null),error:o(m).name,label:"Nome"},null,8,["modelValue","error"]),c(u,{modelValue:o(a),"onUpdate:modelValue":s[1]||(s[1]=n=>v(a)?a.value=n:null),error:o(m).email,label:"Email",class:"",disabled:V.value==="email"},null,8,["modelValue","error","disabled"]),c(u,{modelValue:o(w),"onUpdate:modelValue":s[2]||(s[2]=n=>v(w)?w.value=n:null),error:o(m).phone,label:"Telefone","data-maska":"## #####-####",class:"",disabled:V.value==="phone"},null,8,["modelValue","error","disabled"]),i("div",Q,[c(u,{error:o(m).password,modelValue:o(r),"onUpdate:modelValue":s[3]||(s[3]=n=>v(r)?r.value=n:null),label:"Senha",type:"password"},null,8,["error","modelValue"]),c(u,{error:o(m).password_confirmation,modelValue:o(p),"onUpdate:modelValue":s[4]||(s[4]=n=>v(p)?p.value=n:null),label:"Confirmar senha",type:"password"},null,8,["error","modelValue"])])]),c(B,{loading:e.loading,type:"submit",class:"mt-2 w-full"},{default:j(()=>s[6]||(s[6]=[I(" Completar Cadastro ")])),_:1},8,["loading"])],32)}}}),X={class:"flex items-center justify-center h-full"},Y={class:"card mx-auto w-full max-w-5xl shadow-xl"},Z={class:"grid md:grid-cols-2 grid-cols-1 px-2 rounded-xl"},_={class:"pt-4 pb-6 sm:py-12 px-4 sm:px-10 bg-base-200 rounded-xl md:rounded-l-none md:rounded-r-xl"},ee={key:0,class:"text-error text-center mb-4"},ie=E({__name:"finish-register",props:{slug:{type:String},company:{type:Object},professional:{type:Object},encryptedData:{type:String}},setup(e){const D=J(),b=M.useToast(),f=y(!1),d=y(null),t=y("");F(async()=>{var l;if(console.log("Encrypted data received:",e.encryptedData?`${e.encryptedData.substring(0,50)}...`:"null"),e.encryptedData)try{console.log("Sending data to decrypt...");const a=await S.post("/decrypt-user-data",{data:e.encryptedData});console.log("Decryption response status:",a.status),a.data&&typeof a.data=="object"?(d.value=a.data,console.log("Decrypted user data:",d.value),(!d.value.name||!d.value.email||!d.value.phone)&&(console.error("Decrypted data is missing required fields"),t.value="Dados incompletos. Por favor, solicite um novo link.")):(console.error("Invalid response format:",a.data),t.value="Formato de dados inválido. Por favor, solicite um novo link.")}catch(a){t.value="Dados inválidos ou expirados. Por favor, solicite um novo link.",console.error("Error decrypting data:",((l=a.response)==null?void 0:l.data)||a.message||a)}else t.value="Nenhum dado encontrado. Por favor, solicite um novo link."});async function m(l){var a;try{f.value=!0,await S.post("/complete-registration",l);const r={identifier:l.email,password:l.password,isPhone:!1},{data:p}=await S.post("/login",r);D.login(p.token),b.success("Cadastro completado com sucesso!",{position:"top"}),G.get(`/${e.slug}/servicos`)}catch(r){H.isAxiosError(r)&&b.error(((a=r.response)==null?void 0:a.data.message)||"Erro ao completar o cadastro",{position:"top"})}finally{f.value=!1}}return(l,a)=>{const r=W;return x(),C(A,{slug:e.slug,company:e.company,professional:e.professional},{default:j(()=>[i("div",X,[i("div",Y,[i("div",Z,[a[1]||(a[1]=i("div",{class:"hero min-h-full rounded-l-xl bg-base-200"},[i("img",{src:"/icons/logo.png",alt:"Logo",class:"w-96 md:pt-0 hidden md:block"})],-1)),i("div",_,[a[0]||(a[0]=i("h2",{class:"text-2xl font-semibold mb-2 text-center"}," Complete seu Cadastro ",-1)),o(t)?(x(),q("p",ee,R(o(t)),1)):N("",!0),o(t)?N("",!0):(x(),C(r,{key:1,onSubmit:m,userData:o(d),loading:o(f)},null,8,["userData","loading"]))])])])])]),_:1},8,["slug","company","professional"])}}});export{ie as default};
