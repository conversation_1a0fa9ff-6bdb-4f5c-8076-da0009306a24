<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Payment Analysis</h1>
          <div class="flex gap-2">
            <button @click="refreshData" class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
            <NuxtLink to="/analytics" class="btn btn-ghost btn-sm">
              ← Back to Dashboard
            </NuxtLink>
          </div>
        </div>

        <!-- Filter Controls -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
          <div class="flex flex-wrap gap-4 items-end">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Year</span>
              </label>
              <select v-model="filters.year" class="select select-bordered select-sm">
                <option value="">All Years</option>
                <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Month</span>
              </label>
              <select v-model="filters.month" class="select select-bordered select-sm">
                <option value="">All Months</option>
                <option value="1">January</option>
                <option value="2">February</option>
                <option value="3">March</option>
                <option value="4">April</option>
                <option value="5">May</option>
                <option value="6">June</option>
                <option value="7">July</option>
                <option value="8">August</option>
                <option value="9">September</option>
                <option value="10">October</option>
                <option value="11">November</option>
                <option value="12">December</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Start Date</span>
              </label>
              <input v-model="filters.start_date" type="date" class="input input-bordered input-sm">
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">End Date</span>
              </label>
              <input v-model="filters.end_date" type="date" class="input input-bordered input-sm">
            </div>
            <button @click="applyFilters" class="btn btn-primary btn-sm">Apply</button>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">Clear</button>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-lg"></span>
        </div>

        <!-- Charts Row -->
        <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <!-- Payment Methods Distribution -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-4">Payment Methods Distribution</h3>
            <div v-if="chartData.labels?.length" class="h-64">
              <canvas ref="pieChart"></canvas>
            </div>
            <div v-else class="text-center py-8 text-gray-500">
              No data available
            </div>
          </div>

          <!-- Payment Values -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-4">Payment Values</h3>
            <div v-if="chartData.labels?.length" class="h-64">
              <canvas ref="barChart"></canvas>
            </div>
            <div v-else class="text-center py-8 text-gray-500">
              No data available
            </div>
          </div>
        </div>

        <!-- Summary Stats -->
        <div v-if="paymentData.total" class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">Summary</h3>
          <div class="stat">
            <div class="stat-title">Total Payment Value</div>
            <div class="stat-value text-primary">{{ formatCurrency(paymentData.total) }}</div>
          </div>
        </div>

        <!-- Data Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-6 border-b">
            <h3 class="text-lg font-semibold">Payment Analysis Data</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Payment Method</th>
                  <th>Value</th>
                  <th>Percentage</th>
                  <th>Period</th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="!paymentData.data?.length">
                  <td colspan="4" class="text-center py-8 text-gray-500">
                    No data available
                  </td>
                </tr>
                <tr v-for="item in paymentData.data" :key="item.id">
                  <td class="font-medium">{{ item.category }}</td>
                  <td>{{ formatCurrency(item.value) }}</td>
                  <td>{{ formatPercentage(item.percentage) }}</td>
                  <td>{{ formatDate(item.period) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Upload Section -->
        <div class="bg-white rounded-lg shadow p-6 mt-6">
          <h3 class="text-lg font-semibold mb-4">Upload Payment Analysis Data</h3>
          <form @submit.prevent="uploadFile" class="space-y-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Period</span>
              </label>
              <input v-model="uploadForm.period" type="date" class="input input-bordered" required>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">CSV File</span>
              </label>
              <input @change="handleFileChange" type="file" accept=".csv,.txt" class="file-input file-input-bordered" required>
            </div>
            <button type="submit" :disabled="uploading" class="btn btn-primary">
              <span v-if="uploading" class="loading loading-spinner loading-sm mr-2"></span>
              {{ uploading ? 'Uploading...' : 'Upload' }}
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

definePageMeta({
  layout: 'professionals'
});

interface PaymentItem {
  id: number;
  category: string;
  value: number;
  percentage: number;
  period: string;
}

interface ChartData {
  labels: string[];
  values: number[];
  percentages: number[];
}

const toast = useToast();
const loading = ref(false);
const uploading = ref(false);
const paymentData = ref<{ data: PaymentItem[]; total: number }>({ data: [], total: 0 });
const chartData = ref<ChartData>({ labels: [], values: [], percentages: [] });
const availableYears = ref<number[]>([]);

const filters = ref({
  year: '',
  month: '',
  start_date: '',
  end_date: ''
});

const uploadForm = ref<{ period: string; file: File | null }>({
  period: '',
  file: null
});

const pieChart = ref<HTMLCanvasElement | null>(null);
const barChart = ref<HTMLCanvasElement | null>(null);
let pieChartInstance: Chart | null = null;
let barChartInstance: Chart | null = null;

async function loadPaymentData() {
  try {
    loading.value = true;
    const params = Object.fromEntries(
      Object.entries(filters.value).filter(([, value]) => value !== '')
    );
    
    const { data } = await api.get('/financial/payment-analysis', { params });
    paymentData.value = data;
    chartData.value = data.chart_data || { labels: [], values: [], percentages: [] };
    
    nextTick(() => {
      createCharts();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados de análise de pagamentos');
  } finally {
    loading.value = false;
  }
}

function createCharts() {
  createPieChart();
  createBarChart();
}

function createPieChart() {
  if (pieChartInstance) {
    pieChartInstance.destroy();
  }

  if (!pieChart.value || !chartData.value.labels?.length) return;

  const ctx = pieChart.value.getContext('2d');
  if (!ctx) return;
  
  pieChartInstance = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: chartData.value.labels,
      datasets: [{
        data: chartData.value.percentages,
        backgroundColor: [
          '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
          '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
        ],
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const percentage = context.parsed + '%';
              return `${label}: ${percentage}`;
            }
          }
        }
      }
    }
  });
}

function createBarChart() {
  if (barChartInstance) {
    barChartInstance.destroy();
  }

  if (!barChart.value || !chartData.value.labels?.length) return;

  const ctx = barChart.value.getContext('2d');
  if (!ctx) return;
  
  barChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: chartData.value.labels,
      datasets: [{
        label: 'Value',
        data: chartData.value.values,
        backgroundColor: '#3498db',
        borderColor: '#3498db',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return formatCurrency(Number(value));
            }
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return formatCurrency(context.parsed.y);
            }
          }
        }
      }
    }
  });
}

function applyFilters() {
  loadPaymentData();
}

function clearFilters() {
  filters.value = {
    year: '',
    month: '',
    start_date: '',
    end_date: ''
  };
  loadPaymentData();
}

function refreshData() {
  loadPaymentData();
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  uploadForm.value.file = target.files?.[0] || null;
}

async function uploadFile() {
  if (!uploadForm.value.file || !uploadForm.value.period) {
    toast.error('Please fill in all fields');
    return;
  }

  try {
    uploading.value = true;
    const formData = new FormData();
    formData.append('file', uploadForm.value.file);
    formData.append('type', 'payment_analysis');
    formData.append('period', uploadForm.value.period);

    await api.post('/financial/upload-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    toast.success('File uploaded successfully!');
    uploadForm.value = { period: '', file: null };
    
    // Reset file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
    
    // Refresh data
    setTimeout(() => {
      loadPaymentData();
    }, 1000);
  } catch (error) {
    console.error(error);
    toast.error('Failed to upload file');
  } finally {
    uploading.value = false;
  }
}

function formatCurrency(value: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

function formatPercentage(value: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value / 100);
}

function formatDate(dateString: string) {
  if (!dateString) return '-';
  return new Intl.DateTimeFormat('pt-BR').format(new Date(dateString));
}

onMounted(() => {
  loadPaymentData();
});

onUnmounted(() => {
  if (pieChartInstance) {
    pieChartInstance.destroy();
  }
  if (barChartInstance) {
    barChartInstance.destroy();
  }
});
</script>
