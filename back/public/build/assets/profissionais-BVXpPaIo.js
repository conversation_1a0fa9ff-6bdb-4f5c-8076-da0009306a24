import{_ as $}from"./BaseButton-CxEL1ccK.js";import{i as C}from"./v-lazy-image-8UyCsxZp.js";import{l as x,f as S,o as n,c as f,b as u,u as r,a as s,d as p,t as z,g as v,w as g,n as B,j as w,T as N,D as A,r as P,h as V,N as y,e as j,F as T}from"./app-BkKCG4YJ.js";import{u as b,d as D}from"./BaseLayout--Fu-QUOh.js";import{_ as F}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{r as L}from"./ArrowLeftIcon-CzJvhcLt.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const M={class:"sm:card-body sm:py-2 sm:px-6 sm:w-full"},O={class:"w-full h-full flex flex-col px-2 sm:p-0 py-1"},Q={class:"font-bold line-clamp-2 text-base sm:text-center"},q={key:0,class:"badge badge-sm badge-primary ml-1",title:"Possui questionário"},E=x({__name:"Card",props:{professional:{},cardStyles:{}},emits:["schedule"],setup(e,{emit:m}){const l=m,i=e,d=S(()=>i.professional.user.profile_photo_path?`/${i.professional.user.profile_photo_path}`:i.professional.user.profile_photo_url);return(c,o)=>{const a=$;return n(),f("div",{class:B(["card items-center card-side sm:flex-col w-full shadow-sm sm:w-44 h-20 sm:h-56 bg-base-200 rounded-lg sm:p-2 p-1",`${c.cardStyles}`])},[u(r(C),{class:"w-16 h-16 sm:w-24 sm:h-24 rounded-full",src:d.value},null,8,["src"]),s("div",M,[s("div",O,[s("div",Q,[p(z(c.professional.user.name)+" ",1),c.professional.hasQuestionary?(n(),f("span",q,"Q")):v("",!0)])]),u(a,{color:"black",size:"sm",class:"text-xs mt-auto btn-outline text-center hidden sm:block w-full",onClick:o[0]||(o[0]=t=>l("schedule"))},{default:g(()=>o[2]||(o[2]=[p(" Agendar ")])),_:1})]),u(a,{color:"black",size:"sm",class:"text-xs mt-auto btn-outline sm:hidden ml-auto my-auto",onClick:o[1]||(o[1]=t=>l("schedule"))},{default:g(()=>o[3]||(o[3]=[p(" Agendar ")])),_:1})],2)}}}),H={key:0,class:"absolute top-0 left-0 w-full h-screen backdrop-blur-sm flex z-50"},R=x({__name:"Loading",props:{show:{type:Boolean}},setup(e){return(m,l)=>(n(),w(N,{to:"body"},[e.show?(n(),f("div",H,l[0]||(l[0]=[s("div",{class:"m-auto"},[s("svg",{class:"animate-spin w-10 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})])],-1)]))):v("",!0)]))}}),G={class:"flex flex-wrap sm:gap-10 gap-1 justify-center sm:mt-5 p-1 mt-2 overflow-auto mx-1"},es=x({__name:"profissionais",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(e){const{loading:m,cartDrawerActive:l}=A(b()),{addProfessional:i}=b(),d=P();function c(a){i(a),y.get(`/${e.slug}/horarios`)}async function o(){const{data:a}=await D(e.slug);d.value=a}return V(()=>{o()}),(a,t)=>{const _=E;return n(),w(F,{slug:e.slug,company:e.company,professional:e.professional},{default:g(()=>[s("div",null,[s("div",{onClick:t[0]||(t[0]=h=>r(y).get(`/${e.slug}/servicos`)),class:"ml-5 cursor-pointer font-bold gap-1 text-lg text-accent-content flex items-center mt-3 md:mt-5"},[u(r(L),{class:"w-5 h-5"}),t[1]||(t[1]=p()),t[2]||(t[2]=s("span",null,"Alterar Serviço",-1))]),s("div",G,[(n(!0),f(T,null,j(r(d),(h,k)=>(n(),w(_,{key:k,professional:h,onSchedule:I=>c(h)},null,8,["professional","onSchedule"]))),128))]),u(R,{show:r(m)&&!r(l)},null,8,["show"])])]),_:1},8,["slug","company","professional"])}}});export{es as default};
