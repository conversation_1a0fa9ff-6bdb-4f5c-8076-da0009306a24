import { defineStore } from "pinia";
import { type Chat } from "@/utils/models";
import { api } from "@/server/api";
function convertOrderToChat(order: Order): Chat {
  return {
    id: `${order.id}`,
    admin_name: undefined,
    user_name: "eu",
    newMessage: {} as NewMessage,
    status: "active",
    order: `${order.id}`,
  };
}
export const useChat = defineStore("chat", {
  state: () => ({
    newMessage: false,
    loading: false,
    selectedChat: undefined as Chat | undefined,
    chats: [] as Array<Chat>,
    pendingOrders: [],
  }),
  actions: {
    // async getChats() {
    //   const route = useRoute();
    //   this.loading = true;
    //   try {
    //     const { data } = await api.get("/chat");
    //     const { id } = route.query;
    //     this.chats = data;
    //     if (!id) return;
    //     const [chat] = data.filter((chat: ChatItem) => chat.id === id);
    //     if (chat) this.selectedChat = chat;
    //   } finally {
    //     this.loading = false;
    //   }
    // },
    async getPendingOrders() {
      const { data } = await api.get("/orders");
      const convertedChats = data
        .filter((data: Order) => !data.admin_id)
        .map(convertOrderToChat);
      this.pendingOrders = convertedChats;
    },
  },
  persist: true,
});
