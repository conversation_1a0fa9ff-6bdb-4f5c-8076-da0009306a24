@use "./global.scss";
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .container-table {
    @apply card h-[98%] overflow-hidden flex flex-col bg-base-100 shadow-2xl mx-2 md:mx-2 w-full py-2 md:py-3 px-2 md:px-3;
  }
  .elevated-card {
    @apply card bg-base-100 shadow-xl py-3 px-5;
  }
  .title-card {
    @apply inline-block text-xl font-semibold;
  }
  .container-body {
    @apply h-full bg-base-100 overflow-y-auto;
  }
  .btn-action {
    @apply btn btn-square btn-ghost;
  }
  .btn-create {
    @apply btn btn-primary btn-sm;
  }
}
.table th:first-child {
  position: relative;
}
html {
  color: var('--text-theme')
}
.text-contrast {
  color: var(--contrast-color)
}
