<template>
    <component :is="slug ? AppLayout : BaseLayout" :slug="slug" :company="company" :professional="professional">
        <div
            class="py-2 h-full flex flex-col md:p-6 md:pt-3 space-y-4 w-full"
            :class="loading ? 'h-full overflow-hidden' : ''"
        >
            <div class="tabs tabs-bordered w-full grid grid-cols-2 font-bold">
                <a
                    v-for="(tab, i) in tabs"
                    :key="i"
                    :class="
                        activeTab === tab ? 'tab-active !border-primary' : ''
                    "
                    class="tab text-lg transition-colors"
                    @click="activeTab = tab"
                    >{{ getTab(tab) }}</a
                >
            </div>
            <div class="grid px-2 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-2">
                <skeleton
                    :number="5"
                    classes="h-52 w-full"
                    v-if="loading"
                />
                <template v-else>
                    <order-card
                        v-for="(order, i) in selectedOrders"
                        :key="i"
                        :order="order"
                        :active-tab="activeTab"
                        @refresh="getData"
                    />
                </template>
            </div>
        </div>
    </component>
</template>

<script setup lang="ts">
// eslint-disable-next-line
import Loading from "@/components/Loading.vue";
import AppLayout from "@/Layouts/AppLayout.vue";
import BaseLayout from "@/Layouts/BaseLayout.vue";
import { useOrderStore } from "@/store/order";
import type { newOrder } from "@/utils/models";
const orderStore = useOrderStore();
const tabs = ref(["active", "finished"]);
const activeTab = ref("active");
const orders = ref<newOrder[]>([]);
const loading = ref()
// const currentOrders = computed<Order[]>(() => {
//   return orders.value.filter((order) => {
//     const openedOrders = ["active", "started"];
//     if (activeTab.value === "finished") return order.status === "finished";
//     else return openedOrders.includes(order.status);
//   });
// });
function getTab(status: string) {
    if (status === "finished") return "Finalizados";
    else return "Ativos";
}
async function getData() {
    loading.value = true
    try {
        const data = await orderStore.getAllServices();
        orders.value = data;
    } catch (err) {
        return;
    }
    finally {
        loading.value = false
    }

}
const selectedOrders = computed(() => {
    return orders.value.filter((order) => {
        if (activeTab.value === "finished") return order.finished_at;
        else return !order.finished_at;
    });
});
const { slug } = defineProps({
    slug: { type: String },
    company: { type: Object },
    professional: { type: Object },
});
onMounted(async () => {
    await getData();
});
</script>
