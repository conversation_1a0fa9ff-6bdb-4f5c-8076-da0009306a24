import{o as a,c as i,b as o,u as d,Z as r,w as n,a as s,t as l,F as m}from"./app-BkKCG4YJ.js";import{_ as u}from"./BaseLayout--Fu-QUOh.js";const c={class:"py-6 w-full bg-[#eee] h-layout"},x={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 px-4"},f={class:"bg-white overflow-hidden shadow-xl sm:rounded-lg p-6"},g={class:"mt-6"},_={class:"flex items-center space-x-4"},v={class:"avatar"},b={class:"w-24 h-24 rounded-full"},h=["src"],p={class:"text-xl font-medium text-gray-900"},y={class:"text-gray-500"},w={class:"mt-8 border-t border-gray-200 pt-6"},P={class:"mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2"},k={class:"mt-1 p-2 bg-gray-50 rounded-md"},B={class:"mt-1 p-2 bg-gray-50 rounded-md"},F={__name:"Profile",props:{user:Object},setup(e){return(E,t)=>(a(),i(m,null,[o(d(r),{title:"Perfil"}),o(u,null,{default:n(()=>[s("div",c,[s("div",x,[s("div",f,[t[4]||(t[4]=s("h1",{class:"text-2xl font-semibold text-gray-900"},"Seu Perfil",-1)),s("div",g,[s("div",_,[s("div",v,[s("div",b,[s("img",{src:e.user.profile_photo_url,alt:"Profile Photo"},null,8,h)])]),s("div",null,[s("h2",p,l(e.user.name),1),s("p",y,l(e.user.email),1)])]),s("div",w,[t[2]||(t[2]=s("h3",{class:"text-lg font-medium text-gray-900"},"Informações Pessoais",-1)),s("div",P,[s("div",null,[t[0]||(t[0]=s("label",{class:"block text-sm font-medium text-gray-700"},"Nome",-1)),s("div",k,l(e.user.name),1)]),s("div",null,[t[1]||(t[1]=s("label",{class:"block text-sm font-medium text-gray-700"},"Email",-1)),s("div",B,l(e.user.email),1)])])]),t[3]||(t[3]=s("div",{class:"mt-8 flex justify-end"},[s("button",{class:"btn btn-primary"},"Editar Perfil")],-1))])])])])]),_:1})],64))}};export{F as default};
