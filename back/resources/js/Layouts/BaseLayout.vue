<template>
    <Cart>
        <div class="w-full bg-base-100 base-layout flex flex-col h-screen">
            <!-- Header -->
            <header class="bg-base-100 shadow-lg w-full z-50 h-[56px] flex items-center">
                <div class="px-4 w-full flex justify-between items-center">
                    <div class="flex items-center">
                        <Link href="/">
                            <img src="/icons/logo.png" alt="Psy+ Logo" class="h-10 w-auto" />
                        </Link>
                        <span class="ml-2 text-xl font-bold text-primary">Psy+</span>
                    </div>
                    <button @click="toggleDrawer" class="p-2 rounded-md focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </header>

            <!-- Main Content -->
            <main class="w-full overflow-y-auto flex-1">
                <slot></slot>
            </main>
        </div>
    </Cart>
</template>

<script setup>
import Cart from '@/components/Cart.vue';
import { useSchedule } from '@/store/schedule';
import { Link } from '@inertiajs/vue3';

const schedule = useSchedule();

const toggleDrawer = () => {
    schedule.toggleActive();
};
</script>
