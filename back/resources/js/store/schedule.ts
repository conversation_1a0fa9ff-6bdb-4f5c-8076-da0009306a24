import { api, getAllServices } from "@/server/api";
import type { Schedule, Professional, newOrder, Service } from "@/utils/models";
import { defineStore } from "pinia";

export const useSchedule = defineStore("schedule", {
    state: () => {
        return {
            active: false,
            cartDrawerActive: false,
            cart: [] as Schedule[],
            service: {} as Service,
            professional: {} as Professional,
            loading: false,
            orders: [] as newOrder[],
            variant_id: null,
        };
    },
    getters: {
        cartLength: (state) => state.cart.length,
    },
    actions: {
        toggleActive() {
            this.cartDrawerActive = !this.cartDrawerActive;
        },
        addToCart(item: Schedule) {
            this.cart.push(item);
        },
        addService(service: Service) {
            this.service = service;
        },
        addVariant(variant: number) {
            this.variant_id = variant;
        },
        addHour({ hour, date }: { hour: string; date: string }) {
            this.cart.push({
                service: this.service,
                professional: this.professional,
                hour,
                date,
                id: crypto.randomUUID(),
                variant_id: this.variant_id,
            });
            this.service = {} as Service;
            this.professional = {} as Professional;
            this.variant_id = null;
        },
        addProfessional(professional: Professional) {
            this.professional = professional;
        },
        async postCart(slug) {
            await api.post("/orders", {
                services: this.cart,
                company: slug,
            });
            this.cart = [];
        },
        async filterProductsByGame(url: string) {
            this.loading = true;
            try {
                const response = await getAllServices(url);
                return response;
            } catch (err) {
                throw err;
            } finally {
                this.loading = false;
            }
        },
    },
    persist: true,
});
