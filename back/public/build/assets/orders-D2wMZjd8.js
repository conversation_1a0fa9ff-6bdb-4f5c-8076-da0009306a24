import{b as D,_ as F}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{_ as T}from"./BaseButton-CxEL1ccK.js";import{a as A,g as N,_ as z}from"./BaseLayout--Fu-QUOh.js";import{l as B,r as v,o as l,c as u,a as t,t as d,u as o,s as C,F as j,e as O,n as V,d as p,b as y,w as g,i as H,g as M,y as Y,C as R,f as q,h as L,j as S,B as P}from"./app-BkKCG4YJ.js";import{_ as U}from"./Skeleton.vue_vue_type_script_setup_true_lang-BgQVuwFr.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const E={class:"elevated-card rounded-lg px-3 py-1 shadow-xl transition-shadow"},I={class:"flex items-center justify-between font-semibold text-lg border-b border-gray-400 pb-1"},G={class:"font-bold"},J={class:"mb-3 mt-2 divide-y space-y-2"},K={class:"flex gap-x-1 leading-tight mt-2"},Q={class:""},W={class:"flex justify-between mt-auto pb-2"},X={class:"flex"},Z={class:"text-lg font-semibold"},ee={key:0,class:"flex justify-between pb-1"},te={class:"flex justify-end items-center mt-5"},se=B({__name:"OrderCard",props:{order:{type:Object,required:!0},activeTab:{type:String,required:!0,default:"active"}},emits:["refresh"],setup(s,{emit:c}){const x=s,f=c,r=v(!1),n=v(!1);function w(m){const e=m.split(" ");return`${e[0]} ${e[e.length-1]}`}async function b(){n.value=!0;try{await A.post("/finish-my-order/"+x.order.id),r.value=!1,f("refresh")}catch(m){console.log(m)}finally{n.value=!1}}return(m,e)=>{var h,i;const _=T,k=D;return l(),u("div",E,[t("div",I,[t("div",null,"Pedido #"+d(s.order.id),1),t("div",G,d((i=o(C)((h=s.order.appointments[0])==null?void 0:h.start_date))==null?void 0:i.format("DD/MM/YYYY")),1)]),t("div",J,[(l(!0),u(j,null,O(s.order.appointments,(a,$)=>(l(),u("div",{key:$,class:""},[t("div",{class:V(["flex gap-x-1",$!==0?"mt-2":""])},[e[3]||(e[3]=t("span",null,"Serviço:",-1)),t("div",null,d(a.service.name),1),t("b",null,d(o(C)(a.start_date).format("HH:mm")),1),e[4]||(e[4]=p(" - ")),t("b",null,d(o(C)(a.end_date).format("HH:mm")),1)],2),t("div",K,[e[5]||(e[5]=p(" Profissional: ")),t("b",Q,[y(o(H),{href:`/${a.professional.slug}`},{default:g(()=>[p(d(w(a==null?void 0:a.professional.user.name)),1)]),_:2},1032,["href"])])])]))),128))]),t("div",W,[t("div",X,[e[6]||(e[6]=t("div",{class:"font-bold text-lg mr-1"},"Total:",-1)),t("div",Z,"R$"+d(s.order.total),1)])]),s.activeTab==="active"?(l(),u("div",ee,[y(_,{class:"btn-sm ml-auto",onClick:e[0]||(e[0]=a=>r.value=!0)},{default:g(()=>e[7]||(e[7]=[p("Finalizar")])),_:1})])):M("",!0),y(k,{loading:o(n),title:"Finalizar pedido",modelValue:o(r),"onUpdate:modelValue":e[2]||(e[2]=a=>Y(r)?r.value=a:null)},{default:g(()=>[e[10]||(e[10]=t("div",{class:"px-2 pt-2"}," Deseja realmente concluir seu pedido? ",-1)),t("div",te,[y(_,{loading:o(n),class:"btn-ghost font-bold uppercase",onClick:e[1]||(e[1]=a=>r.value=!1)},{default:g(()=>e[8]||(e[8]=[p("Cancelar")])),_:1},8,["loading"]),y(_,{loading:o(n),onClick:b},{default:g(()=>e[9]||(e[9]=[p("Confirmar")])),_:1},8,["loading"])])]),_:1},8,["loading","modelValue"])])}}}),ae="/orders",{getAll:oe,create:ne,deleteById:le}=N(ae),re=R("orders",{state:()=>({orders:[],loading:!1}),actions:{async addService(s){this.loading=!0;try{await ne(s)}catch(c){throw c}finally{this.loading=!1}},async deleteService(s){this.loading=!0;try{await le(s)}catch(c){throw c}finally{this.loading=!1}},async getAllServices(){this.loading=!0;try{return await oe()}catch(s){throw s}finally{this.loading=!1}}},persist:!0}),ie={class:"tabs tabs-bordered w-full grid grid-cols-2 font-bold"},de=["onClick"],ce={class:"grid px-2 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-2"},be=B({__name:"orders",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(s){const c=re(),x=v(["active","finished"]),f=v("active"),r=v([]),n=v();function w(e){return e==="finished"?"Finalizados":"Ativos"}async function b(){n.value=!0;try{const e=await c.getAllServices();r.value=e}catch{return}finally{n.value=!1}}const m=q(()=>r.value.filter(e=>f.value==="finished"?e.finished_at:!e.finished_at));return L(async()=>{await b()}),(e,_)=>{const k=U,h=se;return l(),S(P(s.slug?F:z),{slug:s.slug,company:s.company,professional:s.professional},{default:g(()=>[t("div",{class:V(["py-2 h-full flex flex-col md:p-6 md:pt-3 space-y-4 w-full",o(n)?"h-full overflow-hidden":""])},[t("div",ie,[(l(!0),u(j,null,O(o(x),(i,a)=>(l(),u("a",{key:a,class:V([o(f)===i?"tab-active !border-primary":"","tab text-lg transition-colors"]),onClick:$=>f.value=i},d(w(i)),11,de))),128))]),t("div",ce,[o(n)?(l(),S(k,{key:0,number:5,classes:"h-52 w-full"})):(l(!0),u(j,{key:1},O(o(m),(i,a)=>(l(),S(h,{key:a,order:i,"active-tab":o(f),onRefresh:b},null,8,["order","active-tab"]))),128))])],2)]),_:1},8,["slug","company","professional"])}}});export{be as default};
