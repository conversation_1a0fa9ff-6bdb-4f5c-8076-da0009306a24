<template>
  <div class="min-h-screen flex items-center justify-center bg-base-200">
    <div class="w-full max-w-md p-8 shadow-lg bg-base-100 rounded-lg">
      <div @click="router.back()" class="flex cursor-pointer mb-2">
        <ArrowLeftIcon class="w-5" />
        <div class="ml-2 text-lg">Voltar</div>
      </div>
      <h2 class="text-2xl font-bold text-center mb-6">Recuperar senha</h2>
      <form @submit.prevent="submit">
        <input-base
          class="mb-4"
          v-model="email"
          :error="errors['email']"
          label="Email"
        />
        <base-button :loading type="submit" class="btn btn-primary w-full"
          >Confirmar</base-button
        >
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeftIcon } from "@heroicons/vue/24/solid";
import * as yup from "yup";
import { api } from "~/server/api";
import { useForm } from "vee-validate";
import { useToast } from "vue-toast-notification";
const router = useRouter();
const validationSchema = yup.object().shape({
  email: yup.string().required("Campo obrigatório").email("Email inválido"),
});
const { handleSubmit, defineField, errors } = useForm({
  validationSchema,
});
const [email] = defineField("email");

const loading = ref(false);
const submitForgotPassword = async () => {
  loading.value = true;
  await api.post("/forgot-password", { email: email.value });
  loading.value = false;
  useToast().success("Email enviado");
};
const submit = handleSubmit(submitForgotPassword);
</script>
