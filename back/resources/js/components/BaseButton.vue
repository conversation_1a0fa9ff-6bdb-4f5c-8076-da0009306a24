<template>
    <button class="btn" :class="`btn-${color} `" :disabled="!!loading || disabled">
        <div class="flex items-center justify-center">
            <div v-if="!!loading" class="loading loading-spinner text-primary mr-2"></div>
            <slot />
        </div>
    </button>
</template>

<script setup type="ts">
const buttonSize = computed(() => {
    return `btn-${props.size}`
})
const props = defineProps({
    color: {
        type: String,
        default: 'primary',
    },
    size: {
        type: String,
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});
</script>
