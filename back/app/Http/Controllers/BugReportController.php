<?php

namespace App\Http\Controllers;

use App\Models\BugReport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class BugReportController extends Controller
{
    /**
     * Display a listing of bug reports
     */
    public function index(Request $request): JsonResponse
    {
        $query = BugReport::with(['reporter', 'assignee']);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by severity
        if ($request->has('severity')) {
            $query->where('severity', $request->severity);
        }

        // Filter by category
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Filter by assigned user
        if ($request->has('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        // Search in title and description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $bugReports = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json($bugReports);
    }

    /**
     * Store a newly created bug report
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'severity' => 'required|in:low,medium,high,critical',
            'category' => 'nullable|string|max:100',
            'browser' => 'nullable|string|max:100',
            'os' => 'nullable|string|max:100',
            'device_type' => 'nullable|string|max:50',
            'page_url' => 'nullable|url',
            'steps_to_reproduce' => 'nullable|array',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max per file
        ]);

        // Handle file uploads
        $attachmentPaths = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('bug-reports', 'public');
                $attachmentPaths[] = $path;
            }
        }

        $bugReport = BugReport::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'severity' => $validated['severity'],
            'category' => $validated['category'] ?? null,
            'reported_by' => Auth::id(),
            'browser' => $validated['browser'] ?? $request->userAgent(),
            'os' => $validated['os'] ?? null,
            'device_type' => $validated['device_type'] ?? null,
            'page_url' => $validated['page_url'] ?? $request->headers->get('referer'),
            'steps_to_reproduce' => $validated['steps_to_reproduce'] ?? [],
            'attachments' => $attachmentPaths,
            'metadata' => [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'submitted_at' => now(),
            ],
        ]);

        return response()->json([
            'message' => 'Bug report created successfully',
            'data' => $bugReport->load(['reporter'])
        ], 201);
    }

    /**
     * Display the specified bug report
     */
    public function show(BugReport $bugReport): JsonResponse
    {
        return response()->json($bugReport->load(['reporter', 'assignee']));
    }

    /**
     * Update the specified bug report
     */
    public function update(Request $request, BugReport $bugReport): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'severity' => 'sometimes|in:low,medium,high,critical',
            'status' => 'sometimes|in:open,in_progress,resolved,closed',
            'category' => 'sometimes|nullable|string|max:100',
            'assigned_to' => 'sometimes|nullable|exists:users,id',
            'steps_to_reproduce' => 'sometimes|nullable|array',
        ]);

        // If status is being changed to resolved, set resolved_at
        if (isset($validated['status']) && $validated['status'] === 'resolved' && $bugReport->status !== 'resolved') {
            $validated['resolved_at'] = now();
        }

        $bugReport->update($validated);

        return response()->json([
            'message' => 'Bug report updated successfully',
            'data' => $bugReport->load(['reporter', 'assignee'])
        ]);
    }

    /**
     * Remove the specified bug report
     */
    public function destroy(BugReport $bugReport): JsonResponse
    {
        // Delete associated files
        if ($bugReport->attachments) {
            foreach ($bugReport->attachments as $attachment) {
                Storage::disk('public')->delete($attachment);
            }
        }

        $bugReport->delete();

        return response()->json([
            'message' => 'Bug report deleted successfully'
        ]);
    }

    /**
     * Assign bug report to a user
     */
    public function assign(Request $request, BugReport $bugReport): JsonResponse
    {
        $validated = $request->validate([
            'assigned_to' => 'required|exists:users,id',
        ]);

        $bugReport->assignTo($validated['assigned_to']);

        return response()->json([
            'message' => 'Bug report assigned successfully',
            'data' => $bugReport->load(['reporter', 'assignee'])
        ]);
    }

    /**
     * Mark bug report as resolved
     */
    public function resolve(BugReport $bugReport): JsonResponse
    {
        $bugReport->markAsResolved(Auth::id());

        return response()->json([
            'message' => 'Bug report marked as resolved',
            'data' => $bugReport->load(['reporter', 'assignee'])
        ]);
    }

    /**
     * Get bug report statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total' => BugReport::count(),
            'by_status' => BugReport::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'by_severity' => BugReport::selectRaw('severity, count(*) as count')
                ->groupBy('severity')
                ->pluck('count', 'severity'),
            'by_category' => BugReport::selectRaw('category, count(*) as count')
                ->whereNotNull('category')
                ->groupBy('category')
                ->pluck('count', 'category'),
            'recent_count' => BugReport::where('created_at', '>=', now()->subDays(7))->count(),
            'resolved_this_week' => BugReport::where('resolved_at', '>=', now()->subDays(7))->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Get categories for dropdown
     */
    public function categories(): JsonResponse
    {
        $categories = BugReport::whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->sort()
            ->values();

        return response()->json($categories);
    }
}
