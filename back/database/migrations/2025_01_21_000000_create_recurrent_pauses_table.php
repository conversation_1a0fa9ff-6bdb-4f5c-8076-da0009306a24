<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recurrent_pauses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_professional_id')->constrained()->onDelete('cascade');
            $table->integer('day_of_week'); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
            $table->time('start_time');
            $table->time('end_time');
            $table->string('title')->default('Horário fixo');
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_from_appointment_id')->nullable()->constrained('appointments')->onDelete('set null');
            $table->timestamps();
            
            // Ensure no overlapping recurrent pauses for the same professional on the same day
            $table->unique(['company_professional_id', 'day_of_week', 'start_time'], 'unique_recurrent_pause');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recurrent_pauses');
    }
};
