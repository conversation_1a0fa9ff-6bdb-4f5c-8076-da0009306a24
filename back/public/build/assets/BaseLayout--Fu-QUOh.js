import{o as u,c as p,a as s,C as v,z as y,q as b,N as _,l as k,f as $,m as C,v as A,u as o,Q as h,b as i,w as f,i as g,F as B,j as m,t as M}from"./app-BkKCG4YJ.js";function L(e,r){return u(),p("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{d:"M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z"}),s("path",{d:"m12 5.432 8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z"})])}function S(e,r){return u(),p("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"fill-rule":"evenodd",d:"M10.5 3.75a6.75 6.75 0 1 0 0 13.5 6.75 6.75 0 0 0 0-13.5ZM2.25 10.5a8.25 8.25 0 1 1 14.59 5.28l4.69 4.69a.75.75 0 1 1-1.06 1.06l-4.69-4.69A8.25 8.25 0 0 1 2.25 10.5Z","clip-rule":"evenodd"})])}function Z(e,r){return u(),p("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"fill-rule":"evenodd",d:"M7.5 6v.75H5.513c-.96 0-1.764.724-1.865 1.679l-1.263 12A1.875 1.875 0 0 0 4.25 22.5h15.5a1.875 1.875 0 0 0 1.865-2.071l-1.263-12a1.875 1.875 0 0 0-1.865-1.679H16.5V6a4.5 4.5 0 1 0-9 0ZM12 3a3 3 0 0 0-3 3v.75h6V6a3 3 0 0 0-3-3Zm-3 8.25a3 3 0 1 0 6 0v-.75a.75.75 0 0 1 1.5 0v.75a4.5 4.5 0 1 1-9 0v-.75a.75.75 0 0 1 1.5 0v.75Z","clip-rule":"evenodd"})])}function D(e,r){return u(),p("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"fill-rule":"evenodd",d:"M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z","clip-rule":"evenodd"})])}function P(e,r){return u(),p("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"fill-rule":"evenodd",d:"M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])}function V(e,r){return u(),p("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"})])}const w=v("users",{state:()=>({token:"",form_completed:!1}),getters:{isLoggedIn:e=>!!e.token.length},actions:{login(e){this.token=e},logout(e){this.token="",this.form_completed=!1}},persist:!0}),j=v("company",{state:()=>({background_color:"",buttons_color:"",cards_color:"",logo:null,name:"",plan_id:null,slug:"",sunday_time:null,monday_time:"",tuesday_time:"",wednesday_time:"",thursday_time:"",friday_time:"",saturday_time:null,solo_professional:!1}),persist:!0}),c=y.create({}),I=["Cannot read properties of undefined (reading 'id')","You must be logged in to access this page."];c.interceptors.request.use(e=>{e.baseURL="/api/";const l=w().token;return l&&(e.headers.Authorization=`Bearer ${l}`),e},e=>Promise.reject(e));c.interceptors.response.use(e=>e,e=>{if(e.response){const r=b.useToast(),l=j(),{status:d,data:a}=e.response;if(console.log(d),d===403||d===401||d===500&&I[1].includes(a.message)){if(w().token="",(a==null?void 0:a.error)==="Invalid credentials")return;_.get(`/${l.slug}/login`,{redirect:route().current()}),r.error("Sessão expirada",{position:"top"})}}return Promise.reject(e)});async function H(e){return c.get(e)}function ee(e){return c.get(`/${e}/professionals`)}function te(e){async function r(t){try{return(await c.post(e,t)).data}catch(n){throw n}}async function l(t){try{return(await c.delete(`${e}/${t}`)).data}catch(n){throw n}}async function d(t){try{return(await c.put(`${e}/${t}`)).data}catch(n){throw n}}async function a(){try{return(await c.get(e)).data}catch(t){throw t}}return{create:r,getAll:a,editById:d,deleteById:l,api:c}}const x=v("schedule",{state:()=>({active:!1,cartDrawerActive:!1,cart:[],service:{},professional:{},loading:!1,orders:[],variant_id:null}),getters:{cartLength:e=>e.cart.length},actions:{toggleActive(){this.cartDrawerActive=!this.cartDrawerActive},addToCart(e){this.cart.push(e)},addService(e){this.service=e},addVariant(e){this.variant_id=e},addHour({hour:e,date:r}){this.cart.push({service:this.service,professional:this.professional,hour:e,date:r,id:crypto.randomUUID(),variant_id:this.variant_id}),this.service={},this.professional={},this.variant_id=null},addProfessional(e){this.professional=e},async postCart(e){await c.post("/orders",{services:this.cart,company:e}),this.cart=[]},async filterProductsByGame(e){this.loading=!0;try{return await H(e)}catch(r){throw r}finally{this.loading=!1}}},persist:!0}),U={class:"drawer drawer-end h-screen w-screen"},z={class:"drawer-content bg-base-100 h-screen w-full overflow-auto"},N={class:"drawer-side shadow-xl z-50"},E={class:"w-80 flex flex-col h-full bg-base-100 text-base-content overflow-auto"},F={class:"flex flex-row justify-between items-center p-4 text-primary border-b"},T={class:"p-4"},q={class:"space-y-2"},Y={class:"mt-auto p-4"},G={class:"text-sm text-gray-500 text-center"},Q=k({__name:"Cart",setup(e){const r=x(),l=w(),d=$(()=>{var a;return(a=l==null?void 0:l.token)==null?void 0:a.length});return(a,t)=>(u(),p("div",U,[C(s("input",{id:"my-drawer-2",type:"checkbox",class:"drawer-toggle","onUpdate:modelValue":t[0]||(t[0]=n=>o(r).cartDrawerActive=n)},null,512),[[A,o(r).cartDrawerActive]]),s("div",z,[h(a.$slots,"default")]),s("div",N,[t[15]||(t[15]=s("label",{for:"my-drawer-2",class:"drawer-overlay"},null,-1)),s("div",E,[s("div",F,[t[7]||(t[7]=s("div",{class:"flex items-center"},[s("img",{src:"/icons/logo.png",alt:"Psy+ Logo",class:"h-8 w-auto"}),s("span",{class:"ml-2 text-xl font-bold"},"Psy+")],-1)),i(o(P),{class:"w-6 h-6 cursor-pointer",onClick:t[1]||(t[1]=n=>o(r).toggleActive())})]),s("div",T,[s("nav",q,[i(o(g),{onClick:t[2]||(t[2]=n=>o(r).toggleActive()),href:"/",class:"flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors"},{default:f(()=>[i(o(L),{class:"w-5 h-5 mr-3 text-primary"}),t[8]||(t[8]=s("span",{class:"text-base font-medium"},"Início",-1))]),_:1}),i(o(g),{onClick:t[3]||(t[3]=n=>o(r).toggleActive()),href:"/profissionais",class:"flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors"},{default:f(()=>[i(o(S),{class:"w-5 h-5 mr-3 text-primary"}),t[9]||(t[9]=s("span",{class:"text-base font-medium"},"Buscar profissionais",-1))]),_:1}),d.value?(u(),p(B,{key:0},[i(o(g),{onClick:t[4]||(t[4]=n=>o(r).toggleActive()),href:"/pedidos",class:"flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors"},{default:f(()=>[i(o(Z),{class:"w-5 h-5 mr-3 text-primary"}),t[10]||(t[10]=s("span",{class:"text-base font-medium"},"Agendamentos",-1))]),_:1}),i(o(g),{onClick:t[5]||(t[5]=n=>o(r).toggleActive()),href:"/perfil",class:"flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors"},{default:f(()=>[i(o(D),{class:"w-5 h-5 mr-3 text-primary"}),t[11]||(t[11]=s("span",{class:"text-base font-medium"},"Meus dados",-1))]),_:1})],64)):(u(),m(o(g),{key:1,onClick:t[6]||(t[6]=n=>o(r).toggleActive()),href:"/login",class:"flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors"},{default:f(()=>[i(o(V),{class:"w-5 h-5 mr-3 text-primary"}),t[12]||(t[12]=s("span",{class:"text-base font-medium"},"Entrar",-1))]),_:1}))])]),t[14]||(t[14]=s("div",{class:"border-t border-base-300 my-4"},null,-1)),s("div",Y,[s("div",G,[s("p",null,"© "+M(new Date().getFullYear())+" Psy+",1),t[13]||(t[13]=s("p",null,"Todos os direitos reservados",-1))])])])])]))}}),R={class:"w-full bg-base-100 base-layout flex flex-col h-screen"},J={class:"bg-base-100 shadow-lg w-full z-50 h-[56px] flex items-center"},K={class:"px-4 w-full flex justify-between items-center"},O={class:"flex items-center"},W={class:"w-full overflow-y-auto flex-1"},se={__name:"BaseLayout",setup(e){const r=x(),l=()=>{r.toggleActive()};return(d,a)=>(u(),m(Q,null,{default:f(()=>[s("div",R,[s("header",J,[s("div",K,[s("div",O,[i(o(g),{href:"/"},{default:f(()=>a[0]||(a[0]=[s("img",{src:"/icons/logo.png",alt:"Psy+ Logo",class:"h-10 w-auto"},null,-1)])),_:1}),a[1]||(a[1]=s("span",{class:"ml-2 text-xl font-bold text-primary"},"Psy+",-1))]),s("button",{onClick:l,class:"p-2 rounded-md focus:outline-none"},a[2]||(a[2]=[s("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])]),s("main",W,[h(d.$slots,"default")])])]),_:3}))}};export{se as _,c as a,j as b,w as c,ee as d,L as e,D as f,te as g,S as r,x as u};
