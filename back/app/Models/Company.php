<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    protected $hidden = [
        'id',
    ];
    protected $fillable = [
        'name',
        'plan_id',
        'sunday_time',
        'monday_time',
        'tuesday_time',
        'wednesday_time',
        'thursday_time',
        'friday_time',
        'saturday_time',
        'sunday_pause',
        'monday_pause',
        'tuesday_pause',
        'wednesday_pause',
        'thursday_pause',
        'friday_pause',
        'saturday_pause',
        'buttons_color',
        'background_color',
        'cards_color',
        'slug',
        'tryal_phase',
        'solo_professional',
        'logo',
        'banner',
        'whatsapp',
        'description',
        'instagram',
        'solo_professional',
    ];
}
