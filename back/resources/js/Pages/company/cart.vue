<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div class="flex flex-col h-full justify-center gap-1 p-2 pt-3 rounded">
            <div v-if="drawer.cart.length === 0" class="flex justify-center items-center h-full">
                <div class="flex flex-col items-center">
                    <ExclamationCircleIcon class="w-12 text-accent-content" />
                    <p class="mt-4 text-lg font-bold text-accent-content">Você não tem agendamentos</p>
                    <Link :href="`/${slug}/servicos`" class="mt-0 text-lg font-bold text-primary">
                    Agende um horário
                    </Link>
                </div>
            </div>
            <template v-else>

                <div v-for="(item, i) in drawer.cart" :key="i">
                    <cart-card :item="item" class="shadow-none" cart @delete="deleteItem" />
                </div>
                <!-- <div class="elevated-card divide-y overflow-y-auto p-1">
                    <cart-card
          v-for="(item, i) in drawer.cart"
          :item="item"
          class="mx-1"
          :key="i"
          @delete="deleteItem"
        />
                </div> -->

                <div class="elevated-card flex-row justify-between mt-auto">
                    <div class="font-bold text-lg my-auto md:text-xl">
                        Total: {{ formatPrice(totalPrice) }}
                    </div>
                    <base-button :loading="loading" type="submit" :disabled="drawer.cart.length === 0" @click="
                        onSubmit
                    ">
                        Agendar
                    </base-button>
                </div>
            </template>
        </div>
    </AppLayout>
</template>
<script setup lang="ts">
import { useSchedule } from "@/store/schedule";
import { ExclamationCircleIcon } from "@heroicons/vue/24/outline";
import { useCompanyStore } from "@/store/company";
import { router } from "@inertiajs/vue3";
import { useToast } from "vue-toast-notification";
import { useUser } from "@/store/user";
import { Link } from "@inertiajs/vue3";
import formatPrice from "@/utils/formatPrice";
import AppLayout from "@/Layouts/AppLayout.vue";
const drawer = useSchedule();
const companyStore = useCompanyStore();
const totalPrice = computed(() => {
    return drawer.cart.reduce((total, el) => {
        return total + parseFloat(el.service.price);
    }, 0);
});
const { slug, company } = defineProps({
    slug: {
        type: String,
    },
    company: {
        type: Object,
    },
    professional: {
        type: Object,
    },
});
const deleteItem = (id) => {
    drawer.cart = drawer.cart.filter((el) => el.id !== id);
};
const user = useUser();
const loading = ref()
const toast = useToast();

async function onSubmit() {
    loading.value = true
    try {
        if (!user.isLoggedIn) {
            toast.error("Você precisa estar logado para fazer um pedido!", { position: "top" });
            router.get(`/${slug}/login?redirect=checkout`);
            drawer.cartDrawerActive = false;
            return;
        }
        await drawer.postCart(slug);
        toast.success("Pedido criado com sucesso!!!", { position: "top" });
        router.get(`/${slug}/pedidos?checkForm=true`);
    } catch (err) {
        return;
    }
    finally {
        loading.value = false
    }
}
</script>
