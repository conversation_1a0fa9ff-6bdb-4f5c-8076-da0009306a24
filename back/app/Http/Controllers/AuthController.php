<?php

namespace App\Http\Controllers;

use App\Helpers\EncryptionHelper;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    public function loginUser(Request $request)
    {
        $loginType = $request->only(['isPhone']);
        $identifier = $loginType['isPhone'] ? 'phone' : 'email';
        $credentials = $request->only('identifier', 'password');

        if (auth()->guard('web')->attempt([$identifier => $credentials['identifier'], 'password' => $credentials['password']])) {
            $user = auth()->guard('web')->user();
            $token = $user->createToken('token')->plainTextToken;

            return response()->json([
                'token' => $token,
                'expires_in' => 30 * 24 * 60 * 60, // 30 days
                'user' => $user,
            ]);
        }

        return response()->json(['error' => 'Invalid credentials'], 401);
    }

    public function loginAdmin(Request $request)
    {
        $credentials = $request->only('email', 'password');

        if (auth()->guard('web')->attempt($credentials)) {
            $admin = auth()->guard('web')->user();

            // Check if user has admin privileges
            if ($admin->type !== 'admin' && $admin->type !== 'super_admin') {
                return response()->json(['error' => "Invalid credentials"], 401);
            }

            $token = $admin->createToken('admin_token', ['admin'])->plainTextToken;
            $image = strpos($admin->profile_photo_path, 'http') === 0 ? $admin->profile_photo_path : $admin->profile_photo_url;

            return response()->json([
                'image' => $image,
                'name' => $admin->name,
                'email' => $admin->email,
                'phone' => $admin->phone,
                'token' => $token,
            ]);
        }

        return response()->json(['error' => 'Invalid credentials'], 401);
    }

    public function registerUser(Request $request)
    {
        $request->validate([
            'email' => 'string',
            'name' => 'required|string',
            'password' => 'required|min:6|confirmed',
        ]);
        $user = User::where('phone', $request->phone)->first();
	if(!$user) $user = User::where('email', $request->email)->first();
        if ($user) {
            if (!$user->finished_register) {
                $user->finished_register = true;
                $user->email = $request->email;
		$user->phone = $request->phone;
                $user->password = bcrypt($request->password);
                $user->save();
            } else {
                return response()->json(['message' => 'Número já cadastrado'], 400);
            }
        } else {
            $user = User::create([
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'name' => $request->name,
                'phone' => $request->phone,
                'type' => 'user'
            ]);
        }
        return response()->json(['user' => $user], 201);
    }

    // public function registerAdmin(Request $request)
    // {
    //     $request->validate([
    //         'email' => 'required|email|unique',
    //         'phone' => 'required|string|unique',
    //         'password' => 'required|min:6',
    //         'name' => 'required|string',
    //     ]);

    //     $admin = Admin::create([
    //         'id' => Str::uuid(),
    //         'email' => $request->email,
    //         'password' => Hash::make($request->password),
    //         'name' => $request->name,
    //         'is_worker' => true,
    //     ]);

    //     return response()->json(['admin' => $admin], 201);
    // }

    public function getUsers()
    {
        return response()->json(User::all());
    }

    // public function getProfessionals()
    // {
    //     $professionals = Admin::where('is_worker', true)->get();
    //     return response()->json($professionals);
    // }

    // public function createProfessional(Request $request)
    // {
    //     $request->validate([
    //         'email' => 'required|email|unique',
    //         'phone' => 'required|string|unique',
    //         'password' => 'required|min:6',
    //         'name' => 'required|string',
    //     ]);

    //     $admin = Admin::create([
    //         'id' => Str::uuid(),
    //         'email' => $request->email,
    //         'password' => Hash::make($request->password),
    //         'name' => $request->name,
    //         'is_worker' => true,
    //     ]);

    //     return response()->json(['admin' => $admin], 201);
    // }

    // public function getAdmins()
    // {
    //     return response()->json(Admin::all());
    // }

    public function updateUser(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:users,id',
            'email' => 'required|email|unique:users,email,' . $request->id,
            'name' => 'required|string',
            'password' => 'nullable|min:6',
        ]);

        $user = User::findOrFail($request->id);
        $user->email = $request->email;
        $user->name = $request->name;

        if ($request->password) {
            $user->password = Hash::make($request->password);
        }

        $user->save();

        return response()->json($user);
    }

    // public function updateAdmin(Request $request)
    // {
    //     $request->validate([
    //         'id' => 'required|exists:admins,id',
    //         'email' => 'required|email|unique:admins,email,' . $request->id,
    //         'name' => 'required|string',
    //         'password' => 'nullable|min:6',
    //     ]);

    //     $admin = Admin::findOrFail($request->id);
    //     $admin->email = $request->email;
    //     $admin->name = $request->name;

    //     if ($request->password) {
    //         $admin->password = Hash::make($request->password);
    //     }

    //     $admin->save();

    //     return response()->json($admin);
    // }
    public function checkUser(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
        ]);
        $user = User::where('phone', $request->phone)->first();
        if ($user) {
            return response()->json(['user' => ['exist' => true, 'hasEmail' => $user->email ? true : false]]);
        }
        return response()->json(['user' => null]);
    }

    /**
     * Decrypt user data from the URL
     */
    public function decryptUserData(Request $request)
    {
        Log::info('Decrypt request received with data: ' . substr($request->data, 0, 50) . '...');

        $request->validate([
            'data' => 'required|string',
        ]);

        try {
            $userData = EncryptionHelper::decryptUserData($request->data);
            Log::info('Decrypted user data: ' . json_encode($userData));

            if (!$userData) {
                Log::error('Decryption returned null');
                return response()->json(['error' => 'Invalid or expired data'], 400);
            }

            return response()->json($userData);
        } catch (\Exception $e) {
            Log::error('Exception during decryption: ' . $e->getMessage());
            return response()->json(['error' => 'Error during decryption: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Complete user registration from the finish-register page
     */
    public function completeRegistration(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string',
                'email' => 'required|email',
                'phone' => 'required|string',
                'password' => 'required|min:6',
                'password_confirmation' => 'required|same:password',
            ]);

            // Find the user by email or phone
            $user = User::where('email', $request->email)
                ->orWhere('phone', $request->phone)
                ->first();

            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Update user information
            $user->name = $request->name;
            $user->email = $request->email;
            $user->phone = $request->phone;
            $user->password = Hash::make($request->password);
            $user->finished_register = 1;
            $user->save();

            return response()->json(['message' => 'Registration completed successfully'], 200);
        } catch (ValidationException $e) {
            return response()->json(['errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to complete registration: ' . $e->getMessage()], 500);
        }
    }
}
