export function getContrastColor(hex: string): string {
  // Remove o #, se existir
  hex = hex.replace("#", "");

  // Converte os componentes RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Calcula o luminance relativo (padrão W3C)
  const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;

  // Retorna a cor de maior contraste
  return luminance > 186 ? "#000" : "#FFF";
}
