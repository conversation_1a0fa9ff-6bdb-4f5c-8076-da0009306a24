<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Analytics tracking table
        Schema::create('analytics', function (Blueprint $table) {
            $table->id();
            $table->string('event_type'); // page_view, login, appointment_created, etc.
            $table->string('event_category'); // user, professional, system
            $table->string('event_action');
            $table->string('event_label')->nullable();
            $table->integer('event_value')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('set null');
            $table->string('session_id')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('page_url')->nullable();
            $table->string('referrer')->nullable();
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamps();
            
            $table->index(['event_type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['company_id', 'created_at']);
        });

        // Bug reports table
        Schema::create('bug_reports', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->string('severity')->default('medium'); // low, medium, high, critical
            $table->string('status')->default('open'); // open, in_progress, resolved, closed
            $table->string('category')->nullable(); // frontend, backend, mobile, etc.
            $table->foreignId('reported_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->string('browser')->nullable();
            $table->string('os')->nullable();
            $table->string('device_type')->nullable();
            $table->string('page_url')->nullable();
            $table->json('steps_to_reproduce')->nullable();
            $table->json('attachments')->nullable(); // File paths
            $table->json('metadata')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'severity']);
            $table->index(['reported_by', 'created_at']);
        });

        // Access logs table for detailed tracking
        Schema::create('access_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('user_type'); // user, admin, super_admin
            $table->string('action'); // login, logout, page_view, api_call
            $table->string('resource')->nullable(); // specific page or endpoint
            $table->string('method')->nullable(); // GET, POST, PUT, DELETE
            $table->string('ip_address');
            $table->string('user_agent')->nullable();
            $table->string('session_id')->nullable();
            $table->integer('response_time')->nullable(); // in milliseconds
            $table->integer('status_code')->nullable();
            $table->json('request_data')->nullable();
            $table->json('response_data')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'created_at']);
            $table->index(['user_type', 'action', 'created_at']);
            $table->index(['created_at']);
        });

        // System metrics table
        Schema::create('system_metrics', function (Blueprint $table) {
            $table->id();
            $table->string('metric_name'); // daily_active_users, revenue, appointments_count, etc.
            $table->string('metric_type'); // count, sum, average, percentage
            $table->decimal('value', 15, 2);
            $table->date('date');
            $table->string('period')->default('daily'); // daily, weekly, monthly, yearly
            $table->json('breakdown')->nullable(); // Additional breakdown data
            $table->timestamps();
            
            $table->unique(['metric_name', 'date', 'period']);
            $table->index(['metric_name', 'date']);
        });

        // Subscription tracking table
        Schema::create('subscription_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('plan_id')->constrained()->onDelete('cascade');
            $table->string('event_type'); // subscription_created, upgraded, downgraded, cancelled, renewed
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('BRL');
            $table->string('payment_method')->nullable();
            $table->string('payment_status'); // pending, completed, failed, refunded
            $table->date('billing_period_start');
            $table->date('billing_period_end');
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['company_id', 'event_type', 'created_at']);
            $table->index(['plan_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_analytics');
        Schema::dropIfExists('system_metrics');
        Schema::dropIfExists('access_logs');
        Schema::dropIfExists('bug_reports');
        Schema::dropIfExists('analytics');
    }
};
