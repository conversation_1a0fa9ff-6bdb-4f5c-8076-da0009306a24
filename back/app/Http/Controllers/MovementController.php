<?php

namespace App\Http\Controllers;

use App\Models\Movement;
use App\Models\Order;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MovementController extends Controller
{
    public function index(Request $request)
    {
        $companyID = $request->get('company_id');
        $filterBy = $request->query('filter_by', 'name');
        $filterValue = $request->query('value', '');
        $sortBy = $request->query('sort_by', 'created_at');
        $sortDirection = $request->query('direction', 'asc');
        $perPage = $request->query('per_page', 10);
        $category = $request->query('category');
        $period = $request->query('period');

        $query = Movement::where('company_id', $companyID);

        if (!empty($category)) {
            $query->where('category', $category);
        }

        if (!empty($filterValue)) {
            $query->where($filterBy, 'LIKE', "%{$filterValue}%");
        }

        // Add period filtering
        if (!empty($period)) {
            $startDate = $this->getStartDateForPeriod($period);
            if ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }
        }

        if (in_array($sortBy, ['quantity', 'created_at', 'name', 'total'])) {
            $query->orderBy($sortBy, $sortDirection);
        }

        $result = $query->paginate($perPage);

        return response()->json($result, 200);
    }

    private function getStartDateForPeriod($period)
    {
        return match($period) {
            'today' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            '3months' => now()->subMonths(3)->startOfMonth(),
            '6months' => now()->subMonths(6)->startOfMonth(),
            default => null
        };
    }

    public function getAllMovements(Request $request)
    {
        $companyID = $request->get('company_id');
        $movements = Movement::where('company_id', $companyID)->get();
        return response()->json($movements, 200);
    }

    /**
     * Store bill movement
     */
    public function storeBillMovement(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'type' => 'required|string|in:profit,loss',
            'notes' => 'nullable|string',
            'total' => 'required|numeric|min:0',
        ]);

        $data = array_merge($validated, [
            'company_id' => $request->get('company_id'),
            'category' => 'bill',
            'total_spent' => $validated['type'] === 'loss' ? $validated['total'] : null,
            'total_earned' => $validated['type'] === 'profit' ? $validated['total'] : null,
            'quantity' => 1,
        ]);

        $movement = Movement::create($data);

        return response()->json($movement, 201);
    }

    /**
     * Store service movement
     */
    public function storeServiceMovement(Request $request)
    {
        $validated = $request->validate([
            'service_id' => 'required|numeric',
            'type' => 'required|string|in:profit,loss',
            'notes' => 'nullable|string',
            'total' => 'required|numeric|min:0',
        ]);

        $service = Service::findOrFail($validated['service_id']);

        $data = array_merge($validated, [
            'company_id' => $request->get('company_id'),
            'name' => $service->name,
            'category' => 'service',
            'total_spent' => $validated['type'] === 'loss' ? $validated['total'] : null,
            'total_earned' => $validated['type'] === 'profit' ? $validated['total'] : null,
            'quantity' => 1,
        ]);

        $movement = Movement::create($data);

        return response()->json($movement, 201);
    }

    public function update(Request $request, $id)
    {
        $movement = Movement::findOrFail($id);

        $validated = $request->validate([
            'name' => 'sometimes|string',
            'type' => 'sometimes|string|in:profit,loss',
            'notes' => 'sometimes|string',
            'total' => 'sometimes|numeric|min:0',
        ]);

        $movement->update($validated);
        return response()->json($movement);
    }

    public function destroy($id)
    {
        $movement = Movement::findOrFail($id);
        $movement->delete();

        return response()->json(['message' => 'Movement successfully deleted']);
    }
}
