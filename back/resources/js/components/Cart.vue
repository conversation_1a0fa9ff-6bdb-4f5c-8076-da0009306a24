<template>
    <div class="drawer drawer-end h-screen w-screen">
        <input id="my-drawer-2" type="checkbox" class="drawer-toggle" v-model="drawer.cartDrawerActive" />
        <div class="drawer-content bg-base-100 h-screen w-full overflow-auto">
            <!-- Page content here -->
            <slot></slot>
        </div>
        <div class="drawer-side shadow-xl z-50">
            <label for="my-drawer-2" class="drawer-overlay"></label>

            <div class="w-80 flex flex-col h-full bg-base-100 text-base-content overflow-auto">
                <div class="flex flex-row justify-between items-center p-4  text-primary border-b">
                    <div class="flex items-center">
                        <img src="/icons/logo.png" alt="Psy+ Logo" class="h-8 w-auto" />
                        <span class="ml-2 text-xl font-bold">Psy+</span>
                    </div>
                    <XMarkIcon class="w-6 h-6 cursor-pointer" @click="drawer.toggleActive()" />
                </div>

                <!-- Navigation Menu -->
                <div class="p-4">
                    <nav class="space-y-2">
                        <!-- Always visible menu items -->
                        <Link @click="drawer.toggleActive()" href="/" class="flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors">
                        <HomeIcon class="w-5 h-5 mr-3 text-primary" />
                        <span class="text-base font-medium">Início</span>
                        </Link>

                        <Link @click="drawer.toggleActive()" href="/profissionais"
                            class="flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors">
                        <MagnifyingGlassIcon class="w-5 h-5 mr-3 text-primary" />
                        <span class="text-base font-medium">Buscar profissionais</span>
                        </Link>

                        <!-- Conditional menu items based on login status -->
                        <template v-if="isLoggedIn">
                            <Link @click="drawer.toggleActive()" href="/pedidos"
                                class="flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors">
                            <ShoppingBagIcon class="w-5 h-5 mr-3 text-primary" />
                            <span class="text-base font-medium">Agendamentos</span>
                            </Link>

                            <Link @click="drawer.toggleActive()" href="/perfil"
                                class="flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors">
                            <UserIcon class="w-5 h-5 mr-3 text-primary" />
                            <span class="text-base font-medium">Meus dados</span>
                            </Link>
                        </template>

                        <template v-else>
                            <Link @click="drawer.toggleActive()" href="/login"
                                class="flex items-center p-3 rounded-lg hover:bg-base-200 transition-colors">
                            <LockClosedIcon class="w-5 h-5 mr-3 text-primary" />
                            <span class="text-base font-medium">Entrar</span>
                            </Link>
                        </template>
                    </nav>
                </div>

                <div class="border-t border-base-300 my-4"></div>

                <!-- Footer -->
                <div class="mt-auto p-4">
                    <div class="text-sm text-gray-500 text-center">
                        <p>&copy; {{ new Date().getFullYear() }} Psy+</p>
                        <p>Todos os direitos reservados</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- <Loading :show="drawer.loading" /> -->
    </div>
</template>

<script setup lang="ts">
import {
    XMarkIcon,
    HomeIcon,
    MagnifyingGlassIcon,
    ShoppingBagIcon,
    UserIcon
} from "@heroicons/vue/24/solid";
import { LockClosedIcon } from "@heroicons/vue/24/outline";
import { useSchedule } from "@/store/schedule";
import { Link } from "@inertiajs/vue3";
import { computed } from 'vue';
import { useUser } from "@/store/user";
const drawer = useSchedule();
const user = useUser();
const isLoggedIn = computed(() => {
    return user?.token?.length
});
</script>
