<template>
  <div class="space-y-6">
    <!-- Revenue Over Time Chart -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Receita ao Longo do Tempo</h3>
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>
      <div v-else-if="revenueAnalytics.revenue_over_time?.length" class="h-64">
        <canvas ref="revenueChart"></canvas>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- Revenue Stats Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Revenue by Plan -->
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4"><PERSON><PERSON>ita por Plano</h3>
        <div v-if="revenueAnalytics.revenue_by_plan?.length" class="space-y-2">
          <div 
            v-for="plan in revenueAnalytics.revenue_by_plan" 
            :key="plan.name"
            class="flex justify-between items-center p-2 bg-base-200 rounded"
          >
            <span>{{ plan.name }}</span>
            <span class="font-semibold">{{ toBrl(plan.revenue) }}</span>
          </div>
        </div>
        <div v-else class="text-center py-4 text-gray-500">
          Nenhum dado disponível
        </div>
      </div>

      <!-- Average Order Value -->
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4">Valor Médio do Pedido</h3>
        <div class="text-center">
          <p class="text-3xl font-bold text-primary">
            {{ toBrl(revenueAnalytics.average_order_value || 0) }}
          </p>
          <p class="text-sm text-gray-600">Valor Médio</p>
        </div>
      </div>
    </div>

    <!-- Subscription Analytics -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Analytics de Assinaturas</h3>
      <div v-if="revenueAnalytics.subscription_analytics" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-green-600">
            {{ revenueAnalytics.subscription_analytics.new_subscriptions || 0 }}
          </p>
          <p class="text-sm text-gray-600">Novas Assinaturas</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-red-600">
            {{ revenueAnalytics.subscription_analytics.cancelled_subscriptions || 0 }}
          </p>
          <p class="text-sm text-gray-600">Cancelamentos</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-blue-600">
            {{ revenueAnalytics.subscription_analytics.upgrades || 0 }}
          </p>
          <p class="text-sm text-gray-600">Upgrades</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ toBrl(revenueAnalytics.subscription_analytics.total_revenue || 0) }}
          </p>
          <p class="text-sm text-gray-600">Receita Total</p>
        </div>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

const props = defineProps({
  period: {
    type: String,
    default: 'week'
  }
});

const toast = useToast();
const loading = ref(false);
const revenueAnalytics = ref({});
const revenueChart = ref(null);
let revenueChartInstance = null;

async function loadRevenueAnalytics() {
  try {
    loading.value = true;
    const { data } = await api.get('/super-admin/analytics/revenue', {
      params: { period: props.period }
    });
    revenueAnalytics.value = data;
    
    nextTick(() => {
      createChart();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar analytics de receita');
  } finally {
    loading.value = false;
  }
}

function createChart() {
  if (revenueChartInstance) {
    revenueChartInstance.destroy();
  }

  if (!revenueChart.value || !revenueAnalytics.value.revenue_over_time?.length) return;

  const ctx = revenueChart.value.getContext('2d');
  const data = revenueAnalytics.value.revenue_over_time;

  revenueChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.map(item => item.period),
      datasets: [{
        label: 'Receita (R$)',
        data: data.map(item => parseFloat(item.revenue)),
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return 'R$ ' + value.toLocaleString('pt-BR');
            }
          }
        }
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: function(context) {
              return 'Receita: R$ ' + context.parsed.y.toLocaleString('pt-BR');
            }
          }
        }
      }
    }
  });
}

watch(() => props.period, () => {
  loadRevenueAnalytics();
});

onMounted(() => {
  loadRevenueAnalytics();
});

onUnmounted(() => {
  if (revenueChartInstance) {
    revenueChartInstance.destroy();
  }
});
</script>
