<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class RecurrentPause extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_professional_id',
        'day_of_week',
        'start_time',
        'end_time',
        'title',
        'is_active',
        'created_from_appointment_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function professional()
    {
        return $this->belongsTo(CompanyProfessional::class, 'company_professional_id');
    }

    public function createdFromAppointment()
    {
        return $this->belongsTo(Appointment::class, 'created_from_appointment_id');
    }

    /**
     * Check if this recurrent pause conflicts with a given time slot on a specific date
     */
    public function conflictsWith(Carbon $startTime, Carbon $endTime): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // Check if the day of week matches
        if ($startTime->dayOfWeek !== $this->day_of_week) {
            return false;
        }

        // Convert recurrent pause times to the same date for comparison
        $pauseStart = Carbon::parse($startTime->format('Y-m-d') . ' ' . $this->start_time);
        $pauseEnd = Carbon::parse($startTime->format('Y-m-d') . ' ' . $this->end_time);

        // Check for overlap: slot starts before pause ends AND slot ends after pause starts
        return $startTime->lt($pauseEnd) && $endTime->gt($pauseStart);
    }

    /**
     * Get all active recurrent pauses for a specific professional and day
     */
    public static function getForProfessionalAndDay(int $professionalId, int $dayOfWeek)
    {
        return static::where('company_professional_id', $professionalId)
            ->where('day_of_week', $dayOfWeek)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Create a recurrent pause from an appointment
     */
    public static function createFromAppointment(Appointment $appointment): self
    {
        $startTime = Carbon::parse($appointment->start_date);
        $endTime = Carbon::parse($appointment->end_date);

        return static::create([
            'company_professional_id' => $appointment->company_professional_id,
            'day_of_week' => $startTime->dayOfWeek,
            'start_time' => $startTime->format('H:i'),
            'end_time' => $endTime->format('H:i'),
            'title' => $appointment->title ?: 'Horário fixo',
            'is_active' => true,
            'created_from_appointment_id' => $appointment->id,
        ]);
    }
}
