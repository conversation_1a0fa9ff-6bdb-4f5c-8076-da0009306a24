<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SystemMetric extends Model
{
    use HasFactory;

    protected $fillable = [
        'metric_name',
        'metric_type',
        'value',
        'date',
        'period',
        'breakdown',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'date' => 'date',
        'breakdown' => 'array',
    ];

    // Scopes
    public function scopeByMetric($query, $metricName)
    {
        return $query->where('metric_name', $metricName);
    }

    public function scopeByPeriod($query, $period)
    {
        return $query->where('period', $period);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    // Static methods for common metrics
    public static function recordDailyActiveUsers($date = null, $count = null)
    {
        $date = $date ?? Carbon::today();
        $count = $count ?? User::whereDate('updated_at', $date)->count();
        
        return self::updateOrCreate(
            [
                'metric_name' => 'daily_active_users',
                'date' => $date,
                'period' => 'daily'
            ],
            [
                'metric_type' => 'count',
                'value' => $count
            ]
        );
    }

    public static function recordDailyRevenue($date = null, $amount = null)
    {
        $date = $date ?? Carbon::today();
        $amount = $amount ?? Order::whereDate('finished_at', $date)->sum('total');
        
        return self::updateOrCreate(
            [
                'metric_name' => 'daily_revenue',
                'date' => $date,
                'period' => 'daily'
            ],
            [
                'metric_type' => 'sum',
                'value' => $amount
            ]
        );
    }

    public static function recordDailyAppointments($date = null, $count = null)
    {
        $date = $date ?? Carbon::today();
        $count = $count ?? Appointment::whereDate('created_at', $date)->count();
        
        return self::updateOrCreate(
            [
                'metric_name' => 'daily_appointments',
                'date' => $date,
                'period' => 'daily'
            ],
            [
                'metric_type' => 'count',
                'value' => $count
            ]
        );
    }

    public static function recordDailyRegistrations($date = null, $count = null)
    {
        $date = $date ?? Carbon::today();
        $count = $count ?? User::whereDate('created_at', $date)->count();
        
        return self::updateOrCreate(
            [
                'metric_name' => 'daily_registrations',
                'date' => $date,
                'period' => 'daily'
            ],
            [
                'metric_type' => 'count',
                'value' => $count
            ]
        );
    }

    public static function recordTotalProfessionals($date = null, $count = null)
    {
        $date = $date ?? Carbon::today();
        $count = $count ?? CompanyProfessional::where('status', 'active')->count();
        
        return self::updateOrCreate(
            [
                'metric_name' => 'total_professionals',
                'date' => $date,
                'period' => 'daily'
            ],
            [
                'metric_type' => 'count',
                'value' => $count
            ]
        );
    }

    public static function recordTotalCompanies($date = null, $count = null)
    {
        $date = $date ?? Carbon::today();
        $count = $count ?? Company::count();
        
        return self::updateOrCreate(
            [
                'metric_name' => 'total_companies',
                'date' => $date,
                'period' => 'daily'
            ],
            [
                'metric_type' => 'count',
                'value' => $count
            ]
        );
    }
}
