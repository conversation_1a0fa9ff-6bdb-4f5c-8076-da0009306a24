[{"from": "vue", "name": "onActivated", "as": "onActivated"}, {"from": "vue", "name": "onBeforeMount", "as": "onBeforeMount"}, {"from": "vue", "name": "onBeforeUnmount", "as": "onBeforeUnmount"}, {"from": "vue", "name": "onBeforeUpdate", "as": "onBeforeUpdate"}, {"from": "vue", "name": "onErrorCaptured", "as": "onErrorCaptured"}, {"from": "vue", "name": "onDeactivated", "as": "onDeactivated"}, {"from": "vue", "name": "onMounted", "as": "onMounted"}, {"from": "vue", "name": "onServerPrefetch", "as": "onServerPrefetch"}, {"from": "vue", "name": "onUnmounted", "as": "onUnmounted"}, {"from": "vue", "name": "onUpdated", "as": "onUpdated"}, {"from": "vue", "name": "useAttrs", "as": "useAttrs"}, {"from": "vue", "name": "useSlots", "as": "useSlots"}, {"from": "vue", "name": "computed", "as": "computed"}, {"from": "vue", "name": "customRef", "as": "customRef"}, {"from": "vue", "name": "is<PERSON><PERSON><PERSON>ly", "as": "is<PERSON><PERSON><PERSON>ly"}, {"from": "vue", "name": "isRef", "as": "isRef"}, {"from": "vue", "name": "isProxy", "as": "isProxy"}, {"from": "vue", "name": "isReactive", "as": "isReactive"}, {"from": "vue", "name": "mark<PERSON>aw", "as": "mark<PERSON>aw"}, {"from": "vue", "name": "reactive", "as": "reactive"}, {"from": "vue", "name": "readonly", "as": "readonly"}, {"from": "vue", "name": "ref", "as": "ref"}, {"from": "vue", "name": "shallowReactive", "as": "shallowReactive"}, {"from": "vue", "name": "shallowReadonly", "as": "shallowReadonly"}, {"from": "vue", "name": "shallowRef", "as": "shallowRef"}, {"from": "vue", "name": "triggerRef", "as": "triggerRef"}, {"from": "vue", "name": "toRaw", "as": "toRaw"}, {"from": "vue", "name": "toRef", "as": "toRef"}, {"from": "vue", "name": "toRefs", "as": "toRefs"}, {"from": "vue", "name": "toValue", "as": "toValue"}, {"from": "vue", "name": "unref", "as": "unref"}, {"from": "vue", "name": "watch", "as": "watch"}, {"from": "vue", "name": "watchEffect", "as": "watchEffect"}, {"from": "vue", "name": "watchPostEffect", "as": "watchPostEffect"}, {"from": "vue", "name": "watchSyncEffect", "as": "watchSyncEffect"}, {"from": "vue", "name": "defineComponent", "as": "defineComponent"}, {"from": "vue", "name": "defineAsyncComponent", "as": "defineAsyncComponent"}, {"from": "vue", "name": "getCurrentInstance", "as": "getCurrentInstance"}, {"from": "vue", "name": "h", "as": "h"}, {"from": "vue", "name": "inject", "as": "inject"}, {"from": "vue", "name": "nextTick", "as": "nextTick"}, {"from": "vue", "name": "provide", "as": "provide"}, {"from": "vue", "name": "useCssModule", "as": "useCssModule"}, {"from": "vue", "name": "createApp", "as": "createApp"}, {"from": "vue", "name": "effectScope", "as": "effectScope"}, {"from": "vue", "name": "EffectScope", "as": "EffectScope"}, {"from": "vue", "name": "getCurrentScope", "as": "getCurrentScope"}, {"from": "vue", "name": "onScopeDispose", "as": "onScopeDispose"}, {"from": "vue", "name": "Component", "type": true, "as": "Component"}, {"from": "vue", "name": "ComponentPublicInstance", "type": true, "as": "ComponentPublicInstance"}, {"from": "vue", "name": "ComputedRef", "type": true, "as": "ComputedRef"}, {"from": "vue", "name": "DirectiveBinding", "type": true, "as": "DirectiveBinding"}, {"from": "vue", "name": "ExtractDefaultPropTypes", "type": true, "as": "ExtractDefaultPropTypes"}, {"from": "vue", "name": "ExtractPropTypes", "type": true, "as": "ExtractPropTypes"}, {"from": "vue", "name": "ExtractPublicPropTypes", "type": true, "as": "ExtractPublicPropTypes"}, {"from": "vue", "name": "Injection<PERSON>ey", "type": true, "as": "Injection<PERSON>ey"}, {"from": "vue", "name": "PropType", "type": true, "as": "PropType"}, {"from": "vue", "name": "Ref", "type": true, "as": "Ref"}, {"from": "vue", "name": "MaybeRef", "type": true, "as": "MaybeRef"}, {"from": "vue", "name": "MaybeRefOrGetter", "type": true, "as": "MaybeRefOrGetter"}, {"from": "vue", "name": "VNode", "type": true, "as": "VNode"}, {"from": "vue", "name": "WritableComputedRef", "type": true, "as": "WritableComputedRef"}, {"from": "vue", "name": "onRenderTracked", "as": "onRenderTracked"}, {"from": "vue", "name": "onRenderTriggered", "as": "onRenderTriggered"}, {"from": "vue", "name": "resolveComponent", "as": "resolveComponent"}, {"from": "vue", "name": "useCssVars", "as": "useCssVars"}, {"from": "vue", "name": "useModel", "as": "useModel"}, {"from": "vue", "name": "onWatcherCleanup", "as": "onWatcherCleanup"}, {"from": "vue", "name": "useId", "as": "useId"}, {"from": "vue", "name": "useTemplateRef", "as": "useTemplateRef"}, {"from": "axios", "name": "default", "as": "axios"}, {"from": "[package-name]", "name": "[import-names]", "as": "[import-names]"}, {"from": "[package-name]", "name": "[from]", "as": "[alias]"}, {"name": "meses", "as": "meses", "from": "/home/<USER>/psyplus/back/resources/js/utils/date.ts", "__source": "dir"}, {"name": "anos", "as": "anos", "from": "/home/<USER>/psyplus/back/resources/js/utils/date.ts", "__source": "dir"}, {"name": "handleTime", "as": "handleTime", "from": "/home/<USER>/psyplus/back/resources/js/utils/date.ts", "__source": "dir"}, {"name": "default", "as": "formatPrice", "from": "/home/<USER>/psyplus/back/resources/js/utils/formatPrice.ts", "__source": "dir"}, {"name": "getContrastColor", "as": "getContrastColor", "from": "/home/<USER>/psyplus/back/resources/js/utils/getContrast.ts", "__source": "dir"}, {"name": "getPhoto", "as": "getPhoto", "from": "/home/<USER>/psyplus/back/resources/js/utils/getPhoto.ts", "__source": "dir"}, {"name": "Cash", "as": "Cash", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Finance", "as": "Finance", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Date", "as": "Date", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Service", "as": "Service", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Product", "as": "Product", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "<PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON>", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Order", "as": "Order", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "ItemOrder", "as": "ItemOrder", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Cha<PERSON>", "as": "Cha<PERSON>", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Message", "as": "Message", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "NewMessage", "as": "NewMessage", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "ChatItem", "as": "ChatItem", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Professional", "as": "Professional", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "Schedule", "as": "Schedule", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}, {"name": "newOrder", "as": "newOrder", "from": "/home/<USER>/psyplus/back/resources/js/utils/models.ts", "type": true, "__source": "dir"}]