<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Cash Flow Analysis</h1>
          <div class="flex gap-2">
            <button @click="refreshData" class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
            <NuxtLink to="/analytics" class="btn btn-ghost btn-sm">
              ← Back to Dashboard
            </NuxtLink>
          </div>
        </div>

        <!-- Filter Controls -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
          <div class="flex flex-wrap gap-4 items-end">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Year</span>
              </label>
              <select v-model="filters.year" class="select select-bordered select-sm">
                <option value="">All Years</option>
                <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Start Date</span>
              </label>
              <input v-model="filters.start_date" type="date" class="input input-bordered input-sm">
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">End Date</span>
              </label>
              <input v-model="filters.end_date" type="date" class="input input-bordered input-sm">
            </div>
            <button @click="applyFilters" class="btn btn-primary btn-sm">Apply</button>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">Clear</button>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-lg"></span>
        </div>

        <!-- Cash Flow Chart -->
        <div v-else class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">Cash Flow - Predicted vs Actual</h3>
          <div v-if="chartData.labels?.length" class="h-96">
            <canvas ref="cashFlowChart"></canvas>
          </div>
          <div v-else class="text-center py-8 text-gray-500">
            No data available
          </div>
        </div>

        <!-- Data Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-6 border-b">
            <h3 class="text-lg font-semibold">Cash Flow Data</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Predicted</th>
                  <th>Actual</th>
                  <th>Variance</th>
                  <th>Period</th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="!cashFlowData.data?.length">
                  <td colspan="5" class="text-center py-8 text-gray-500">
                    No data available
                  </td>
                </tr>
                <tr v-for="item in cashFlowData.data" :key="item.id">
                  <td class="font-medium">{{ item.category }}</td>
                  <td>{{ formatCurrency(item.data?.predicted || 0) }}</td>
                  <td>{{ formatCurrency(item.data?.actual || 0) }}</td>
                  <td :class="getVarianceClass(item.data?.predicted || 0, item.data?.actual || 0)">
                    {{ formatCurrency((item.data?.actual || 0) - (item.data?.predicted || 0)) }}
                  </td>
                  <td>{{ formatDate(item.period) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Upload Section -->
        <div class="bg-white rounded-lg shadow p-6 mt-6">
          <h3 class="text-lg font-semibold mb-4">Upload Cash Flow Data</h3>
          <form @submit.prevent="uploadFile" class="space-y-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Period</span>
              </label>
              <input v-model="uploadForm.period" type="date" class="input input-bordered" required>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">CSV File</span>
              </label>
              <input @change="handleFileChange" type="file" accept=".csv,.txt" class="file-input file-input-bordered" required>
            </div>
            <button type="submit" :disabled="uploading" class="btn btn-primary">
              <span v-if="uploading" class="loading loading-spinner loading-sm mr-2"></span>
              {{ uploading ? 'Uploading...' : 'Upload' }}
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

definePageMeta({
  layout: 'professionals'
});

interface CashFlowItem {
  id: number;
  category: string;
  data?: {
    predicted: number;
    actual: number;
  };
  period: string;
}

interface ChartData {
  labels: string[];
  datasets: any[];
}

const toast = useToast();
const loading = ref(false);
const uploading = ref(false);
const cashFlowData = ref<{ data: CashFlowItem[] }>({ data: [] });
const chartData = ref<ChartData>({ labels: [], datasets: [] });
const availableYears = ref<number[]>([]);

const filters = ref({
  year: '',
  start_date: '',
  end_date: ''
});

const uploadForm = ref<{ period: string; file: File | null }>({
  period: '',
  file: null
});

const cashFlowChart = ref<HTMLCanvasElement | null>(null);
let cashFlowChartInstance: Chart | null = null;

async function loadCashFlowData() {
  try {
    loading.value = true;
    const params = Object.fromEntries(
      Object.entries(filters.value).filter(([, value]) => value !== '')
    );
    
    const { data } = await api.get('/financial/cash-flow', { params });
    cashFlowData.value = data;
    chartData.value = data.chart_data || { labels: [], datasets: [] };
    
    nextTick(() => {
      createChart();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados de fluxo de caixa');
  } finally {
    loading.value = false;
  }
}

function createChart() {
  if (cashFlowChartInstance) {
    cashFlowChartInstance.destroy();
  }

  if (!cashFlowChart.value || !chartData.value.labels?.length) return;

  const ctx = cashFlowChart.value.getContext('2d');
  if (!ctx) return;
  
  cashFlowChartInstance = new Chart(ctx, {
    type: 'line',
    data: chartData.value,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return formatCurrency(Number(value));
            }
          }
        }
      },
      plugins: {
        legend: {
          position: 'top'
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.dataset.label || '';
              const value = formatCurrency(context.parsed.y);
              return `${label}: ${value}`;
            }
          }
        }
      }
    }
  });
}

function applyFilters() {
  loadCashFlowData();
}

function clearFilters() {
  filters.value = {
    year: '',
    start_date: '',
    end_date: ''
  };
  loadCashFlowData();
}

function refreshData() {
  loadCashFlowData();
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  uploadForm.value.file = target.files?.[0] || null;
}

async function uploadFile() {
  if (!uploadForm.value.file || !uploadForm.value.period) {
    toast.error('Please fill in all fields');
    return;
  }

  try {
    uploading.value = true;
    const formData = new FormData();
    formData.append('file', uploadForm.value.file);
    formData.append('type', 'cash_flow');
    formData.append('period', uploadForm.value.period);

    await api.post('/financial/upload-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    toast.success('File uploaded successfully!');
    uploadForm.value = { period: '', file: null };
    
    // Reset file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
    
    // Refresh data
    setTimeout(() => {
      loadCashFlowData();
    }, 1000);
  } catch (error) {
    console.error(error);
    toast.error('Failed to upload file');
  } finally {
    uploading.value = false;
  }
}

function getVarianceClass(predicted: number, actual: number) {
  const variance = actual - predicted;
  if (variance > 0) return 'text-success font-semibold';
  if (variance < 0) return 'text-error font-semibold';
  return '';
}

function formatCurrency(value: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

function formatDate(dateString: string) {
  if (!dateString) return '-';
  return new Intl.DateTimeFormat('pt-BR').format(new Date(dateString));
}

onMounted(() => {
  loadCashFlowData();
});

onUnmounted(() => {
  if (cashFlowChartInstance) {
    cashFlowChartInstance.destroy();
  }
});
</script>
