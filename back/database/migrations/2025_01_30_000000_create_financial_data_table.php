<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('financial_data', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['payment_analysis', 'cash_flow', 'dre']);
            $table->string('category');
            $table->decimal('value', 15, 2)->nullable();
            $table->decimal('percentage', 5, 2)->nullable();
            $table->date('period');
            $table->integer('month');
            $table->integer('year');
            $table->json('data')->nullable(); // For storing additional data like monthly values
            $table->timestamps();

            $table->index(['type', 'period']);
            $table->index(['month', 'year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('financial_data');
    }
};
