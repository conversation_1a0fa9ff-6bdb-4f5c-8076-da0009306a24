<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $page['props']['company']['name'] ?? 'Psi+' }}</title>
    <link name="manifest" rel="manifest" href="/api/{{ $page['props']['company']['slug'] ?? 'Psi+' }}/manifest" />
    <meta name="description" content="{{ $page['props']['company']['description'] ?? 'Agendamento online para psicologos.' }}">
    <meta name="keywords" content="{{ $page['props']['company']['name'] ?? 'Psi+' }} Psi+, Agendamentos para terapia online">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="{{ $page['props']['company']['name'] ?? 'Psi+' }}">
    <meta property="og:description" content="{{ $page['props']['company']['description'] ?? 'Agendamento online para psicologos.' }}">
    <meta property="og:image" content="{{ isset($page['props']['company']['logo']) ? asset($page['props']['company']['logo'] . '_512.jpg') : asset('/icons/logo.png') }}">
    <meta property="og:image:width" content="512">
    <meta property="og:image:height" content="512">
    <meta property="og:site_name" content="{{ $page['props']['company']['name'] ?? 'Psi+' }}">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="{{ url()->current() }}">
    <meta name="twitter:title" content="{{ $page['props']['company']['name'] ?? 'Psi+' }}">
    <meta name="twitter:description" content="{{ $page['props']['company']['description'] ?? 'Agendamento online para psicologos.' }}">
    <meta name="twitter:image" content="{{ isset($page['props']['company']['logo']) ? $page['props']['company']['logo'] . '_512.webp' : '/icons/logo.png' }}">

    <!-- WhatsApp -->
    <meta property="og:image:alt" content="{{ $page['props']['company']['name'] ?? 'Psi+' }}">
    <meta property="og:image:type" content="image/webp">

    <!-- Schema.org / Google -->
    <meta itemprop="name" content="{{ $page['props']['company']['name'] ?? 'Psi+' }}">
    <meta itemprop="description" content="{{ $page['props']['company']['description'] ?? 'Agendamento online para psicologos.' }}">
    <meta itemprop="image" content="{{ isset($page['props']['company']['logo']) ? $page['props']['company']['logo'] . '_192.webp' : '/icons/logo.png' }}">

    <link rel="icon" href="{{ isset($page['props']['company']['logo']) ? $page['props']['company']['logo'] . '_48.webp' : '/icons/logo.png' }}" type="image/icon type">

    <!-- Fonts -->
    {{-- <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" /> --}}

    <!-- Scripts -->
    @routes
    @vite(['resources/js/app.js', "resources/js/Pages/{$page['component']}.vue"])
    @inertiaHead
</head>

<body class="font-sans antialiased">
    @inertia
</body>

</html>
