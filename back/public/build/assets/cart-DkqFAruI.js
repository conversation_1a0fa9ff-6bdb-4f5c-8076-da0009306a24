import{_ as k}from"./BaseButton-CxEL1ccK.js";import{_ as j}from"./CartCard.vue_vue_type_script_setup_true_lang-4Qp_1A9Z.js";import{u as C,b as $,c as B}from"./BaseLayout--Fu-QUOh.js";import{o as a,c,a as r,l as N,f as S,r as V,q as F,j as A,w as d,u as s,b as i,d as m,i as D,F as f,e as P,t as T,N as p}from"./app-BkKCG4YJ.js";import{f as E}from"./formatPrice-DFW6Dy3T.js";import{_ as I}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";function L(o,t){return a(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"})])}const O={class:"flex flex-col h-full justify-center gap-1 p-2 pt-3 rounded"},Z={key:0,class:"flex justify-center items-center h-full"},q={class:"flex flex-col items-center"},z={class:"elevated-card flex-row justify-between mt-auto"},H={class:"font-bold text-lg my-auto md:text-xl"},X=N({__name:"cart",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(o){const t=C();$();const g=S(()=>t.cart.reduce((n,e)=>n+parseFloat(e.service.price),0)),x=n=>{t.cart=t.cart.filter(e=>e.id!==n)},y=B(),l=V(),u=F.useToast();async function h(){l.value=!0;try{if(!y.isLoggedIn){u.error("Você precisa estar logado para fazer um pedido!",{position:"top"}),p.get(`/${o.slug}/login?redirect=checkout`),t.cartDrawerActive=!1;return}await t.postCart(o.slug),u.success("Pedido criado com sucesso!!!",{position:"top"}),p.get(`/${o.slug}/pedidos?checkForm=true`)}catch{return}finally{l.value=!1}}return(n,e)=>{const v=j,_=k;return a(),A(I,{slug:o.slug,company:o.company,professional:o.professional},{default:d(()=>[r("div",O,[s(t).cart.length===0?(a(),c("div",Z,[r("div",q,[i(s(L),{class:"w-12 text-accent-content"}),e[1]||(e[1]=r("p",{class:"mt-4 text-lg font-bold text-accent-content"},"Você não tem agendamentos",-1)),i(s(D),{href:`/${o.slug}/servicos`,class:"mt-0 text-lg font-bold text-primary"},{default:d(()=>e[0]||(e[0]=[m(" Agende um horário ")])),_:1},8,["href"])])])):(a(),c(f,{key:1},[(a(!0),c(f,null,P(s(t).cart,(w,b)=>(a(),c("div",{key:b},[i(v,{item:w,class:"shadow-none",cart:"",onDelete:x},null,8,["item"])]))),128)),r("div",z,[r("div",H," Total: "+T(s(E)(s(g))),1),i(_,{loading:s(l),type:"submit",disabled:s(t).cart.length===0,onClick:h},{default:d(()=>e[2]||(e[2]=[m(" Agendar ")])),_:1},8,["loading","disabled","onClick"])])],64))])]),_:1},8,["slug","company","professional"])}}});export{X as default};
