import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";

export default defineConfig({
    plugins: [
        laravel({
            input: "resources/js/app.js",
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
        AutoImport({
            include: [
                /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
                /\.vue$/,
                /\.vue\?vue/, // .vue
                /\.md$/, // .md
            ],
            imports: [
                "vue",
                {
                    axios: [
                        ["default", "axios"], // import { default as axios } from 'axios',
                    ],
                    "[package-name]": ["[import-names]", ["[from]", "[alias]"]],
                },
            ],
            defaultExportByFilename: true,
            // dirsScanOptions: {
            //     types: true, // Enable auto import the types under the directories
            // },
            dirs: [
                "./resources/js/hooks",
                "./resources/js/composables/**", // all nested modules
                "./resources/js/utils",
                {
                    glob: "./hooks",
                    types: true, // enable import the types
                },
                {
                    glob: "./composables",
                    types: false, // If top level dirsScanOptions.types importing enabled, just only disable this directory
                },
                {
                    glob: "./utils",
                    types: true, // enable import the types
                },
            ],

            dts: "./auto-imports.d.ts",

            // Auto import inside Vue template
            // see https://github.com/unjs/unimport/pull/15 and https://github.com/unjs/unimport/pull/72
            vueTemplate: false,

            vueDirectives: undefined,
            resolvers: [
                /* ... */
            ],
            viteOptimizeDeps: true,
            injectAtEnd: true,
            eslintrc: {
                enabled: false, // Default `false`
                filepath: "./.eslintrc-auto-import.json", // Default `./.eslintrc-auto-import.json`
                globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
            },

            // Generate corresponding .biomelintrc-auto-import.json file.
            // biomejs extends Docs - https://biomejs.dev/guides/how-biome-works/#the-extends-option
            biomelintrc: {
                enabled: false, // Default `false`
                filepath: "./.biomelintrc-auto-import.json", // Default `./.biomelintrc-auto-import.json`
            },
            dumpUnimportItems: "./auto-imports.json", // Default `false`
        }),
        Components({
            dirs: ["./resources/js/components"],
            extensions: ["vue"],
            // globs: ["src/components/*.{vue}"],
            deep: true,
            dts: true,
            directoryAsNamespace: true,
            collapseSamePrefixes: false,
            directives: true,
            // importPathTransform: (v) => v,
            include: [/\.vue$/, /\.vue\?vue/],
            exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/],
            excludeNames: [/^Async.+/],
            types: [],
        }),
    ],
});
