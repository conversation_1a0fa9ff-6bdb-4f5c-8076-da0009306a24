class ChartManager {
    constructor() {
        this.charts = {};
        this.defaultColors = [
            '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
            '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
        ];
    }

    destroyChart(chartId) {
        if (this.charts[chartId]) {
            this.charts[chartId].destroy();
            delete this.charts[chartId];
        }
    }

    createOverviewChart(data) {
        this.destroyChart('overview');
        
        const ctx = document.getElementById('overview-chart');
        if (!ctx) return;

        const chartData = this.prepareOverviewData(data);
        
        this.charts['overview'] = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = Utils.formatCurrency(context.parsed);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    createPaymentPieChart(data) {
        this.destroyChart('payment-pie');
        
        const ctx = document.getElementById('payment-pie-chart');
        if (!ctx) return;

        this.charts['payment-pie'] = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.percentages,
                    backgroundColor: this.defaultColors.slice(0, data.labels.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const percentage = context.parsed + '%';
                                return `${label}: ${percentage}`;
                            }
                        }
                    }
                }
            }
        });
    }

    createPaymentBarChart(data) {
        this.destroyChart('payment-bar');
        
        const ctx = document.getElementById('payment-bar-chart');
        if (!ctx) return;

        this.charts['payment-bar'] = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Value',
                    data: data.values,
                    backgroundColor: this.defaultColors[0],
                    borderColor: this.defaultColors[0],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return Utils.formatCurrency(context.parsed.y);
                            }
                        }
                    }
                }
            }
        });
    }

    createCashFlowChart(data) {
        this.destroyChart('cash-flow');
        
        const ctx = document.getElementById('cash-flow-chart');
        if (!ctx) return;

        this.charts['cash-flow'] = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = Utils.formatCurrency(context.parsed.y);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    createDreChart(data) {
        this.destroyChart('dre');
        
        const ctx = document.getElementById('dre-chart');
        if (!ctx) return;

        this.charts['dre'] = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = Utils.formatCurrency(context.parsed.y);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    prepareOverviewData(data) {
        const labels = [];
        const values = [];
        const colors = [];

        if (data && data.summary) {
            Object.values(data.summary).forEach((item, index) => {
                labels.push(this.getTypeLabel(item.type));
                values.push(Math.abs(item.total_value || 0));
                colors.push(this.defaultColors[index % this.defaultColors.length]);
            });
        }

        return {
            labels: labels,
            datasets: [{
                data: values,
                backgroundColor: colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };
    }

    getTypeLabel(type) {
        const labels = {
            'payment_analysis': 'Payment Analysis',
            'cash_flow': 'Cash Flow',
            'dre': 'DRE'
        };
        return labels[type] || type;
    }

    updateChartData(chartId, newData) {
        if (this.charts[chartId]) {
            this.charts[chartId].data = newData;
            this.charts[chartId].update();
        }
    }

    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            chart.resize();
        });
    }
}

// Initialize chart manager
const chartManager = new ChartManager();

// Handle window resize
window.addEventListener('resize', () => {
    chartManager.resizeCharts();
});
