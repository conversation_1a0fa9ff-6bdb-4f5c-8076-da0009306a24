<template>
  <div class="p-6">
    <div class="max-w-2xl mx-auto">
      <h1 class="text-2xl font-bold mb-6"><PERSON><PERSON></h1>
      
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="flex items-center mb-6">
          <div class="h-20 w-20 flex-shrink-0">
            <img
              v-if="userInfo.image"
              :src="userInfo.image"
              :alt="userInfo.name"
              class="h-20 w-20 rounded-full object-cover"
            />
            <div
              v-else
              class="h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center"
            >
              <span class="text-2xl font-medium text-gray-700">
                {{ userInfo.name?.charAt(0)?.toUpperCase() }}
              </span>
            </div>
          </div>
          <div class="ml-6">
            <h2 class="text-xl font-semibold">{{ userInfo.name }}</h2>
            <p class="text-gray-600">{{ userInfo.email }}</p>
            <p class="text-gray-600" v-if="userInfo.phone">{{ userInfo.phone }}</p>
          </div>
        </div>
        
        <div class="border-t pt-6">
          <h3 class="text-lg font-semibold mb-4">Informações da Conta</h3>
          
          <form @submit.prevent="updateProfile" class="space-y-4">
            <input-base
              v-model="form.name"
              label="Nome"
              :error="errors.name"
            />
            <input-base
              v-model="form.email"
              label="Email"
              type="email"
              :error="errors.email"
            />
            <input-base
              v-model="form.phone"
              label="Telefone"
              :error="errors.phone"
            />
            
            <div class="flex gap-4 pt-4">
              <base-button type="submit" :loading="loading">
                Salvar Alterações
              </base-button>
              <base-button variant="secondary" @click="resetForm">
                Cancelar
              </base-button>
            </div>
          </form>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">Alterar Senha</h3>
        
        <form @submit.prevent="changePassword" class="space-y-4">
          <input-base
            v-model="passwordForm.currentPassword"
            label="Senha Atual"
            type="password"
            :error="passwordErrors.currentPassword"
          />
          <input-base
            v-model="passwordForm.newPassword"
            label="Nova Senha"
            type="password"
            :error="passwordErrors.newPassword"
          />
          <input-base
            v-model="passwordForm.confirmPassword"
            label="Confirmar Nova Senha"
            type="password"
            :error="passwordErrors.confirmPassword"
          />
          
          <base-button type="submit" :loading="passwordLoading">
            Alterar Senha
          </base-button>
        </form>
      </div>
      
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4 text-red-600">Zona de Perigo</h3>
        <p class="text-gray-600 mb-4">
          Desconectar da sua conta atual.
        </p>
        <base-button variant="danger" @click="logout">
          Sair da Conta
        </base-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from "vue-toast-notification";
import { useLoginStore } from "~/store/user";
import { api } from "~/server/api";

const toast = useToast();
const loginStore = useLoginStore();
const loading = ref(false);
const passwordLoading = ref(false);

const { userInfo } = storeToRefs(loginStore);

// Profile form
const form = ref({
  name: userInfo.value.name || '',
  email: userInfo.value.email || '',
  phone: userInfo.value.phone || '',
});

const errors = ref({
  name: '',
  email: '',
  phone: '',
});

// Password form
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const passwordErrors = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const updateProfile = async () => {
  try {
    loading.value = true;
    errors.value = { name: '', email: '', phone: '' };
    
    // Basic validation
    if (!form.value.name) {
      errors.value.name = 'Nome é obrigatório';
      return;
    }
    if (!form.value.email) {
      errors.value.email = 'Email é obrigatório';
      return;
    }
    
    const { data } = await api.put('/profile', form.value);
    
    // Update store with new user info
    loginStore.setUser({
      name: data.name,
      email: data.email,
      phone: data.phone,
      image: data.image || userInfo.value.image,
    });
    
    toast.success('Perfil atualizado com sucesso!');
    
  } catch (error) {
    console.error('Error updating profile:', error);
    toast.error('Erro ao atualizar perfil');
  } finally {
    loading.value = false;
  }
};

const changePassword = async () => {
  try {
    passwordLoading.value = true;
    passwordErrors.value = { currentPassword: '', newPassword: '', confirmPassword: '' };
    
    // Basic validation
    if (!passwordForm.value.currentPassword) {
      passwordErrors.value.currentPassword = 'Senha atual é obrigatória';
      return;
    }
    if (!passwordForm.value.newPassword) {
      passwordErrors.value.newPassword = 'Nova senha é obrigatória';
      return;
    }
    if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
      passwordErrors.value.confirmPassword = 'Senhas não coincidem';
      return;
    }
    if (passwordForm.value.newPassword.length < 6) {
      passwordErrors.value.newPassword = 'Nova senha deve ter pelo menos 6 caracteres';
      return;
    }
    
    await api.put('/change-password', {
      current_password: passwordForm.value.currentPassword,
      new_password: passwordForm.value.newPassword,
    });
    
    toast.success('Senha alterada com sucesso!');
    
    // Reset form
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    };
    
  } catch (error) {
    console.error('Error changing password:', error);
    toast.error('Erro ao alterar senha');
  } finally {
    passwordLoading.value = false;
  }
};

const resetForm = () => {
  form.value = {
    name: userInfo.value.name || '',
    email: userInfo.value.email || '',
    phone: userInfo.value.phone || '',
  };
  errors.value = { name: '', email: '', phone: '' };
};

const logout = () => {
  if (confirm('Tem certeza que deseja sair da sua conta?')) {
    loginStore.logout();
  }
};

// Watch for changes in userInfo to update form
watch(userInfo, (newUserInfo) => {
  form.value = {
    name: newUserInfo.name || '',
    email: newUserInfo.email || '',
    phone: newUserInfo.phone || '',
  };
}, { deep: true });
</script>
