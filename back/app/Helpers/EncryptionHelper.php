<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Encryption\DecryptException;

class EncryptionHelper
{
    /**
     * Encrypt user data for URL
     *
     * @param array $data
     * @return string
     */
    public static function encryptUserData(array $data): string
    {
        $json = json_encode($data);
        Log::debug('Encrypting data: ' . $json);
        $encrypted = Crypt::encryptString($json);
        $urlEncoded = urlencode($encrypted);
        Log::debug('Encrypted and URL encoded data length: ' . strlen($urlEncoded));
        return $urlEncoded;
    }

    /**
     * Decrypt user data from URL
     *
     * @param string $encryptedData
     * @return array|null
     */
    public static function decryptUserData(string $encryptedData): ?array
    {
        try {
            Log::debug('Decrypting data of length: ' . strlen($encryptedData));
            $urlDecoded = urldecode($encryptedData);
            Log::debug('URL decoded data length: ' . strlen($urlDecoded));
            $decrypted = Crypt::decryptString($urlDecoded);
            Log::debug('Decrypted data: ' . $decrypted);
            $data = json_decode($decrypted, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON decode error: ' . json_last_error_msg());
                return null;
            }

            return $data;
        } catch (DecryptException $e) {
            Log::error('Failed to decrypt user data: ' . $e->getMessage());
            return null;
        }
    }
}
