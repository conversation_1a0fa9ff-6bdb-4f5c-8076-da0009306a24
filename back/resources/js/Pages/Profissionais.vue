<template>
    <BaseLayout>
        <section class="py-4 min-h-screen w-full md:py-6 bg-[#eee]">
            <div class="md:px-8 h-full px-4">
                <div class="md:hidden mb-4">
                    <button @click="showFilters = !showFilters"
                        class="btn btn-primary w-full flex justify-between items-center shadow-md">
                        <span>{{ showFilters ? 'Esconder Filtros' : 'Filtros' }}</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform duration-300"
                            :class="{ 'rotate-180': showFilters }" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                </div>

                <div class="flex flex-col md:flex-row md:gap-6 h-full">
                    <!-- Filtros (colapsável em dispositivos móveis) -->
                    <div class="w-full h-full md:w-1/4 bg-base-100 rounded-lg shadow-lg border border-base-200 transition-all duration-300 overflow-hidden"
                        :class="{ 'max-h-0 p-0 opacity-0 mb-0': !showFilters && isMobile, 'max-h-[2000px] opacity-100 mb-4': showFilters || !isMobile }">
                        <div class="p-4 h-full overflow-hidden flex flex-col">
                            <div class="flex justify-between items-center mb-4 border-b pb-2">
                                <h2 class="text-xl font-bold text-primary flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                    </svg>
                                    <span>Filtros</span>
                                </h2>
                                <button class="btn btn-ghost btn-xs" @click="clearFilters">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Limpar
                                </button>
                            </div>



                            <!-- Filtro por Público -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-2 flex items-center text-base-content">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                    Público
                                </h3>
                                <div class="flex flex-wrap gap-2">
                                    <div v-for="(type, index) in availableClientTypes" :key="'type-' + index"
                                        class="flex items-center">
                                        <label :for="'type-' + index"
                                            class="label cursor-pointer gap-2 bg-base-100 px-3 py-1 rounded-full">
                                            <input type="checkbox" :id="'type-' + index" :value="type"
                                                v-model="selectedClientTypes" @change="filterProfessionals"
                                                class="checkbox checkbox-primary checkbox-xs">
                                            <span class="label-text">{{ type }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Filtro por Especialidades -->
                            <div class="flex-1">
                                <h3 class="font-semibold mb-2 flex items-center text-base-content">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </svg>
                                    Especialidades
                                </h3>
                                <!-- Filtro de especialidades por nome -->
                                <div class="relative mb-2">
                                    <input type="text" placeholder="Filtrar especialidades..."
                                        class="input input-bordered input-sm w-full pr-10 bg-base-100"
                                        v-model="specialtyFilter">
                                    <button v-if="specialtyFilter" @click="specialtyFilter = ''"
                                        class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20"
                                            fill="currentColor">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                                <div
                                    class="overflow-y-auto pr-2 bg-base-100 p-2 rounded-lg h-full max-h-[calc(100vh-350px)]">
                                    <div v-for="(specialty, index) in filteredSpecialties" :key="'spec-' + index"
                                        class="flex items-center py-1 border-b border-base-300 last:border-0">
                                        <input type="checkbox" :id="'spec-' + index" :value="specialty"
                                            v-model="selectedSpecialties" @change="handleSpecialtySelection"
                                            class="checkbox checkbox-primary checkbox-sm mr-2">
                                        <label :for="'spec-' + index"
                                            class="text-sm cursor-pointer hover:text-primary">{{
                                                specialty }}</label>
                                    </div>
                                    <div v-if="filteredSpecialties.length === 0"
                                        class="py-2 text-center text-sm text-gray-500">
                                        Nenhuma especialidade encontrada
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resultados -->
                    <div class="w-full md:w-3/4">
                        <div class="bg-base-100 p-3 rounded-lg shadow-lg border border-base-200 mb-4">
                            <div class="flex justify-between items-center mb-3">
                                <h2 class="text-xl font-bold text-primary">Profissionais</h2>
                                <div class="badge badge-primary badge-lg text-nowrap text-sm  ml-2 sm:text-base">
                                    {{ filteredProfessionals.length }} encontrado(s)
                                </div>
                            </div>

                            <!-- Busca por nome do profissional (movido para cima da lista) -->
                            <div class="relative">
                                <div class="flex items-center absolute inset-y-0 left-0 pl-3 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <input type="text" placeholder="Buscar profissional por nome..."
                                    class="input input-bordered w-full pl-10 pr-10" v-model="searchName"
                                    @input="filterProfessionals">
                                <button v-if="searchName" @click="searchName = ''; filterProfessionals()"
                                    class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div v-if="loading" class="flex justify-center items-center h-40">
                            <div class="loading loading-spinner loading-lg text-primary"></div>
                        </div>
                        <div v-else-if="filteredProfessionals.length === 0"
                            class="text-center py-10 bg-base-100 rounded-lg shadow-lg border border-base-200">
                            <p class="text-lg">Nenhum profissional encontrado com os filtros selecionados.</p>
                            <button @click="clearFilters" class="btn btn-primary btn-sm mt-4">Limpar filtros</button>
                        </div>
                        <div v-else class="flex flex-col gap-4 mt-6">
                            <div v-for="(professional, index) in filteredProfessionals" :key="index"
                                class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow border border-base-200">
                                <div class="card-body p-4 hover:bg-base-200/50 transition-colors duration-200">
                                    <div class="flex flex-col md:flex-row items-start md:items-center gap-6">
                                        <!-- Avatar and Name -->
                                        <div class="flex-shrink-0 flex items-center justify-center w-full md:w-auto">
                                            <div class="avatar">
                                                <div
                                                    class="w-20 h-20 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                                    <img :src="professional.user.profile_photo_path || '/placeholder-avatar.jpg'"
                                                        alt="Avatar" />
                                                </div>
                                            </div>
                                            <h3
                                                class="font-bold text-lg mb-2 text-left ml-4 md:text-left md:hidden block">
                                                {{ professional.user.name }}</h3>

                                        </div>

                                        <!-- Professional Info -->
                                        <div class="flex-grow w-full md:w-auto">
                                            <h3 class="font-bold text-lg mb-2 text-center md:text-left hidden md:block">
                                                {{ professional.user.name }}</h3>

                                            <!-- Público -->
                                            <div class="mb-2 flex-wrap flex items-center gap-1">
                                                <h4 class="font-semibold text-sm flex items-center md:justify-start">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                                    </svg>
                                                    Público:
                                                </h4>
                                                <span v-for="(type, i) in professional.public?.split(',') || []"
                                                    :key="'pt-' + i" class="badge badge-sm badge-outline">
                                                    {{ type }}
                                                </span>
                                            </div>

                                            <!-- Especialidades -->
                                            <div class=" flex items-center gap-1 flex-wrap">
                                                <h4 class="font-semibold text-sm flex items-center md:justify-start">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                    </svg>
                                                    Especialidades:
                                                </h4>
                                                <span
                                                    v-for="(spec, i) in professional.specialties?.split(',').slice(0, 5) || []"
                                                    :key="'ps-' + i" class="badge badge-sm badge-primary">
                                                    {{ spec }}
                                                </span>
                                                <span v-if="professional.specialties?.split(',').length > 5"
                                                    class="badge badge-sm badge-ghost">
                                                    +{{ professional.specialties?.split(',').length - 5 }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Action Button -->
                                        <div
                                            class="flex-shrink-0 flex items-center justify-center w-full md:w-auto mt-4 md:mt-0">
                                            <button class="btn btn-primary w-full md:w-auto"
                                                @click="viewProfessional(professional)">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                                Ver perfil
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <footer class="shadow-xl py-8">
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-3 gap-8 text-center md:text-left">
                    <div>
                        <h4 class="font-bold text-lg mb-4">Psy +</h4>
                        <p class="text-sm">Conectando pessoas a profissionais de saúde mental</p>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg mb-4">Links Úteis</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary">Sobre Nós</a></li>
                            <li><a href="#" class="hover:text-primary">Contato</a></li>
                            <li><a href="#" class="hover:text-primary">Blog</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold text-lg mb-4">Contato</h4>
                        <p class="text-sm"><EMAIL></p>
                    </div>
                </div>
                <div class="text-center mt-8 text-sm">
                    <p>&copy; 2025 Psy +. Todos os direitos reservados.</p>
                </div>
            </div>
        </footer>
    </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import axios from 'axios'
import { debounce } from 'lodash'
import { api } from '../server/api'
import BaseLayout from '@/Layouts/BaseLayout.vue'

// Estado
const loading = ref(true)
const professionals = ref([])
const searchName = ref('')
const selectedClientTypes = ref([])
const selectedSpecialties = ref([])
const showFilters = ref(false)
const isMobile = ref(false)
const specialtyFilter = ref('')

// Dados de filtro
const availableSpecialties = ref<string[]>([
    'Ansiedade',
    'Depressão',
    'Transtorno Bipolar',
    'Transtorno Obsessivo-Compulsivo (TOC)',
    'Estresse Pós-Traumático (TEPT)',
    'Transtornos Alimentares',
    'Fobias',
    'TDA/TDAH',
    'Transtorno do Espectro Autista (TEA)',
    'Esquizofrenia',
    'Transtornos de Personalidade',
    'Dependência Química',
    'Luto',
    'Estresse',
    'Burnout',
    'Problemas de Relacionamento',
    'Terapia de Casal',
    'Terapia Familiar',
    'Orientação Parental',
    'Orientação Profissional',
    'Desenvolvimento Pessoal',
    'Autoestima',
    'Habilidades Sociais',
    'Transtornos do Sono',
    'Disfunções Sexuais',
    'Questões de Gênero e Sexualidade',
    'Transtornos Psicossomáticos',
    'Neuropsicologia',
    'Psicologia Esportiva',
    'Psicologia Organizacional'
])

// Client types as strings
const availableClientTypes = ref<string[]>([
    'Infantil',
    'Adolescente',
    'Adulto',
    'Idoso',
    'Casal',
])

// Computed properties
const filteredSpecialties = computed(() => {
    if (specialtyFilter.value === '') {
        return availableSpecialties.value
    }

    return availableSpecialties.value.filter(specialty =>
        specialty.toLowerCase().includes(specialtyFilter.value.toLowerCase())
    )
})

const filteredProfessionals = computed(() => {
    if (!professionals.value) return []

    return professionals.value.filter(professional => {
        // Filtro por nome
        const nameMatch = searchName.value === '' ||
            professional.user?.name.toLowerCase().includes(searchName.value.toLowerCase())

        // Filtro por público
        const publicTypes = professional.public ? professional.public?.split(',') : []
        const publicMatch = selectedClientTypes.value.length === 0 ||
            selectedClientTypes.value.some(type => publicTypes.includes(type))

        // Filtro por especialidades
        const specs = professional.specialties ? professional.specialties?.split(',') : []
        const specialtyMatch = selectedSpecialties.value.length === 0 ||
            selectedSpecialties.value.some(spec => specs.includes(spec))

        return nameMatch && publicMatch && specialtyMatch
    })
})

// Métodos
async function fetchProfessionals() {
    try {
        loading.value = true
        // Construindo a URL com os filtros selecionados
        let url = '/get-all/psychologists';
        const params = new URLSearchParams();

        if (searchName.value) {
            params.append('name', searchName.value);
        }

        if (selectedClientTypes.value.length > 0) {
            params.append('public', selectedClientTypes.value.join(','));
        }

        if (selectedSpecialties.value.length > 0) {
            params.append('specialties', selectedSpecialties.value.join(','));
        }

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        // Fazendo a requisição
        const response = await api.get(url);
        professionals.value = response.data;

        // Verificando se há dados e se estão no formato esperado
        if (professionals.value && Array.isArray(professionals.value)) {
            //   console.log(`Carregados ${professionals.value.length} profissionais`);
        } else {
            console.warn('Formato de resposta inesperado:', professionals.value);
            professionals.value = [];
        }
    } catch (error) {
        console.error('Erro ao buscar profissionais:', error);
        professionals.value = [];
    } finally {
        loading.value = false;
    }
}

// Debounce para evitar muitas requisições quando o usuário está digitando
const filterProfessionals = debounce(() => {
    // Quando os filtros mudam, buscamos novamente os profissionais da API
    fetchProfessionals();
}, 800); // Aumentado de 300ms para 800ms para dar mais tempo ao usuário digitar

function viewProfessional(professional: any) {
    // Redirecionar para a página do profissional
    // Adapte conforme necessário para sua aplicação
    router.get(`/${professional.slug}`)
}

// Métodos adicionais
function clearFilters() {
    searchName.value = ''
    specialtyFilter.value = ''
    selectedClientTypes.value = []
    selectedSpecialties.value = []
    fetchProfessionals()
}

// Função removida pois já está sendo feita pelo computed property filteredSpecialties

function handleSpecialtySelection() {
    // Limpar o filtro de especialidades quando uma especialidade é selecionada
    specialtyFilter.value = ''
    filterProfessionals()
}

function checkMobile() {
    isMobile.value = window.innerWidth < 768
}

// Lifecycle hooks
onMounted(() => {
    fetchProfessionals()
    checkMobile()
    window.addEventListener('resize', checkMobile)
})
</script>
