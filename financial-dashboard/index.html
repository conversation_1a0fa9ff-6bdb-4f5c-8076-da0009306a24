<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Analytics Dashboard</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Financial Analytics Dashboard</h1>
            <div class="user-info">
                <span id="user-name">Admin User</span>
                <button id="logout-btn" class="btn btn-secondary">Logout</button>
            </div>
        </header>

        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="overview">Overview</button>
            <button class="tab-btn" data-tab="payment-analysis">Payment Analysis</button>
            <button class="tab-btn" data-tab="cash-flow">Cash Flow</button>
            <button class="tab-btn" data-tab="dre">DRE</button>
            <button class="tab-btn" data-tab="upload">Upload Data</button>
        </nav>

        <div class="filters">
            <div class="filter-group">
                <label for="year-filter">Year:</label>
                <select id="year-filter">
                    <option value="">All Years</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="month-filter">Month:</label>
                <select id="month-filter">
                    <option value="">All Months</option>
                    <option value="1">January</option>
                    <option value="2">February</option>
                    <option value="3">March</option>
                    <option value="4">April</option>
                    <option value="5">May</option>
                    <option value="6">June</option>
                    <option value="7">July</option>
                    <option value="8">August</option>
                    <option value="9">September</option>
                    <option value="10">October</option>
                    <option value="11">November</option>
                    <option value="12">December</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="start-date">Start Date:</label>
                <input type="date" id="start-date">
            </div>
            <div class="filter-group">
                <label for="end-date">End Date:</label>
                <input type="date" id="end-date">
            </div>
            <button id="apply-filters" class="btn btn-primary">Apply Filters</button>
            <button id="clear-filters" class="btn btn-secondary">Clear</button>
        </div>

        <div class="loading" id="loading" style="display: none;">
            <div class="spinner"></div>
            <p>Loading data...</p>
        </div>

        <div class="error-message" id="error-message" style="display: none;">
            <p id="error-text"></p>
            <button id="retry-btn" class="btn btn-primary">Retry</button>
        </div>

        <!-- Overview Tab -->
        <div class="tab-content active" id="overview-tab">
            <div class="summary-cards">
                <div class="card">
                    <h3>Total Records</h3>
                    <p class="metric" id="total-records">-</p>
                </div>
                <div class="card">
                    <h3>Total Value</h3>
                    <p class="metric" id="total-value">-</p>
                </div>
                <div class="card">
                    <h3>Average Value</h3>
                    <p class="metric" id="avg-value">-</p>
                </div>
                <div class="card">
                    <h3>Latest Period</h3>
                    <p class="metric" id="latest-period">-</p>
                </div>
            </div>
            <div class="chart-container">
                <canvas id="overview-chart"></canvas>
            </div>
        </div>

        <!-- Payment Analysis Tab -->
        <div class="tab-content" id="payment-analysis-tab">
            <div class="chart-row">
                <div class="chart-container">
                    <h3>Payment Methods Distribution</h3>
                    <canvas id="payment-pie-chart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Payment Values</h3>
                    <canvas id="payment-bar-chart"></canvas>
                </div>
            </div>
            <div class="data-table">
                <h3>Payment Analysis Data</h3>
                <table id="payment-table">
                    <thead>
                        <tr>
                            <th>Payment Method</th>
                            <th>Value</th>
                            <th>Percentage</th>
                            <th>Period</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>

        <!-- Cash Flow Tab -->
        <div class="tab-content" id="cash-flow-tab">
            <div class="chart-container">
                <h3>Cash Flow - Predicted vs Actual</h3>
                <canvas id="cash-flow-chart"></canvas>
            </div>
            <div class="data-table">
                <h3>Cash Flow Data</h3>
                <table id="cash-flow-table">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Predicted</th>
                            <th>Actual</th>
                            <th>Variance</th>
                            <th>Period</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>

        <!-- DRE Tab -->
        <div class="tab-content" id="dre-tab">
            <div class="chart-container">
                <h3>DRE - Revenue, Expenses & Profit</h3>
                <canvas id="dre-chart"></canvas>
            </div>
            <div class="data-table">
                <h3>DRE Data</h3>
                <table id="dre-table">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Value</th>
                            <th>Period</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>

        <!-- Upload Tab -->
        <div class="tab-content" id="upload-tab">
            <div class="upload-section">
                <h3>Upload Financial Data</h3>
                <form id="upload-form">
                    <div class="form-group">
                        <label for="data-type">Data Type:</label>
                        <select id="data-type" required>
                            <option value="">Select data type</option>
                            <option value="payment_analysis">Payment Analysis</option>
                            <option value="cash_flow">Cash Flow</option>
                            <option value="dre">DRE</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="period">Period:</label>
                        <input type="date" id="period" required>
                    </div>
                    <div class="form-group">
                        <label for="csv-file">CSV File:</label>
                        <input type="file" id="csv-file" accept=".csv,.txt" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </form>
                <div class="upload-result" id="upload-result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
