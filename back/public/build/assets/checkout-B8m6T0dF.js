import{_ as b}from"./BaseButton-CxEL1ccK.js";import{_ as w}from"./CartCard.vue_vue_type_script_setup_true_lang-4Qp_1A9Z.js";import{c as k,u as N}from"./BaseLayout--Fu-QUOh.js";import{l as C,q as S,r as d,f as j,o as n,j as B,w as u,a as t,c as m,e as F,b as f,u as a,F as T,t as V,d as $,N as p}from"./app-BkKCG4YJ.js";import{f as P}from"./formatPrice-DFW6Dy3T.js";import{_ as z}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const D={class:"p-3 min-h-full flex flex-col relative"},E={class:"flex gap-6 md:gap-12 flex-col md:flex-row-reverse"},L={class:"gap-1 flex flex-col flex-1 p-1"},O={class:"elevated-card flex flex-row justify-between mt-auto"},q={class:"font-bold text-lg my-auto md:text-xl"},Q=C({__name:"checkout",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(e){const i=S.useToast(),g=k(),r=d();d("local");const o=N(),_=j(()=>o.cart.reduce((c,s)=>{const l=parseFloat(Number(s.service.price).toString());return c+l},0));async function x(){r.value=!0;try{if(!g.isLoggedIn){i.error("Você precisa estar logado para fazer um pedido!",{position:"top"}),p.get(`/${e.slug}/login?redirect=checkout`),o.cartDrawerActive=!1;return}await o.postCart(e.slug),i.success("Pedido criado com sucesso!!!",{position:"top"}),p.get("pedidos")}catch{return}finally{r.value=!1}}return(c,s)=>{const l=w,y=b;return n(),B(z,{slug:e.slug,company:e.company,professional:e.professional},{default:u(()=>[t("div",D,[t("div",E,[t("div",L,[(n(!0),m(T,null,F(a(o).cart,(v,h)=>(n(),m("div",{key:h},[f(l,{item:v,class:"shadow-none"},null,8,["item"])]))),128))])]),t("div",O,[t("div",q," Total: "+V(a(P)(a(_))),1),f(y,{onClick:x,loading:a(r),class:"w-auto md:text-lg !py-1 md:pb-1"},{default:u(()=>s[0]||(s[0]=[$(" Finalizar ")])),_:1},8,["loading"])])])]),_:1},8,["slug","company","professional"])}}});export{Q as default};
