<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onUpdate('cascade')
                ->onDelete('cascade');
            $table->text('name');
            $table->enum('category', ['service', 'bill']);
            $table->enum('type', ['profit', 'loss']);
            $table->text('notes')->nullable();
            $table->decimal('total', 10, 2);
            // product related fields
            $table->enum('stock_movement', ['addition', 'removal'])->nullable();
            $table->integer('quantity')->nullable();
            $table->decimal('total_spent', 10, 2)->nullable();
            $table->decimal('total_earned', 10, 2)->nullable();
            $table->enum('reason', ['purchase', 'sale', 'waste', 'used', 'other'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('movements');
    }
};
