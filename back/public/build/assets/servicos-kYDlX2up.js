import{_ as F}from"./BaseButton-CxEL1ccK.js";import{h as z}from"./date-Kj-P0Std.js";import{f as S}from"./formatPrice-DFW6Dy3T.js";import{l as $,r as n,x as L,o as i,c as C,a as s,t as p,d,u as v,b as h,w,n as k,j as b,g as P,F as V,f as A,h as D,N as _,e as M}from"./app-BkKCG4YJ.js";import{_ as O}from"./Skeleton.vue_vue_type_script_setup_true_lang-BgQVuwFr.js";import{_ as T,a as q}from"./AppLayout.vue_vue_type_script_setup_true_lang-BcExojnd.js";import{u as E,b as G}from"./BaseLayout--Fu-QUOh.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./CalendarDaysIcon-BL3Qt4XI.js";const Q={class:"sm:card-body sm:py-2 sm:px-6"},U={class:"w-full h-full flex flex-col px-2 sm:p-0 py-1"},H={class:"font-bold line-clamp-2 leading-5 text-base"},I={class:"text-xs mt-auto"},J={class:"justify-between md:flex hidden"},K={class:"mt-2 text-xs sm:text-base"},R={class:"font-bold"},W={class:"ml-auto flex flex-col py-2 sm:hidden"},X={class:"sm:text-base"},Y={class:"font-bold mb-2 text-right"},Z=$({__name:"Card",props:{item:{},cardStyles:{}},emits:["schedule"],setup(t,{emit:y}){const u=y,c=n(!1);function r(){c.value=!1}return(o,e)=>{const l=F,x=L("service-modal");return i(),C(V,null,[s("div",{class:k(["card card-side sm:flex-col w-full shadow-sm sm:w-52 h-24 sm:h-72 bg-base-200 rounded-lg sm:p-0 p-1 px-2",`${o.cardStyles}`])},[s("div",Q,[s("div",U,[s("div",H,p(o.item.name),1),s("span",I,[e[2]||(e[2]=s("b",null,"Duração:",-1)),e[3]||(e[3]=d()),e[4]||(e[4]=s("br",null,null,-1)),d(" "+p(v(z)(o.item.duration)),1)]),s("div",J,[s("div",K,[e[5]||(e[5]=d(" Preço: ")),s("span",R,p(v(S)(parseFloat(o.item.price))),1)])])]),h(l,{color:"primary",size:"sm",class:"text-xs mt-auto btn-sm text-center hidden sm:block",onClick:e[0]||(e[0]=g=>u("schedule"))},{default:w(()=>e[6]||(e[6]=[d(" Agendar ")])),_:1})]),s("div",W,[s("div",X,[s("div",Y,p(v(S)(parseFloat(o.item.price))),1)]),h(l,{color:"primary",size:"sm",class:"text-xs mt-auto btn-sm sm:hidden ml-auto",onClick:e[1]||(e[1]=g=>u("schedule"))},{default:w(()=>e[7]||(e[7]=[d(" Agendar ")])),_:1})])],2),o.item?(i(),b(x,{key:0,"modal-state":v(c),"close-modal":r,type:"services",item:o.item},null,8,["modal-state","item"])):P("",!0)],64)}}}),ee={class:"px-4 pt-3"},se={class:"flex flex-wrap sm:gap-10 gap-1 justify-center sm:mt-10 p-1 mt-3 overflow-auto mx-1"},ce=$({__name:"servicos",props:{slug:{type:String},company:{type:Object},professional:{type:Object}},setup(t){const{filterProductsByGame:y,addService:u,cartLength:c}=E();n();const r=n(!0);G();const o=n(),e=n(),l=n(""),x=A(()=>{if(!e.value)return[];if(!l.value)return e.value;const a=l.value.toLowerCase();return e.value.filter(f=>f.name.toLowerCase().includes(a))});function g(a){o.value=a,u(a),t.company.solo_professional?_.get(`/${t.slug}/horarios`):_.get(`/${t.slug}/profissionais`)}return n(),D(async()=>{if(c)_.get(`/${t.slug}/cart`);else try{r.value=!0;const a=await y(`${t.slug}/services`);e.value=a.data,r.value=!1}catch{return}}),(a,f)=>{const B=q,N=O,j=Z;return i(),b(T,{slug:t.slug,company:t.company,professional:t.professional},{default:w(()=>[s("div",{class:k(r.value?"h-full overflow-hidden":"")},[s("div",ee,[h(B,{type:"text",modelValue:l.value,"onUpdate:modelValue":f[0]||(f[0]=m=>l.value=m),placeholder:"Buscar serviços...","label-style":"!-mb-2 ml-2 z-10 text-xs !text-accent-content font-bold",class:"sm:min-w-52","label-bg":"!bg-base-100",label:"Filtrar serviços",inputClasses:"!border-black"},null,8,["modelValue"])]),s("div",se,[r.value?(i(),b(N,{key:0,number:10,classes:"h-24 w-full sm:w-52 sm:h-72"})):(i(!0),C(V,{key:1},M(x.value,m=>(i(),b(j,{key:m.id,item:m,type:"services",onSchedule:te=>g(m)},null,8,["item","onSchedule"]))),128))])],2)]),_:1},8,["slug","company","professional"])}}});export{ce as default};
