<?php

namespace App\Http\Controllers;

use App\Models\RecurrentPause;
use App\Models\CompanyProfessional;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class RecurrentPauseController extends Controller
{
    public function index(Request $request)
    {
        $professionalId = $request->query('professionalId');
        
        if (!$professionalId) {
            $userId = Auth::id();
            $professional = CompanyProfessional::where('user_id', $userId)
                ->where('company_id', $request->get('company_id'))
                ->first();
            $professionalId = $professional->id;
        }

        $recurrentPauses = RecurrentPause::where('company_professional_id', $professionalId)
            ->where('is_active', true)
            ->with('professional')
            ->get();

        return response()->json($recurrentPauses);
    }

    public function store(Request $request)
    {
        $request->validate([
            'day_of_week' => 'required|integer|between:0,6',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'title' => 'nullable|string|max:255',
            'professionalId' => 'nullable|integer',
        ]);

        $professionalId = $request->professionalId;
        if (!$professionalId) {
            $userId = Auth::id();
            $professional = CompanyProfessional::where('user_id', $userId)
                ->where('company_id', $request->get('company_id'))
                ->first();
            $professionalId = $professional->id;
        }

        // Check for conflicts with existing recurrent pauses
        $existingPauses = RecurrentPause::where('company_professional_id', $professionalId)
            ->where('day_of_week', $request->day_of_week)
            ->where('is_active', true)
            ->get();

        $startTime = Carbon::parse($request->start_time);
        $endTime = Carbon::parse($request->end_time);

        foreach ($existingPauses as $existingPause) {
            $existingStart = Carbon::parse($existingPause->start_time);
            $existingEnd = Carbon::parse($existingPause->end_time);
            
            // Check for overlap
            if ($startTime->lt($existingEnd) && $endTime->gt($existingStart)) {
                return response()->json([
                    'message' => 'This time slot conflicts with an existing recurrent pause'
                ], 400);
            }
        }

        $recurrentPause = RecurrentPause::create([
            'company_professional_id' => $professionalId,
            'day_of_week' => $request->day_of_week,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'title' => $request->title ?: 'Horário fixo',
            'is_active' => true,
        ]);

        return response()->json($recurrentPause->load('professional'), 201);
    }

    public function update(Request $request, RecurrentPause $recurrentPause)
    {
        $request->validate([
            'day_of_week' => 'sometimes|integer|between:0,6',
            'start_time' => 'sometimes|date_format:H:i',
            'end_time' => 'sometimes|date_format:H:i',
            'title' => 'nullable|string|max:255',
            'is_active' => 'sometimes|boolean',
        ]);

        // If updating times, validate end_time is after start_time
        $startTime = $request->start_time ?: $recurrentPause->start_time;
        $endTime = $request->end_time ?: $recurrentPause->end_time;
        
        if (Carbon::parse($endTime)->lte(Carbon::parse($startTime))) {
            return response()->json([
                'message' => 'End time must be after start time'
            ], 400);
        }

        $recurrentPause->update($request->only([
            'day_of_week',
            'start_time', 
            'end_time',
            'title',
            'is_active'
        ]));

        return response()->json($recurrentPause->load('professional'));
    }

    public function destroy(RecurrentPause $recurrentPause)
    {
        $recurrentPause->delete();
        
        return response()->json(['message' => 'Recurrent pause deleted successfully']);
    }

    public function toggle(RecurrentPause $recurrentPause)
    {
        $recurrentPause->update([
            'is_active' => !$recurrentPause->is_active
        ]);

        return response()->json($recurrentPause->load('professional'));
    }
}
