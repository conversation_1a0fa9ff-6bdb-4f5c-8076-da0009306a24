import{l as h,r as g,f as u,h as y,z as _,o,c as a,Y as f,a as s,t as e,F as p,e as m,g as w}from"./app-BkKCG4YJ.js";const P={class:"min-h-screen bg-base-100"},k={class:"container mx-auto px-4 py-8"},C={class:"bg-base-100 shadow-lg rounded-lg overflow-hidden"},N={class:"bg-primary/10 p-6"},B={class:"flex flex-col md:flex-row items-center md:items-start"},j={class:"avatar mb-4 md:mb-0 md:mr-6"},L={class:"w-32 h-32 rounded-full"},S=["src"],E={class:"text-center md:text-left"},V={class:"text-2xl font-bold"},A={class:"mt-4 flex flex-wrap gap-2 justify-center md:justify-start"},F=["href"],D={class:"p-6"},I={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},R={class:"md:col-span-2 space-y-6"},T={class:"flex flex-wrap gap-2"},$={class:"flex flex-wrap gap-2"},q={class:"space-y-6"},z={class:"bg-base-200 p-4 rounded-lg"},M={class:"space-y-2"},O={class:"flex items-center"},Y={class:"flex items-center"},G={class:"bg-base-200 p-4 rounded-lg"},H={class:"space-y-3"},J={class:"font-semibold"},K={class:"text-sm"},Q={class:"text-xs"},U={key:0,class:"text-center py-2"},X=h({__name:"show",props:{professional:{type:Object,required:!0}},setup(l){const r=l,c=g([]),b=u(()=>r.professional.specialties?r.professional.specialties.split(","):[]),v=u(()=>r.professional.public?r.professional.public.split(","):[]);function x(n){return new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(n)}return y(async()=>{try{const n=await _.get(`/api/professional/${r.professional.id}/services`);c.value=n.data}catch(n){console.error("Erro ao buscar serviços:",n)}}),(n,t)=>(o(),a("div",P,[t[9]||(t[9]=f('<header class="bg-base-100 shadow-md"><div class="container mx-auto px-4 py-4 flex justify-between items-center"><div class="text-2xl font-bold text-primary">Psy +</div><nav><a href="/" class="btn btn-outline btn-sm mr-2">Voltar</a><a href="#" class="btn btn-primary btn-sm">Para Profissionais</a></nav></div></header>',1)),s("div",k,[s("div",C,[s("div",N,[s("div",B,[s("div",j,[s("div",L,[s("img",{src:l.professional.user.profile_photo_url||"/placeholder-avatar.jpg",alt:"Avatar"},null,8,S)])]),s("div",E,[s("h1",V,e(l.professional.user.name),1),t[1]||(t[1]=s("p",{class:"text-gray-600 mt-1"},"Psicólogo(a)",-1)),s("div",A,[t[0]||(t[0]=s("button",{class:"btn btn-primary"},"Agendar Consulta",-1)),s("a",{href:`https://wa.me/55${l.professional.user.phone}`,target:"_blank",class:"btn btn-outline"}," Contato ",8,F)])])])]),s("div",D,[s("div",I,[s("div",R,[s("div",null,[t[2]||(t[2]=s("h2",{class:"text-xl font-bold mb-3"},"Sobre",-1)),s("p",null,e(l.professional.about||"Nenhuma informação disponível."),1)]),s("div",null,[t[3]||(t[3]=s("h2",{class:"text-xl font-bold mb-3"},"Especialidades",-1)),s("div",T,[(o(!0),a(p,null,m(b.value,(i,d)=>(o(),a("span",{key:d,class:"badge badge-primary p-3"},e(i),1))),128))])]),s("div",null,[t[4]||(t[4]=s("h2",{class:"text-xl font-bold mb-3"},"Público Atendido",-1)),s("div",$,[(o(!0),a(p,null,m(v.value,(i,d)=>(o(),a("span",{key:d,class:"badge badge-outline p-3"},e(i),1))),128))])])]),s("div",q,[s("div",z,[t[7]||(t[7]=s("h2",{class:"text-lg font-bold mb-3"},"Informações de Contato",-1)),s("ul",M,[s("li",O,[t[5]||(t[5]=s("span",{class:"font-semibold mr-2"},"Email:",-1)),s("span",null,e(l.professional.user.email),1)]),s("li",Y,[t[6]||(t[6]=s("span",{class:"font-semibold mr-2"},"Telefone:",-1)),s("span",null,e(l.professional.user.phone),1)])])]),s("div",G,[t[8]||(t[8]=s("h2",{class:"text-lg font-bold mb-3"},"Serviços",-1)),s("div",H,[(o(!0),a(p,null,m(c.value,(i,d)=>(o(),a("div",{key:d,class:"p-3 bg-base-100 rounded-md shadow-sm"},[s("div",J,e(i.name),1),s("div",K,e(x(i.price)),1),s("div",Q,"Duração: "+e(i.duration)+" min",1)]))),128)),c.value.length===0?(o(),a("div",U," Nenhum serviço disponível. ")):w("",!0)])])])])])])]),t[10]||(t[10]=f('<footer class="bg-base-200 py-8 mt-12"><div class="container mx-auto px-4"><div class="grid md:grid-cols-3 gap-8 text-center md:text-left"><div><h4 class="font-bold text-lg mb-4">Psy +</h4><p class="text-sm">Conectando pessoas a profissionais de saúde mental</p></div><div><h4 class="font-bold text-lg mb-4">Links Úteis</h4><ul class="space-y-2 text-sm"><li><a href="#" class="hover:text-primary">Sobre Nós</a></li><li><a href="#" class="hover:text-primary">Contato</a></li><li><a href="#" class="hover:text-primary">Blog</a></li></ul></div><div><h4 class="font-bold text-lg mb-4">Contato</h4><p class="text-sm">contato@Psy +.com</p></div></div><div class="text-center mt-8 text-sm"><p>© 2025 Psy +. Todos os direitos reservados.</p></div></div></footer>',1))]))}});export{X as default};
