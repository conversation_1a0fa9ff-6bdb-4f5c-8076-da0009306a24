<template>
  <!-- <ClientOnly> -->
  <TheHeader v-if="!isLogin" />
  <the-drawer>
    <SideBar v-if="!isLogin" />
    <div class="w-full h-full flex flex-col overflow-hidden">
      <Header v-if="!isLogin" class="hidden lg:flex" />
      <div class="flex-1 overflow-auto">
        <slot />
      </div>
    </div>
  </the-drawer>
  <!-- </ClientOnly> -->
</template>

<script lang="ts" setup>
import { api } from "~/server/api";
import { useCompanyStore } from "~/store/company";
import { useLoginStore } from "~/store/user";
import { useNotifications } from "~/store/notifications";
import * as PusherPushNotifications from "@pusher/push-notifications-web";

const company = useCompanyStore();
const route = useRoute();
const isLogin = computed(() => {
  return [
    "/login",
    "/login/",
    "/forgot-password",
    "/forgot-password/",
    "/recover-password",
    "/recover-password/",
    "/criar-conta",
    "/criar-conta/",
    "/completar-cadastro",
    "/completar-cadastro/",
    "/super-admin-login",
    "/super-admin-login/",
  ].includes(route.path);
});
async function getProfile() {
  getNotifications();
  const { data } = await api.get("/my-company");
  updateCompanyData(data);
}
async function getNotifications() {
  const { data } = await api.get("/notifications");
  useNotifications().setNotifications(data);
}
function updateCompanyData(data: any) {
  company.name = data.name;
  company.instagram = data.instagram;
  company.whatsapp = data.whatsapp;
  company.buttons_color = data.buttons_color;
  company.background_color = data.background_color;
  company.cards_color = data.cards_color;
  company.logo = data.logo;
  company.slug = data.slug;
  company.sunday_time = data.sunday_time;
  company.sunday_pause = data.sunday_pause;
  company.monday_time = data.monday_time;
  company.monday_pause = data.monday_pause;
  company.thursday_time = data.thursday_time;
  company.thursday_pause = data.thursday_pause;
  company.wednesday_time = data.wednesday_time;
  company.wednesday_pause = data.wednesday_pause;
  company.friday_time = data.friday_time;
  company.friday_pause = data.friday_pause;
  company.saturday_time = data.saturday_time;
  company.tuesday_time = data.tuesday_time;
  company.about = data.about;
  company.solo_professional = !!data.solo_professional;
  company.client_types = data.public;
  company.specialties = data.specialties;
  company.CRP = data.CRP;
  if (data.logo)
    company.logo = useRuntimeConfig().public.API_URL + data.logo + "_192.webp";
  if (data.banner)
    company.banner = useRuntimeConfig().public.API_URL + data.banner;
}
onMounted(() => {
  if (useLoginStore().isLoggedIn) getProfile();

  const beamsClient = new PusherPushNotifications.Client({
    instanceId: "c52d25d4-9daf-427e-af4f-1dd96715b6ab",
  });
  beamsClient
    .start()
    .then((beamsClient) => beamsClient.getDeviceId())
    .then((deviceId) => {
      // console.log("Successfully registered with Beams. Device ID:", deviceId);
    })
    .then(() =>
      beamsClient.addDeviceInterest(`user.${useLoginStore().userInfo.phone}`)
    )
    .then(() => beamsClient.getDeviceInterests())
    .then((interests) => console.log("Current interests:", interests))
    .catch(console.error);
});
</script>
