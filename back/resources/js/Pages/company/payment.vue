<script setup lang="ts">
import AppLayout from "@/Layouts/AppLayout.vue";
const { slug } = defineProps({
    slug: { type: String },
    company: { type: Object },
    professional: { type: Object },
});
</script>

<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div class="p-4 md:p-12 min-h-full">
            <h1 class="text-3xl">Pagamento</h1>
            <div class="grid md:grid-cols-2 gap-12 mt-8">
                <input-base label="Nome" />
                <input-base label="Email" />
                <input-base label="Cep" />
            </div>
            <base-button class="float-right mt-10 px-10"> Pagar </base-button>
        </div>
    </AppLayout>
</template>
