import{o as d,c as r,a as s,b as o,u as a,Z as p,w as i,i as l,d as t,F as c}from"./app-BkKCG4YJ.js";import{r as x,_ as u}from"./BaseLayout--Fu-QUOh.js";import{r as f}from"./CalendarDaysIcon-BL3Qt4XI.js";function h(n,m){return d(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"})])}const g={class:"py-2 md:py-10 bg-[#eee] w-full h-layout"},b={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 px-4"},v={class:"overflow-hidden shadow-xl bg-primary sm:rounded-lg p-8 text-white mb-8"},w={class:"flex flex-col md:flex-row items-center"},y={class:"md:w-2/3"},_={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"},B={class:"bg-white overflow-hidden shadow-md rounded-lg p-6"},C={class:"flex items-center mb-4 gap-4"},k={class:"btn btn-primary"},q={class:"bg-white overflow-hidden shadow-md rounded-lg p-6"},A={class:"flex items-center mb-4 gap-4"},j={class:"btn btn-primary"},N={class:"bg-white overflow-hidden shadow-md rounded-lg p-6"},P={class:"flex items-center mb-4 gap-4"},E={class:"btn btn-primary"},M={class:"bg-white overflow-hidden shadow-md rounded-lg p-8 text-center"},T={class:"flex justify-center space-x-4"},H={__name:"Home",setup(n){return(m,e)=>(d(),r(c,null,[o(a(p),{title:"Home"}),o(u,null,{default:i(()=>[s("div",g,[s("div",b,[s("div",v,[s("div",w,[s("div",y,[e[1]||(e[1]=s("h1",{class:"text-3xl md:text-4xl font-bold mb-4"},"Bem-vindo ao Psy+",-1)),e[2]||(e[2]=s("p",{class:"text-lg mb-6"}," Conectando você aos melhores profissionais de saúde mental para cuidar do seu bem-estar. ",-1)),o(a(l),{href:"/profissionais",class:"btn bg-white text-primary hover:bg-gray-100 border-none"},{default:i(()=>e[0]||(e[0]=[t(" Buscar profissionais ")])),_:1})])])]),s("div",_,[s("div",B,[s("div",C,[s("div",k,[o(a(x),{class:"w-6 h-6"})]),e[3]||(e[3]=s("h2",{class:"text-xl leading-6 font-semibold text-gray-700"},[t("Encontre "),s("br"),t(" Especialistas")],-1))]),e[4]||(e[4]=s("p",{class:"text-gray-600"}," Busque profissionais qualificados por especialidade, localização ou disponibilidade. ",-1))]),s("div",q,[s("div",A,[s("div",j,[o(a(f),{class:"w-6 h-6"})]),e[5]||(e[5]=s("h2",{class:"text-xl leading-6 font-semibold text-gray-700"},[t("Agende "),s("br"),t(" Consultas")],-1))]),e[6]||(e[6]=s("p",{class:"text-gray-600"}," Marque consultas de forma rápida e fácil, escolhendo o horário que melhor se adapta à sua rotina. ",-1))]),s("div",N,[s("div",P,[s("div",E,[o(a(h),{class:"w-6 h-6"})]),e[7]||(e[7]=s("h2",{class:"text-xl leading-6 font-semibold text-gray-700"},[t("Acompanhe "),s("br"),t(" seu Progresso")],-1))]),e[8]||(e[8]=s("p",{class:"text-gray-600"}," Tenha acesso ao histórico de consultas e acompanhe sua evolução ao longo do tempo. ",-1))])]),s("div",M,[e[10]||(e[10]=s("h2",{class:"text-2xl font-semibold text-gray-700 mb-4"},"Comece sua jornada de bem-estar hoje",-1)),e[11]||(e[11]=s("p",{class:"text-gray-600 mb-6 max-w-3xl mx-auto"}," Cuidar da saúde mental é tão importante quanto cuidar da saúde física. Dê o primeiro passo para uma vida mais equilibrada e saudável. ",-1)),s("div",T,[o(a(l),{href:"/profissionais",class:"btn btn-primary"},{default:i(()=>e[9]||(e[9]=[t(" Buscar profissionais ")])),_:1})])])])]),e[12]||(e[12]=s("footer",{class:"shadow-xl py-8"},[s("div",{class:"container mx-auto px-4"},[s("div",{class:"grid md:grid-cols-3 gap-8 text-center md:text-left"},[s("div",null,[s("h4",{class:"font-bold text-lg mb-4"},"Psy +"),s("p",{class:"text-sm"},"Conectando pessoas a profissionais de saúde mental")]),s("div",null,[s("h4",{class:"font-bold text-lg mb-4"},"Links Úteis"),s("ul",{class:"space-y-2 text-sm"},[s("li",null,[s("a",{href:"#",class:"hover:text-primary"},"Sobre Nós")]),s("li",null,[s("a",{href:"#",class:"hover:text-primary"},"Contato")]),s("li",null,[s("a",{href:"#",class:"hover:text-primary"},"Blog")])])]),s("div",null,[s("h4",{class:"font-bold text-lg mb-4"},"Contato"),s("p",{class:"text-sm"},"<EMAIL>")])]),s("div",{class:"text-center mt-8 text-sm"},[s("p",null,"© 2025 Psy +. Todos os direitos reservados.")])])],-1))]),_:1})],64))}};export{H as default};
