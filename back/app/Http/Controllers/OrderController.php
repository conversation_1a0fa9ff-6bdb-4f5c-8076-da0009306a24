<?php

namespace App\Http\Controllers;

use App\Events\Notify;
use App\Models\Appointment;
use App\Models\Order;
use App\Models\Company;
use App\Models\CompanyProfessional;
use App\Models\CompanyUser;
use App\Models\Movement;
use App\Models\Service;
use App\Models\Notification;
use AppointmentMade;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Pusher\PushNotifications\PushNotifications;

class OrderController extends Controller
{
    public function getMyOrdersAsClient(Request $request)
    {
        $userId = auth()->id();
        $clientIds = CompanyUser::where('user_id', $userId)->pluck('id');
        $orders = collect();
        foreach ($clientIds as $clientId) {
            $order = Order::with(['appointments.service', 'appointments.professional.user', 'company'])
                ->where('company_user_id', $clientId)
                ->orderByDesc('id')
                ->get();
            // push order to orders array
            $orders = $orders->merge($order);
        }
        return response()->json($orders, 200);
    }

    public function store(Request $request)
    {

        $data = $request->only(['services', 'company', 'isRecurrent', 'recursiveValue']);
        $company = Company::where('slug', $data['company'])->firstOrFail();
        $professionalScheduling = false;
        if (!empty($data['services'][0]['userId'])) {
            $clientId = $data['services'][0]['userId'];
            $client = CompanyUser::find($clientId);
            $professionalScheduling = true;
        } else {
            $userId = auth()->id();
            $client = CompanyUser::where('company_id', $company->id)->where('user_id', $userId)->with('user')->first();
        }
        if (!$client) {
            $client = CompanyUser::create([
                'user_id' => $userId,
                'company_id' => $company->id,
                'total_spent' => 0,
                'total_scheduled' => 0,
            ]);
        }
        DB::beginTransaction();
        try {
            $order = Order::create([
                'company_id' => $company->id,
                'company_user_id' => $client->id,
                'discount' => 0,
                'total' => 0,
                'subtotal' => 0,
                'chat_id' => null,
            ]);
            $totalPrice = 0;
            $companyProfessionals = CompanyProfessional::with('user')->where('company_id', $company->id)->get();
            $appointments = [];
            foreach ($data['services'] as $schedule) {
                if (!isset($schedule['service']['id'], $schedule['date'], $schedule['hour'])) {
                    throw new \Exception("Dados do serviço incompletos.");
                }
                $serviceData = Service::findOrFail($schedule['service']['id']);
                $appointment = [];
                $appointment['service'] = $serviceData->name;
                if (!empty($data['services'][0]['userId'])) {
                    $companyProfessional = CompanyProfessional::with('user')->where('user_id', auth()->id())->where('company_id', $company->id)->first();
                    if ($companyProfessional->role === 'attendant') {
                        $professionalId = $schedule['professional']['id'];
                        $professional = CompanyProfessional::with('user')->where('id', $professionalId)->first();
                        $appointment['professional'] = ['phone' => $professional->user->phone, 'user_id' => $professional->user->id];
                    } else {
                        $professionalId = $companyProfessional->id;
                        $professional = $companyProfessional->user;
                        $appointment['professional'] = $professional->id;
                    }
                } else {
                    $soloProfessional = $companyProfessionals->count() === 1;
                    $professionalId = $soloProfessional ? $companyProfessionals[0]->id : $schedule['professional']['id'];
                    $professionalPhone = $soloProfessional ? $companyProfessionals[0]->user->phone : $schedule['professional']['user']['phone'];
                    $professionalUserId =  $soloProfessional ? $companyProfessionals[0]->user->id : $schedule['professional']['user']['id'];
                    $appointment['professional'] = ['phone' => $professionalPhone, 'user_id' => $professionalUserId];
                }
                // $appointment['start_date'] = $startDate->format('H:i');
                $startDate = Carbon::parse($schedule['date'] . ' ' . $schedule['hour']);
                $appointment['date'] = match (true) {
                    $startDate->isToday() => $startDate->format('H:i') . ' de hoje',
                    $startDate->isTomorrow() => $startDate->format('H:i') . ' de amanhã',
                    default => $startDate->format('H:i') . ' dia ' . $startDate->day . ' de ' . $startDate->translatedFormat('F')
                };
                $endDate = $startDate->copy()->addMinutes($serviceData->duration);
                $appointment['expire_at'] = $endDate->copy()->addDay()->startOfDay()->format('Y-m-d H:i:s');
                $appointments[] = $appointment;
                $conflictingAppointment = Appointment::where('company_professional_id', $professionalId)
                    ->where('status', 'active')
                    ->where(function ($query) use ($startDate, $endDate) {
                        $query->where('start_date', '<', $endDate)
                            ->where('end_date', '>', $startDate);
                    })
                    ->exists();

                if ($conflictingAppointment) {
                    throw new \Exception("Horário indisponível para o profissional selecionado.");
                }

                $newAppointment = Appointment::create([
                    'order_id' => $order->id,
                    'client_name' => $client->user->name,
                    'company_id' => $company->id,
                    'company_professional_id' => $professionalId,
                    'company_user_id' => $client->id,
                    'service_id' => $serviceData->id,
                    'title' => $serviceData->name,
                    'start_date' => $startDate->format('Y-m-d H:i:s'),
                    'end_date' => $endDate->format('Y-m-d H:i:s'),
                    'is_recurrent' => $data['isRecurrent'] ?? false,
                    'recursive_value' => ($data['isRecurrent'] ?? false) ? ($data['recursiveValue'] ?? 'weekly') : null,
                ]);

                // Create recursive appointments if this is a recursive appointment
                if ($data['isRecurrent'] ?? false) {
                    Log::info('Creating recursive appointments', [
                        'isRecurrent' => $data['isRecurrent'],
                        'recursiveValue' => $data['recursiveValue'] ?? 'not set',
                        'originalAppointment' => $newAppointment->id
                    ]);
                    $recursiveAppointments = $this->createRecursiveAppointments($newAppointment, $data, $client, $company, $professionalId, $serviceData);
                    Log::info('Created recursive appointments', ['count' => count($recursiveAppointments)]);
                }

                $totalPrice += $serviceData->price;
            }
            $order->update(['total' => $totalPrice, 'subtotal' => $totalPrice]);
            $client->total_spent = $client->total_spent + $totalPrice;
            $client->total_scheduled = $client->total_scheduled + count($data['services']);
            $client->save();
            DB::commit();
            if (!$professionalScheduling) {

                foreach ($appointments as $appointment) {
                    $description = $client->user->name . ' agendou ' . $appointment['service'] . ' às ' . $appointment['date'];
                    Notification::create([
                        'user_id' => $appointment['professional']['user_id'],
                        'title' => 'Novo agendamento',
                        'description' => $description,
                        'expiration_date' => $appointment['expire_at'],
                        'type' => 'order',
                        'read' => false,
                    ]);
                    $pushNotifications = new PushNotifications(options: array(
                        "instanceId" => env('PUSHER_APP_ID'),
                        "secretKey" => env('PUSHER_APP_SECRET'),
                    ));
                    $pushNotifications->publishToInterests(
                        ['user.' . $appointment['professional']['phone']],
                        [
                            'web' => ['notification' => ['title' => 'Novo agendamento', 'body' => $description]]
                        ]
                    );
                }
            }
            $newOrder = Order::with(['appointments.service', 'appointments.professional.user'])
                ->findOrFail($order->id);
            return response()->json(['order' => $newOrder, 'recursiveAppointments' => $recursiveAppointments ?? []], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            if (isset($order)) {
                Appointment::where('order_id', $order->id)->delete();
                $order->delete();
            }

            return response()->json(['error' => 'Erro ao criar a ordem.', 'message' => $e->getMessage()], 500);
        }
    }

    public function show($id)
    {
        $userId = auth()->id();
        $client = CompanyUser::where('user_id', $userId)->first();
        $order = Order::with('appointments.service')
            ->where('company_user_id', $client->id)
            ->findOrFail($id);

        return response()->json(['order' => $order], 200);
    }

    public function update(Request $request, $id)
    {
        $data = $request->only(['services', 'discount']);
        $order = Order::findOrFail($id);

        $total = collect($data['services'])->sum('price');
        $order->update([
            'subtotal' => $total,
            'discount' => $data['discount'],
            'total' => $total - $data['discount']
        ]);

        return response()->json(['id' => $id], 200);
    }

    public function destroy(Request $request, $id)
    {
        $company_id = $request->get('company_id');
        $deleted = Order::where('id', $id)->where('company_id', $company_id)->delete();

        if ($deleted) {
            return response()->json(['message' => 'Order deleted successfully'], 200);
        }

        return response()->json(['message' => 'Order not found'], 404);
    }

    public function start($id)
    {
        $adminId = auth()->id();

        $order = Order::findOrFail($id);
        $order->status = 'started';
        $order->admin_id = $adminId;
        $order->save();

        return response()->json($order, 200);
    }
    public function finishOrder(int $id, Request $request)
    {
        $order = Order::with(relations: 'client')->find($id);
        if ($order->company->id !== $request->get('company_id')) return response()->json(['message' => 'Order not found'], 404);
        $appointments = Appointment::where('company_user_id', $order->client->id)->where('order_id', $order->id)->get();
        $returns = [];
        foreach ($appointments as $appointment) {
            $request = new Request(['status' => 'finished', 'validate-company' => true, 'company_id' => $order->company->id]);
            $returns[] = $this->finishAppointment($request, $appointment->id);
        }
        $order->finished_at = Carbon::now();
        $order->status = 'finished';
        $order->save();
        return response()->json($returns, 200);
    }
    public function finishMyOrder(int $id, Request $request)
    {
        $user_id = auth()->id();
        $order = Order::with('client')->find($id);
        if ($order->client->user_id !== $user_id) return response()->json(['message' => 'Order not found'], 404);
        $appointments = Appointment::where('company_user_id', $order->client->id)->where('order_id', $order->id)->whereNull('finished_at')->get();
        foreach ($appointments as $appointment) {
            $request = new Request(['status' => 'finished']);
            $this->finishMyAppointment($appointment->id, $request);
        }
        $order->finished_at = Carbon::now();
        $order->save();
        return response()->json('Order finished', 200);
    }
    public function finishMyAppointment(int $id, Request $request)
    {
        $user_id = auth()->id();
        $appointment = Appointment::with('client')->find($id);
        if ($appointment->client->user_id !== $user_id) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        $request = $request->only(['status']);
        $appointment->status = $request['status'];
        $appointment->finished_at = Carbon::now();
        if (!isset($appointment->started_at)) {
            $appointment->started_at = Carbon::now();
        }
        $appointment->save();

        $order = Order::find($appointment->order_id);

        if ($request['status'] === 'finished') {
            $this->generateServiceMovement($appointment);
        }

        $allFinished = true;
        $allCanceled =  true;
        $appointments = Appointment::where('company_user_id', $appointment->client->id)
            ->where('order_id', $appointment->order_id)->whereNull('finished_at')->get();
        foreach ($appointments as $appointment) {
            if (!$appointment->status) {
                $allFinished = false;
                break;
            } else if ($appointment->status === 'finished') {
                $allCanceled = false;
                break;
            }
        }
        if ($allFinished) {
            if ($allCanceled) {
                $order->status = 'canceled';
                $order->save();
            } else {
                $order->finished_at = Carbon::now();
                $order->status = 'finished';
                $totalToReduce = 0;
                foreach ($appointments as $appointment) {
                    if ($appointment->status === 'canceled') {
                        $totalToReduce += $appointment->total;
                    }
                }
                $order->total -= $totalToReduce;
                $order->save();
            }
        }
        return response()->json('Order finished', 200);
    }
    public function finishAppointment(Request $request, string $id)
    {
        $data = $request->only(['status', 'validate-company', 'company_id']);
        $appointment = Appointment::find($id);
        $user_id = auth()->id();
        if (isset($data['validate-company'])) {
            if ($appointment->company_id !== $data['company_id']) return response()->json(['message' => 'Validated appointment not found'], 404);
        } else {
            $professional = CompanyProfessional::where('user_id', $user_id)
                ->where('company_id', $request->get('company_id'))->first();
            if ($appointment->company_professional_id !== $professional->id) return response()->json(['message' => 'Appointment not found'], 404);
        }
        $appointment->status = $request['status'];
        $appointment->finished_at = Carbon::now();
        if (!isset($appointment->started_at)) {
            $appointment->started_at = Carbon::now();
        }
        $appointment->save();
        $order = Order::find($appointment->order_id);
        if ($request['status'] === 'finished') {
            $order->status = 'finished';
            $this->generateServiceMovement($appointment);
        } else {
            $order->status = 'canceled';
        }
        $order->save();
        if ($appointment->status === 'canceled') {
            $order->total -= $appointment->total;
            $companyUser = CompanyUser::find($order->company_user_id);
            $companyUser->total_spent -= $appointment->total;
            $order->save();
        }
        return response()->json('Order finished', 200);
    }

    public function generateServiceMovement(Appointment $appointment)
    {
        $movement = Movement::create([
            'name' => $appointment->title,
            'company_id' => $appointment->company_id,
            'category' => 'service',
            'type' => 'profit',
            'total' => $appointment->service->price,
            'total_earned' => $appointment->service->price,
            'total_spent' => 0,
            'quantity' => 1,
        ]);

        Log::info('Service movement created', [
            'appointment_id' => $appointment->id,
            'movement_id' => $movement->id,
            'service_price' => $appointment->service->price,
            'movement_total' => $movement->total
        ]);
    }


    public function getAllActive()
    {
        $orders = Order::where('status', 'active')
            ->orderByDesc('updated_at')
            ->get();

        return response()->json($orders, 200);
    }

    public function getAllStarted()
    {
        $adminId = auth()->id();

        $orders = Order::with('items')
            ->where('status', 'started')
            ->where('admin_id', $adminId)
            ->orderByDesc('updated_at')
            ->get();

        return response()->json($orders, 200);
    }

    public function getAllFinished()
    {
        $orders = Order::where('status', 'finished')
            ->orderByDesc('updated_at')
            ->get();

        return response()->json($orders, 200);
    }

    public function getCompanyOrders(Request $request)
    {
        $companyID = $request->get('company_id');
        $filterValue = $request->query('value', '');
        $sortBy = $request->query('sort_by', 'created_at');
        $sortDirection = $request->query('direction', 'desc');
        $perPage = $request->query('per_page', 10);
        $query = Order::with('appointments')->where('company_id', $companyID);
        $status = $request->query('status', '');
        if (!empty($filterValue)) {
            $query->where('company_user_id', $filterValue);
        }
        if (!empty($status)) {
            $query->where('status', $status);
        }
        $query->orderBy($sortBy, $sortDirection);

        $result = $query->paginate($perPage);

        return response()->json($result, 200);
    }

    private function createRecursiveAppointments(Appointment $originalAppointment, array $data, $client, $company, $professionalId, $serviceData)
    {
        $recursiveValue = $data['recursiveValue'] ?? 'weekly';
        $startDate = Carbon::parse($originalAppointment->start_date);
        $originalEndDate = Carbon::parse($originalAppointment->end_date);

        // Calculate duration from the original appointment to ensure consistency
        // Use the absolute difference and ensure it's positive
        $duration = abs($startDate->diffInMinutes($originalEndDate));

        Log::info('Duration calculation', [
            'originalStartDate' => $startDate->format('Y-m-d H:i:s'),
            'originalEndDate' => $originalEndDate->format('Y-m-d H:i:s'),
            'serviceDuration' => $serviceData->duration ?? 'not set',
            'calculatedDuration' => $duration,
            'startDateCarbon' => $startDate->toISOString(),
            'endDateCarbon' => $originalEndDate->toISOString()
        ]);

        // Calculate end date for 3 months from now
        $threeMonthsFromNow = Carbon::now()->addMonths(3);

        Log::info('Starting recursive appointment creation', [
            'recursiveValue' => $recursiveValue,
            'startDate' => $startDate->format('Y-m-d H:i:s'),
            'threeMonthsFromNow' => $threeMonthsFromNow->format('Y-m-d H:i:s'),
            'duration' => $duration
        ]);

        $appointments = [];
        $currentDate = $startDate->copy();

        // Determine the interval based on recursiveValue
        $interval = match($recursiveValue) {
            'weekly' => 1, // weeks
            'biweekly' => 2, // weeks
            'monthly' => 1, // months (handled differently)
            default => 1
        };

        while ($currentDate->lte($threeMonthsFromNow)) {
            Log::info('Processing date in loop', [
                'currentDate' => $currentDate->format('Y-m-d H:i:s'),
                'isWithinRange' => $currentDate->lte($threeMonthsFromNow)
            ]);

            // Skip the original appointment date
            if ($currentDate->eq($startDate)) {
                Log::info('Skipping original appointment date');
                $currentDate = $this->getNextRecursiveDate($currentDate, $recursiveValue, $interval);
                continue;
            }

            $appointmentStart = $currentDate->copy()->setTime($startDate->hour, $startDate->minute);
            $appointmentEnd = $appointmentStart->copy()->addMinutes($duration);

            Log::info('Checking time slot availability', [
                'appointmentStart' => $appointmentStart->format('Y-m-d H:i:s'),
                'appointmentEnd' => $appointmentEnd->format('Y-m-d H:i:s')
            ]);

            // Check if this time slot is available
            if ($this->isTimeSlotAvailable($appointmentStart, $appointmentEnd, $professionalId, $company->id)) {
                Log::info('Time slot is available, creating appointment');
                // Create new order for this appointment
                $order = Order::create([
                    'company_id' => $company->id,
                    'company_user_id' => $client->id,
                    'discount' => 0,
                    'total' => $serviceData->price,
                    'subtotal' => $serviceData->price,
                    'chat_id' => null,
                ]);

                $appointment = Appointment::create([
                    'order_id' => $order->id,
                    'client_name' => $client->user->name,
                    'company_id' => $company->id,
                    'company_professional_id' => $professionalId,
                    'company_user_id' => $client->id,
                    'service_id' => $serviceData->id,
                    'title' => $serviceData->name,
                    'start_date' => $appointmentStart->format('Y-m-d H:i:s'),
                    'end_date' => $appointmentEnd->format('Y-m-d H:i:s'),
                    'is_recurrent' => true,
                    'recursive_value' => $recursiveValue,
                ]);

                $appointments[] = $appointment;
            }

            $currentDate = $this->getNextRecursiveDate($currentDate, $recursiveValue, $interval);
        }

        return $appointments;
    }

    private function getNextRecursiveDate(Carbon $currentDate, string $recursiveValue, int $interval): Carbon
    {
        return match($recursiveValue) {
            'weekly' => $currentDate->addWeeks($interval),
            'biweekly' => $currentDate->addWeeks($interval),
            'monthly' => $currentDate->addMonths($interval),
            default => $currentDate->addWeeks(1)
        };
    }

    private function isTimeSlotAvailable(Carbon $startTime, Carbon $endTime, int $professionalId, int $companyId): bool
    {
        // Check for conflicting appointments
        $conflictingAppointments = Appointment::where('company_professional_id', $professionalId)
            ->where('status', 'active')
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where('start_date', '<', $endTime)
                    ->where('end_date', '>', $startTime);
            })
            ->exists();

        return !$conflictingAppointments;
    }
}
