<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onUpdate('cascade')
                ->onDelete('cascade')->nullable();
            $table->foreignId('order_id')->constrained()->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreignId('company_user_id')->constrained()->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreignId('service_id')->constrained()->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreignId('company_professional_id')->constrained()->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('client_name')->nullable();
            $table->string('status')->default('active');
            $table->string('title')->nullable();
            $table->timestamp('start_date')->nullable();;
            $table->timestamp('end_date')->nullable();;
            $table->timestamp('started_at')->nullable();
            $table->timestamp('finished_at')->nullable();
            $table->boolean('is_recurrent')->default(false);
            $table->enum('recursive_value', ['weekly', 'biweekly', 'monthly'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
