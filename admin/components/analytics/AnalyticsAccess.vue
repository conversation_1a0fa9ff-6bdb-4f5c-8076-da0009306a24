<template>
  <div class="space-y-6">
    <!-- Daily Access by Type Chart -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Acesso Diário por Tipo de Usuário</h3>
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>
      <div v-else-if="accessAnalytics.daily_access_by_type?.length" class="h-64">
        <canvas ref="accessChart"></canvas>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- Page Views Chart -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Visualizações de Página ao Longo do Tempo</h3>
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>
      <div v-else-if="accessAnalytics.page_views_over_time?.length" class="h-64">
        <canvas ref="pageViewsChart"></canvas>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- Session Stats -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Estatísticas de Sessão</h3>
      <div v-if="accessAnalytics.user_sessions" class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ accessAnalytics.user_sessions.total_sessions || 0 }}
          </p>
          <p class="text-sm text-gray-600">Total de Sessões</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ Math.round(accessAnalytics.user_sessions.avg_response_time || 0) }}ms
          </p>
          <p class="text-sm text-gray-600">Tempo Médio de Resposta</p>
        </div>
        <div class="text-center p-4 bg-base-200 rounded">
          <p class="text-2xl font-bold text-primary">
            {{ accessAnalytics.user_sessions.total_requests || 0 }}
          </p>
          <p class="text-sm text-gray-600">Total de Requisições</p>
        </div>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- Most Visited Pages -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Páginas Mais Visitadas</h3>
      <div v-if="accessAnalytics.most_visited_pages?.length">
        <table-base
          :columns="pageColumns"
          :rows="accessAnalytics.most_visited_pages"
          :total-items="accessAnalytics.most_visited_pages.length"
          hide-actions
          no-create
          :loading="loading"
        />
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api, getAnalyticsEndpoint } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

const props = defineProps({
  period: {
    type: String,
    default: 'week'
  }
});

const toast = useToast();
const loading = ref(false);
const accessAnalytics = ref({});
const accessChart = ref(null);
const pageViewsChart = ref(null);
let accessChartInstance = null;
let pageViewsChartInstance = null;

const pageColumns = [
  { label: "URL", key: "page_url", sm: true },
  { label: "Visualizações", key: "views" }
];

async function loadAccessAnalytics() {
  try {
    loading.value = true;
    const { data } = await api.get(getAnalyticsEndpoint('/access'), {
      params: { period: props.period }
    });
    accessAnalytics.value = data;

    nextTick(() => {
      createCharts();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar analytics de acesso');
  } finally {
    loading.value = false;
  }
}

function createCharts() {
  createAccessChart();
  createPageViewsChart();
}

function createAccessChart() {
  if (accessChartInstance) {
    accessChartInstance.destroy();
  }

  if (!accessChart.value || !accessAnalytics.value.daily_access_by_type?.length) return;

  const ctx = accessChart.value.getContext('2d');
  const data = accessAnalytics.value.daily_access_by_type;

  // Group data by user type
  const groupedData = {};
  data.forEach(item => {
    if (!groupedData[item.user_type]) {
      groupedData[item.user_type] = [];
    }
    groupedData[item.user_type].push({
      date: item.date,
      unique_users: item.unique_users
    });
  });

  const datasets = Object.keys(groupedData).map((userType, index) => {
    const colors = ['rgb(59, 130, 246)', 'rgb(34, 197, 94)', 'rgb(168, 85, 247)', 'rgb(245, 158, 11)'];
    return {
      label: getUserTypeLabel(userType),
      data: groupedData[userType].map(item => item.unique_users),
      borderColor: colors[index % colors.length],
      backgroundColor: colors[index % colors.length] + '20',
      tension: 0.1
    };
  });

  const labels = [...new Set(data.map(item => item.date))].sort();

  accessChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels,
      datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

function createPageViewsChart() {
  if (pageViewsChartInstance) {
    pageViewsChartInstance.destroy();
  }

  if (!pageViewsChart.value || !accessAnalytics.value.page_views_over_time?.length) return;

  const ctx = pageViewsChart.value.getContext('2d');
  const data = accessAnalytics.value.page_views_over_time;

  pageViewsChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.map(item => item.period),
      datasets: [{
        label: 'Visualizações de Página',
        data: data.map(item => item.count),
        backgroundColor: 'rgba(245, 158, 11, 0.8)',
        borderColor: 'rgb(245, 158, 11)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

function getUserTypeLabel(type: string) {
  const labels = {
    'user': 'Pacientes',
    'admin': 'Profissionais',
    'super_admin': 'Super Admins',
    'guest': 'Visitantes'
  };
  return labels[type] || type;
}

watch(() => props.period, () => {
  loadAccessAnalytics();
});

onMounted(() => {
  loadAccessAnalytics();
});

onUnmounted(() => {
  if (accessChartInstance) {
    accessChartInstance.destroy();
  }
  if (pageViewsChartInstance) {
    pageViewsChartInstance.destroy();
  }
});
</script>
