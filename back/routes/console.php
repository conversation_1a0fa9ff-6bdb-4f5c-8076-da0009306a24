<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Schedule daily metrics generation
Artisan::command('metrics:generate-daily', function () {
    $this->call('metrics:generate-daily');
})->purpose('Generate daily system metrics')->dailyAt('01:00');
