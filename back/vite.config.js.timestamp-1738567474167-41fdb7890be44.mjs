// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/belezura-back/node_modules/vite/dist/node/index.js";
import laravel from "file:///C:/Users/<USER>/Desktop/belezura-back/node_modules/laravel-vite-plugin/dist/index.js";
import vue from "file:///C:/Users/<USER>/Desktop/belezura-back/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import AutoImport from "file:///C:/Users/<USER>/Desktop/belezura-back/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/Desktop/belezura-back/node_modules/unplugin-vue-components/dist/vite.js";
var vite_config_default = defineConfig({
  plugins: [
    laravel({
      input: "resources/js/app.js",
      refresh: true
    }),
    vue({
      template: {
        transformAssetUrls: {
          base: null,
          includeAbsolute: false
        }
      }
    }),
    AutoImport({
      include: [
        /\.[tj]sx?$/,
        // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/,
        // .vue
        /\.md$/
        // .md
      ],
      imports: [
        "vue",
        {
          axios: [
            ["default", "axios"]
            // import { default as axios } from 'axios',
          ],
          "[package-name]": ["[import-names]", ["[from]", "[alias]"]]
        }
      ],
      defaultExportByFilename: true,
      // dirsScanOptions: {
      //     types: true, // Enable auto import the types under the directories
      // },
      dirs: [
        "./resources/js/hooks",
        "./resources/js/composables/**",
        // all nested modules
        "./resources/js/utils",
        {
          glob: "./hooks",
          types: true
          // enable import the types
        },
        {
          glob: "./composables",
          types: false
          // If top level dirsScanOptions.types importing enabled, just only disable this directory
        },
        {
          glob: "./utils",
          types: true
          // enable import the types
        }
      ],
      dts: "./auto-imports.d.ts",
      // Auto import inside Vue template
      // see https://github.com/unjs/unimport/pull/15 and https://github.com/unjs/unimport/pull/72
      vueTemplate: false,
      vueDirectives: void 0,
      resolvers: [
        /* ... */
      ],
      viteOptimizeDeps: true,
      injectAtEnd: true,
      eslintrc: {
        enabled: false,
        // Default `false`
        filepath: "./.eslintrc-auto-import.json",
        // Default `./.eslintrc-auto-import.json`
        globalsPropValue: true
        // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
      },
      // Generate corresponding .biomelintrc-auto-import.json file.
      // biomejs extends Docs - https://biomejs.dev/guides/how-biome-works/#the-extends-option
      biomelintrc: {
        enabled: false,
        // Default `false`
        filepath: "./.biomelintrc-auto-import.json"
        // Default `./.biomelintrc-auto-import.json`
      },
      dumpUnimportItems: "./auto-imports.json"
      // Default `false`
    }),
    Components({
      dirs: ["./resources/js/components"],
      extensions: ["vue"],
      // globs: ["src/components/*.{vue}"],
      deep: true,
      dts: true,
      directoryAsNamespace: true,
      collapseSamePrefixes: false,
      directives: true,
      // importPathTransform: (v) => v,
      include: [/\.vue$/, /\.vue\?vue/],
      exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/],
      excludeNames: [/^Async.+/],
      types: []
    })
  ]
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
