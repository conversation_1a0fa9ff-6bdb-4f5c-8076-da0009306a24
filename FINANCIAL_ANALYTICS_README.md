# Financial Analytics Dashboard

A comprehensive financial analytics system built with Nuxt 3 (frontend) and Laravel 11 (backend) for analyzing payment data, cash flow, and DRE (Demonstração do Resultado do Exercício).

## 🚀 Features

### 📊 Analytics Pages
- **Overview Dashboard** (`/analytics`) - Main dashboard with summary cards and navigation
- **Payment Analysis** (`/analytics/payment-analysis`) - Payment method distribution and analysis
- **Cash Flow** (`/analytics/cash-flow`) - Predicted vs actual cash flow analysis
- **DRE** (`/analytics/dre`) - Income statement analysis with revenue, expenses, and profit

### 🔍 Key Features
- **Interactive Charts** - Using Chart.js for beautiful visualizations
- **Advanced Filtering** - Filter by year, month, date range, and data type
- **CSV Upload** - Upload and process financial data files
- **Real-time Updates** - Data refreshes automatically after uploads
- **Responsive Design** - Works on desktop and mobile devices
- **Brazilian Formatting** - Proper currency and date formatting for Brazil

## 🛠️ Setup Instructions

### Backend (Laravel)
1. **Start Laravel Server**
   ```bash
   cd back
   php artisan serve
   ```
   This will run the backend on `http://localhost:8000`

2. **Database Setup** (if not already done)
   ```bash
   php artisan migrate
   php artisan db:seed --class=FinancialDataSeeder
   ```

### Frontend (Nuxt 3)
1. **Install Dependencies** (if not already done)
   ```bash
   npm install
   ```

2. **Environment Configuration**
   The `.env` file is already configured to point to `http://localhost:8000`

3. **Start Development Server**
   ```bash
   npm run dev
   ```
   This will run the frontend on `http://localhost:3000`

4. **Access the Dashboard**
   - Navigate to `http://localhost:3000/analytics`
   - Login with admin credentials
   - Explore the financial analytics pages

## 📡 API Endpoints

The frontend connects to these Laravel API endpoints:

- `GET /api/financial/` - Overview data with filters
- `GET /api/financial/payment-analysis` - Payment analysis data
- `GET /api/financial/cash-flow` - Cash flow data
- `GET /api/financial/dre` - DRE data
- `POST /api/financial/upload-csv` - Upload CSV files

## 📄 CSV File Formats

### Payment Analysis CSV
```csv
Payment Method,Value,Percentage,Monthly Value
Cartão de Crédito,22500.00,45.67,22500.00
PIX,12500.00,25.33,12500.00
Boleto,7500.00,15.20,7500.00
```

### Cash Flow CSV
```csv
Category,Predicted,Actual
Receitas Operacionais,50000,48000
Vendas de Produtos,35000,33500
Despesas Operacionais,-25000,-26000
```

### DRE CSV
```csv
Categoria,Janeiro,Fevereiro,Março,Abril,Maio,Junho,Julho,Agosto,Setembro,Outubro,Novembro,Dezembro,Total
Receitas Operacionais,48656.40,46161.10,41691.10,42341.10,40191.10,40191.10,40191.10,40191.10,40191.10,40191.10,40191.10,41655.10,501842.50
```

## 📁 File Structure

```
admin/
├── pages/
│   ├── analytics/
│   │   ├── index.vue              # Main dashboard
│   │   ├── overview.vue           # Overview page
│   │   ├── payment-analysis.vue   # Payment analysis
│   │   ├── cash-flow.vue          # Cash flow analysis
│   │   └── dre.vue                # DRE analysis
│   └── login.vue                  # Login page
├── server/
│   └── api/
│       └── index.ts               # API configuration
├── .env                           # Environment variables
└── nuxt.config.ts                 # Nuxt configuration
```

## 🔧 Technologies Used

### Frontend
- **Nuxt 3** - Vue.js framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **DaisyUI** - UI components
- **Chart.js** - Data visualization
- **Pinia** - State management

### Backend
- **Laravel 11** - PHP framework
- **MySQL** - Database
- **Sanctum** - API authentication
- **Carbon** - Date manipulation

## 🎯 Usage Examples

### Viewing Analytics
1. Navigate to `/analytics`
2. Use the filter controls to select time periods
3. Click on the cards to navigate to specific analysis pages
4. Charts update automatically based on filters

### Uploading Data
1. Go to any analytics page (payment-analysis, cash-flow, or dre)
2. Scroll to the upload section at the bottom
3. Select the period date
4. Choose your CSV file
5. Click upload
6. Data will be processed and charts will update

### Filtering Data
- **Year Filter**: Select specific year
- **Month Filter**: Select specific month
- **Date Range**: Use start and end dates for custom ranges
- **Data Type**: Filter by payment analysis, cash flow, or DRE

## 🔒 Authentication

The system uses the existing authentication system:
- Login is required to access analytics pages
- Uses Laravel Sanctum for API authentication
- Supports both admin and super-admin roles

## 📱 Responsive Design

The dashboard is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## 🚨 Important Notes

- Ensure Laravel backend is running on `http://localhost:8000`
- CSV files must follow the exact format specified
- Brazilian number format is supported (comma as decimal separator)
- All monetary values are displayed in Brazilian Real (BRL)

## 🔄 Development Workflow

1. **Backend Changes**: Make changes in `/back` directory
2. **Frontend Changes**: Make changes in `/admin` directory
3. **Testing**: Upload sample CSV files to test functionality
4. **Deployment**: Update `.env` file for production API URL

## 📞 Support

For issues:
1. Check browser console for JavaScript errors
2. Check Laravel logs for backend errors
3. Verify API endpoints are responding correctly
4. Ensure CSV files are in the correct format

The financial analytics system is now fully integrated into the existing admin dashboard! 🎉
