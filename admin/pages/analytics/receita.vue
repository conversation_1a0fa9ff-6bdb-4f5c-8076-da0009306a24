<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Analytics de Receita</h1>
          <div class="flex gap-2">
            <select-base
              v-model="selectedPeriod"
              :options="periodOptions"
              class="min-w-48"
            />
            <base-button @click="refreshData" :loading="loading" size="sm">
              <ArrowPathIcon class="w-4 h-4 mr-2" />
              Atualizar
            </base-button>
          </div>
        </div>

        <div v-if="loading" class="flex justify-center py-20">
          <loading />
        </div>

        <div v-else class="space-y-6">
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Analytics de Receita</h2>
            <p class="text-gray-600 mb-4">Dados detalhados de receita serão exibidos aqui.</p>

            <!-- Sample revenue cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <div class="flex items-center">
                  <div class="text-green-600 text-2xl mr-3">💰</div>
                  <div>
                    <p class="text-sm text-green-600 font-medium">Receita Total</p>
                    <p class="text-2xl font-bold text-green-800">R$ 12.450,00</p>
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div class="flex items-center">
                  <div class="text-blue-600 text-2xl mr-3">📈</div>
                  <div>
                    <p class="text-sm text-blue-600 font-medium">Crescimento</p>
                    <p class="text-2xl font-bold text-blue-800">+15.3%</p>
                  </div>
                </div>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                <div class="flex items-center">
                  <div class="text-purple-600 text-2xl mr-3">🎯</div>
                  <div>
                    <p class="text-sm text-purple-600 font-medium">Meta Mensal</p>
                    <p class="text-2xl font-bold text-purple-800">83%</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4 text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
              💰 Gráficos detalhados de receita em desenvolvimento
              <p class="text-sm mt-2">Esta página está acessível e funcionando corretamente!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon } from "@heroicons/vue/24/solid";
import { useToast } from "vue-toast-notification";

definePageMeta({
  layout: 'professionals'
});

const toast = useToast();
const loading = ref(false);
const selectedPeriod = ref('week');

const periodOptions = [
  { value: 'day', label: 'Últimos 7 dias' },
  { value: 'week', label: 'Últimas 8 semanas' },
  { value: 'month', label: 'Últimos 12 meses' },
  { value: 'year', label: 'Últimos 5 anos' }
];

async function refreshData() {
  loading.value = true;
  // The analytics-revenue component will handle its own data loading
  setTimeout(() => {
    loading.value = false;
    toast.success('Dados atualizados!');
  }, 1000);
}
</script>
