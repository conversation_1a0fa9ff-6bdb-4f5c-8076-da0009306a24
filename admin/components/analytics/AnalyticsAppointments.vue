<template>
  <div class="space-y-6">
    <!-- Appointments Over Time Chart -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Agendamentos ao Longo do Tempo</h3>
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg"></span>
      </div>
      <div v-else-if="appointmentAnalytics.appointments_over_time?.length" class="h-64">
        <canvas ref="appointmentsChart"></canvas>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Appointments by Status -->
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4">Status dos Agendamentos</h3>
        <div v-if="appointmentAnalytics.appointments_by_status?.length" class="space-y-2">
          <div 
            v-for="status in appointmentAnalytics.appointments_by_status" 
            :key="status.status"
            class="flex justify-between items-center p-2 bg-base-200 rounded"
          >
            <span class="capitalize">{{ getStatusLabel(status.status) }}</span>
            <span class="font-semibold">{{ status.count }}</span>
          </div>
        </div>
        <div v-else class="text-center py-4 text-gray-500">
          Nenhum dado disponível
        </div>
      </div>

      <!-- Completion Rate -->
      <div class="bg-base-100 rounded-lg p-4 shadow">
        <h3 class="text-lg font-semibold mb-4">Taxa de Conclusão</h3>
        <div v-if="appointmentAnalytics.appointment_completion_rate" class="space-y-4">
          <div class="text-center">
            <p class="text-3xl font-bold text-primary">
              {{ appointmentAnalytics.appointment_completion_rate.completion_rate }}%
            </p>
            <p class="text-sm text-gray-600">Taxa de Conclusão</p>
          </div>
          <div class="grid grid-cols-2 gap-4 text-center">
            <div>
              <p class="text-xl font-semibold">{{ appointmentAnalytics.appointment_completion_rate.total }}</p>
              <p class="text-xs text-gray-600">Total</p>
            </div>
            <div>
              <p class="text-xl font-semibold">{{ appointmentAnalytics.appointment_completion_rate.completed }}</p>
              <p class="text-xs text-gray-600">Concluídos</p>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-4 text-gray-500">
          Nenhum dado disponível
        </div>
      </div>
    </div>

    <!-- Top Services -->
    <div class="bg-base-100 rounded-lg p-4 shadow">
      <h3 class="text-lg font-semibold mb-4">Serviços Mais Populares</h3>
      <div v-if="appointmentAnalytics.top_services?.length">
        <table-base
          :columns="serviceColumns"
          :rows="appointmentAnalytics.top_services"
          :total-items="appointmentAnalytics.top_services.length"
          hide-actions
          no-create
          :loading="loading"
        >
          <template #revenue="{ value }">
            <td>{{ toBrl(value) }}</td>
          </template>
        </table-base>
      </div>
      <div v-else class="text-center py-8 text-gray-500">
        Nenhum dado disponível
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

const props = defineProps({
  period: {
    type: String,
    default: 'week'
  }
});

const toast = useToast();
const loading = ref(false);
const appointmentAnalytics = ref({});
const appointmentsChart = ref(null);
let appointmentsChartInstance = null;

const serviceColumns = [
  { label: "Serviço", key: "name", sm: true },
  { label: "Agendamentos", key: "count" },
  { label: "Receita", key: "revenue" }
];

function getStatusLabel(status: string) {
  const labels = {
    'pending': 'Pendente',
    'confirmed': 'Confirmado',
    'finished': 'Finalizado',
    'cancelled': 'Cancelado',
    'no_show': 'Não Compareceu'
  };
  return labels[status] || status;
}

async function loadAppointmentAnalytics() {
  try {
    loading.value = true;
    const { data } = await api.get('/super-admin/analytics/appointments', {
      params: { period: props.period }
    });
    appointmentAnalytics.value = data;
    
    nextTick(() => {
      createChart();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar analytics de agendamentos');
  } finally {
    loading.value = false;
  }
}

function createChart() {
  if (appointmentsChartInstance) {
    appointmentsChartInstance.destroy();
  }

  if (!appointmentsChart.value || !appointmentAnalytics.value.appointments_over_time?.length) return;

  const ctx = appointmentsChart.value.getContext('2d');
  const data = appointmentAnalytics.value.appointments_over_time;

  appointmentsChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: data.map(item => item.period),
      datasets: [{
        label: 'Agendamentos',
        data: data.map(item => item.count),
        borderColor: 'rgb(168, 85, 247)',
        backgroundColor: 'rgba(168, 85, 247, 0.1)',
        tension: 0.1,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

watch(() => props.period, () => {
  loadAppointmentAnalytics();
});

onMounted(() => {
  loadAppointmentAnalytics();
});

onUnmounted(() => {
  if (appointmentsChartInstance) {
    appointmentsChartInstance.destroy();
  }
});
</script>
