import { useLoginStore } from "~/store/user";
import { useNavigationBottom } from "~/store/navigationBottom";

export default defineNuxtPlugin(() => {
  // This plugin runs on client-side only to ensure proper auth state hydration
  const loginStore = useLoginStore();

  // Ensure the store is properly hydrated from Pinia persistence
  // Pinia persistence should handle this automatically, but we call this to be sure
  loginStore.hydrateFromPersistedState();

  // Force a navigation refresh if user is logged in to ensure sidebar shows
  if (loginStore.isLoggedIn) {
    const navigationBottom = useNavigationBottom();
    navigationBottom.forceRefresh();
  }
});
