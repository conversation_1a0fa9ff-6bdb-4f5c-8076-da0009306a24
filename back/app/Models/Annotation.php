<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Annotation extends Model
{
    protected $fillable = [
        'company_professional_id',
        'company_user_id',
        'order_id',
        'title',
        'description',
        'appointment_date',
        'status'
    ];

    public function companyProfessional(): BelongsTo
    {
        return $this->belongsTo(CompanyProfessional::class);
    }

    public function companyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
} 