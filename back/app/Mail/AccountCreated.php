<?php

namespace App\Mail;

use App\Helpers\EncryptionHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Company;
use App\Models\CompanyProfessional;
use Illuminate\Support\Facades\Log;

class AccountCreated extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public User $user,
        public CompanyProfessional $admin
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Sua conta foi criada',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        // Encrypt user data for the URL
        try {
            $userData = [
                'name' => $this->user->name,
                'email' => $this->user->email,
                'phone' => $this->user->phone,
                'company_id' => $this->admin->company_id,
                'fixed_field' => 'email' // Email will be the fixed field
            ];
            Log::info('User data for encryption: ' . json_encode($userData));

            $encryptedData = EncryptionHelper::encryptUserData($userData);
            Log::info('Encrypted data length: ' . strlen($encryptedData));

            $completeUrl = url('/' . $this->admin->slug . '/finish-register?data=' . $encryptedData);
            Log::info('Complete URL generated (truncated): ' . substr($completeUrl, 0, 100) . '...');

            return new Content(
                markdown: 'emails.account-created',
                with: [
                    'name' => $this->user->name,
                    'admin' => $this->admin->user->name,
                    'url' => $completeUrl,
                ],
            );
        } catch (\Exception $e) {
            // Log the error but don't stop execution
            Log::error('Failed to encrypt user data: ' . $e->getMessage());

            // Return a fallback content if encryption fails
            return new Content(
                markdown: 'emails.account-created',
                with: [
                    'name' => $this->user->name,
                    'admin' => $this->admin->user->name,
                    'url' => url('/' . $this->admin->slug),
                ],
            );
        }
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
