<template>
    <AppLayout :slug="slug" :company="company" :professional="professional">
        <div class="flex items-center justify-center h-full">
            <div class="card mx-auto w-full max-w-5xl shadow-xl">
                <div class="grid md:grid-cols-2 grid-cols-1 px-2 rounded-xl">
                    <div class="hero min-h-full rounded-l-xl bg-base-200">
                        <img src="/icons/logo.png" alt="Logo" class="w-96 md:pt-0 hidden md:block" />
                    </div>
                    <div
                        class="pt-4 pb-6 sm:py-12 px-4 sm:px-10 bg-base-200 rounded-xl md:rounded-l-none md:rounded-r-xl">
                        <h2 class="text-2xl font-semibold mb-2 text-center">
                            Complete seu Cadastro
                        </h2>
                        <p v-if="error" class="text-error text-center mb-4">{{ error }}</p>
                        <finish-register-form v-if="!error" @submit="handleFormSubmit" :userData="userData" :loading="loading" />
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
<script setup lang="ts">
import { useUser } from "@/store/user";
import { api } from "@/server/api";
import axios from "axios";
import { useToast } from "vue-toast-notification";
import AppLayout from "@/Layouts/AppLayout.vue";
import { router } from "@inertiajs/vue3";
import { onMounted } from "vue";

const auth = useUser();
const toast = useToast();
const loading = ref(false);
const userData = ref(null);
const error = ref("");

const { slug, company, professional, encryptedData } = defineProps({
    slug: { type: String },
    company: { type: Object },
    professional: { type: Object },
    encryptedData: { type: String },
});

onMounted(async () => {
    console.log("Encrypted data received:", encryptedData ? `${encryptedData.substring(0, 50)}...` : 'null');
    if (encryptedData) {
        try {
            console.log("Sending data to decrypt...");
            const response = await api.post("/decrypt-user-data", { data: encryptedData });
            console.log("Decryption response status:", response.status);

            if (response.data && typeof response.data === 'object') {
                userData.value = response.data;
                console.log("Decrypted user data:", userData.value);

                if (!userData.value.name || !userData.value.email || !userData.value.phone) {
                    console.error("Decrypted data is missing required fields");
                    error.value = "Dados incompletos. Por favor, solicite um novo link.";
                }
            } else {
                console.error("Invalid response format:", response.data);
                error.value = "Formato de dados inválido. Por favor, solicite um novo link.";
            }
        } catch (err) {
            error.value = "Dados inválidos ou expirados. Por favor, solicite um novo link.";
            console.error("Error decrypting data:", err.response?.data || err.message || err);
        }
    } else {
        error.value = "Nenhum dado encontrado. Por favor, solicite um novo link.";
    }
});

async function handleFormSubmit(formData) {
    try {
        loading.value = true;
        await api.post("/complete-registration", formData);

        // Login the user after successful registration
        const userToLogin = {
            identifier: formData.email,
            password: formData.password,
            isPhone: false,
        };

        const { data: dataLogin } = await api.post("/login", userToLogin);
        auth.login(dataLogin.token);

        toast.success("Cadastro completado com sucesso!", { position: "top" });

        // Redirect to services page
        router.get(`/${slug}/servicos`);
    } catch (err) {
        if (axios.isAxiosError(err)) {
            toast.error(err.response?.data.message || "Erro ao completar o cadastro", { position: "top" });
        }
    } finally {
        loading.value = false;
    }
}
</script>
