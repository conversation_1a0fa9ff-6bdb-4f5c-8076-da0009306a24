<?php

namespace App\Http\Controllers;

use App\Models\Answer;
use App\Models\CompanyProfessional;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class QuestionController extends Controller
{
    /**
     * Obter a lista de perguntas com paginação e filtragem.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $professional = CompanyProfessional::where('user_id', $user->id)->first();

        $query = Question::query();

        // Filter by the current professional
        if ($professional) {
            $query->where('company_professional_id', $professional->id);
        }

        // Filtragem por label
        if ($request->has('label') && !empty($request->label)) {
            $query->where('label', 'like', '%' . $request->label . '%');
        }

        // Ordenação
        $sortBy = 'created_at';
        $direction = 'asc';
        $query->orderBy($sortBy, $direction);

        $questions = $query->paginate(10);

        return response()->json([
            'data' => $questions->items(),
            'total' => $questions->total(),
            'current_page' => $questions->currentPage(),
            'per_page' => $questions->perPage(),
            'last_page' => $questions->lastPage(),
        ]);
    }

    /**
     * Armazenar uma nova pergunta.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'label' => 'required|string|max:255',
            'isRequired' => 'boolean',
            'options' => 'nullable|array',
            'type' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $professional = CompanyProfessional::where('user_id', $user->id)->first();

        if (!$professional) {
            return response()->json(['error' => 'Professional not found'], 404);
        }

        $payload = [
            ...$request->all(),
            // 'company_id' => $request->get('company_id'),
            'company_professional_id' => $professional->id
        ];

        $question = Question::create($payload);

        // Update hasQuestionary to true for the professional
        if (!$professional->hasQuestionary) {
            $professional->hasQuestionary = true;
            $professional->save();
        }

        return response()->json($question, 201);
    }

    /**
     * Exibir uma pergunta específica.
     */
    public function show(Question $question)
    {
        return response()->json($question);
    }

    /**
     * Atualizar uma pergunta específica.
     */
    public function update(Request $request, Question $question)
    {
        $validator = Validator::make($request->all(), [
            'label' => 'required|string|max:255',
            'isRequired' => 'boolean',
            'options' => 'nullable|array',
            'type' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $question->update($request->all());

        return response()->json($question);
    }

    /**
     * Remover uma pergunta específica.
     */
    public function destroy(Question $question)
    {
        $professional = CompanyProfessional::find($question->company_professional_id);

        if ($professional) {
            $question->delete();

            // Check if the professional has any questions left
            $questionsCount = Question::where('company_professional_id', $professional->id)->count();

            if ($questionsCount === 0) {
                // If no questions left, set hasQuestionary to false
                $professional->hasQuestionary = false;
                $professional->save();
            }

            return response()->json(null, 204);
        }

        $question->delete();
        return response()->json(null, 204);
    }

    /**
     * Get questions by company slug
     */
    public function getQuestionsBySlug(string $slug, Request $request)
    {
        $professional = CompanyProfessional::where('slug', $slug)->firstOrFail();
        $query = Question::where('company_professional_id', $professional->id);
        $questions = $query->get();
        return response()->json($questions);
    }

    /**
     * Get questions by professional
     */
    public function getQuestionsByProfessional()
    {
        $user = Auth::user();
        $professional = CompanyProfessional::where('user_id', $user->id)->first();

        if (!$professional) {
            return response()->json(['error' => 'Professional not found'], 404);
        }

        $questions = Question::where('company_professional_id', $professional->id)->get();

        return response()->json($questions);
    }
}
