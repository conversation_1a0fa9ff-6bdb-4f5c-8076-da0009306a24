class FinancialAPI {
    constructor() {
        this.baseUrl = 'http://localhost:8000/api/financial'; // Change this to your backend URL
    }

    async makeRequest(endpoint, options = {}) {
        try {
            const url = `${this.baseUrl}${endpoint}`;
            const config = {
                headers: auth.getAuthHeaders(),
                ...options
            };

            const response = await fetch(url, config);
            
            if (!response.ok) {
                if (response.status === 401) {
                    auth.logout();
                    return null;
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    }

    async getOverview(filters = {}) {
        const params = new URLSearchParams();
        
        if (filters.year) params.append('year', filters.year);
        if (filters.month) params.append('month', filters.month);
        if (filters.start_date) params.append('start_date', filters.start_date);
        if (filters.end_date) params.append('end_date', filters.end_date);
        if (filters.type) params.append('type', filters.type);

        const queryString = params.toString();
        const endpoint = queryString ? `/?${queryString}` : '/';
        
        return await this.makeRequest(endpoint);
    }

    async getPaymentAnalysis(filters = {}) {
        const params = new URLSearchParams();
        
        if (filters.year) params.append('year', filters.year);
        if (filters.month) params.append('month', filters.month);
        if (filters.start_date) params.append('start_date', filters.start_date);
        if (filters.end_date) params.append('end_date', filters.end_date);

        const queryString = params.toString();
        const endpoint = queryString ? `/payment-analysis?${queryString}` : '/payment-analysis';
        
        return await this.makeRequest(endpoint);
    }

    async getCashFlow(filters = {}) {
        const params = new URLSearchParams();
        
        if (filters.year) params.append('year', filters.year);
        if (filters.start_date) params.append('start_date', filters.start_date);
        if (filters.end_date) params.append('end_date', filters.end_date);

        const queryString = params.toString();
        const endpoint = queryString ? `/cash-flow?${queryString}` : '/cash-flow';
        
        return await this.makeRequest(endpoint);
    }

    async getDre(filters = {}) {
        const params = new URLSearchParams();
        
        if (filters.year) params.append('year', filters.year);
        if (filters.start_date) params.append('start_date', filters.start_date);
        if (filters.end_date) params.append('end_date', filters.end_date);

        const queryString = params.toString();
        const endpoint = queryString ? `/dre?${queryString}` : '/dre';
        
        return await this.makeRequest(endpoint);
    }

    async uploadCsv(file, type, period) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', type);
            formData.append('period', period);

            const response = await fetch(`${this.baseUrl}/upload-csv`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${auth.token}`,
                    'Accept': 'application/json'
                },
                body: formData
            });

            if (!response.ok) {
                if (response.status === 401) {
                    auth.logout();
                    return null;
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Upload error:', error);
            throw error;
        }
    }
}

// Utility functions
class Utils {
    static formatCurrency(value) {
        if (value === null || value === undefined) return 'R$ 0,00';
        
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    }

    static formatPercentage(value) {
        if (value === null || value === undefined) return '0%';
        
        return new Intl.NumberFormat('pt-BR', {
            style: 'percent',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value / 100);
    }

    static formatDate(dateString) {
        if (!dateString) return '-';
        
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('pt-BR').format(date);
    }

    static showLoading() {
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('error-message');
        
        if (loading) loading.style.display = 'block';
        if (errorMessage) errorMessage.style.display = 'none';
    }

    static hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) loading.style.display = 'none';
    }

    static showError(message) {
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('error-message');
        const errorText = document.getElementById('error-text');
        
        if (loading) loading.style.display = 'none';
        if (errorMessage) errorMessage.style.display = 'block';
        if (errorText) errorText.textContent = message;
    }

    static hideError() {
        const errorMessage = document.getElementById('error-message');
        if (errorMessage) errorMessage.style.display = 'none';
    }

    static showUploadResult(message, isSuccess = true) {
        const uploadResult = document.getElementById('upload-result');
        if (uploadResult) {
            uploadResult.textContent = message;
            uploadResult.className = `upload-result ${isSuccess ? 'success' : 'error'}`;
            uploadResult.style.display = 'block';
            
            // Hide after 5 seconds
            setTimeout(() => {
                uploadResult.style.display = 'none';
            }, 5000);
        }
    }

    static populateYearFilter(periods) {
        const yearFilter = document.getElementById('year-filter');
        if (!yearFilter || !periods) return;

        // Clear existing options except "All Years"
        yearFilter.innerHTML = '<option value="">All Years</option>';

        // Get unique years
        const years = [...new Set(periods.map(p => p.year))].sort((a, b) => b - a);
        
        years.forEach(year => {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            yearFilter.appendChild(option);
        });
    }

    static getFilters() {
        return {
            year: document.getElementById('year-filter')?.value || null,
            month: document.getElementById('month-filter')?.value || null,
            start_date: document.getElementById('start-date')?.value || null,
            end_date: document.getElementById('end-date')?.value || null
        };
    }

    static clearFilters() {
        const yearFilter = document.getElementById('year-filter');
        const monthFilter = document.getElementById('month-filter');
        const startDate = document.getElementById('start-date');
        const endDate = document.getElementById('end-date');

        if (yearFilter) yearFilter.value = '';
        if (monthFilter) monthFilter.value = '';
        if (startDate) startDate.value = '';
        if (endDate) endDate.value = '';
    }
}

// Initialize API instance
const financialAPI = new FinancialAPI();
