<?php

namespace App\Console\Commands;

use App\Models\SystemMetric;
use App\Models\User;
use App\Models\Company;
use App\Models\CompanyProfessional;
use App\Models\Appointment;
use App\Models\Order;
use App\Models\AccessLog;
use App\Models\BugReport;
use Carbon\Carbon;
use Illuminate\Console\Command;

class GenerateDailyMetrics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metrics:generate-daily {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate daily system metrics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->argument('date') ? Carbon::parse($this->argument('date')) : Carbon::yesterday();
        
        $this->info("Generating metrics for {$date->toDateString()}...");

        try {
            // User metrics
            $this->generateUserMetrics($date);
            
            // Company metrics
            $this->generateCompanyMetrics($date);
            
            // Appointment metrics
            $this->generateAppointmentMetrics($date);
            
            // Revenue metrics
            $this->generateRevenueMetrics($date);
            
            // Access metrics
            $this->generateAccessMetrics($date);
            
            // Bug report metrics
            $this->generateBugReportMetrics($date);

            $this->info("✅ Daily metrics generated successfully for {$date->toDateString()}");
            
        } catch (\Exception $e) {
            $this->error("❌ Failed to generate metrics: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function generateUserMetrics(Carbon $date)
    {
        $this->info("Generating user metrics...");

        // Daily new registrations
        $newUsers = User::whereDate('created_at', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_new_users', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $newUsers]
        );

        // Total users
        $totalUsers = User::where('created_at', '<=', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'total_users', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $totalUsers]
        );

        // Active users (users who logged in on this date)
        $activeUsers = AccessLog::whereDate('created_at', $date)
            ->where('action', 'login')
            ->distinct('user_id')
            ->count('user_id');
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_active_users', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $activeUsers]
        );

        // User type breakdown
        $usersByType = User::whereDate('created_at', '<=', $date)
            ->selectRaw('type, count(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        SystemMetric::updateOrCreate(
            ['metric_name' => 'users_by_type', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'breakdown', 'value' => 0, 'breakdown' => $usersByType]
        );
    }

    private function generateCompanyMetrics(Carbon $date)
    {
        $this->info("Generating company metrics...");

        // New companies
        $newCompanies = Company::whereDate('created_at', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_new_companies', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $newCompanies]
        );

        // Total companies
        $totalCompanies = Company::where('created_at', '<=', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'total_companies', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $totalCompanies]
        );

        // Active professionals
        $activeProfessionals = CompanyProfessional::where('status', 'active')
            ->where('created_at', '<=', $date)
            ->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'total_active_professionals', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $activeProfessionals]
        );
    }

    private function generateAppointmentMetrics(Carbon $date)
    {
        $this->info("Generating appointment metrics...");

        // Daily appointments created
        $newAppointments = Appointment::whereDate('created_at', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_new_appointments', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $newAppointments]
        );

        // Daily appointments completed
        $completedAppointments = Appointment::whereDate('finished_at', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_completed_appointments', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $completedAppointments]
        );

        // Total appointments
        $totalAppointments = Appointment::where('created_at', '<=', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'total_appointments', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $totalAppointments]
        );

        // Appointment completion rate
        $totalCreated = Appointment::where('created_at', '<=', $date)->count();
        $totalCompleted = Appointment::where('finished_at', '<=', $date)->count();
        $completionRate = $totalCreated > 0 ? round(($totalCompleted / $totalCreated) * 100, 2) : 0;
        
        SystemMetric::updateOrCreate(
            ['metric_name' => 'appointment_completion_rate', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'percentage', 'value' => $completionRate]
        );
    }

    private function generateRevenueMetrics(Carbon $date)
    {
        $this->info("Generating revenue metrics...");

        // Daily revenue
        $dailyRevenue = Order::whereDate('finished_at', $date)
            ->where('status', 'finished')
            ->sum('total');
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_revenue', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'sum', 'value' => $dailyRevenue]
        );

        // Total revenue
        $totalRevenue = Order::where('finished_at', '<=', $date)
            ->where('status', 'finished')
            ->sum('total');
        SystemMetric::updateOrCreate(
            ['metric_name' => 'total_revenue', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'sum', 'value' => $totalRevenue]
        );

        // Average order value
        $avgOrderValue = Order::where('finished_at', '<=', $date)
            ->where('status', 'finished')
            ->avg('total') ?? 0;
        SystemMetric::updateOrCreate(
            ['metric_name' => 'average_order_value', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'average', 'value' => round($avgOrderValue, 2)]
        );
    }

    private function generateAccessMetrics(Carbon $date)
    {
        $this->info("Generating access metrics...");

        // Daily page views
        $pageViews = AccessLog::whereDate('created_at', $date)
            ->where('action', 'page_view')
            ->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_page_views', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $pageViews]
        );

        // Daily API calls
        $apiCalls = AccessLog::whereDate('created_at', $date)
            ->where('action', 'api_call')
            ->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_api_calls', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $apiCalls]
        );

        // Access by user type
        $accessByType = AccessLog::whereDate('created_at', $date)
            ->selectRaw('user_type, count(*) as count')
            ->groupBy('user_type')
            ->pluck('count', 'user_type')
            ->toArray();

        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_access_by_type', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'breakdown', 'value' => 0, 'breakdown' => $accessByType]
        );
    }

    private function generateBugReportMetrics(Carbon $date)
    {
        $this->info("Generating bug report metrics...");

        // Daily new bug reports
        $newBugReports = BugReport::whereDate('created_at', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_new_bug_reports', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $newBugReports]
        );

        // Daily resolved bug reports
        $resolvedBugReports = BugReport::whereDate('resolved_at', $date)->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'daily_resolved_bug_reports', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $resolvedBugReports]
        );

        // Total open bug reports
        $openBugReports = BugReport::where('status', 'open')
            ->where('created_at', '<=', $date)
            ->count();
        SystemMetric::updateOrCreate(
            ['metric_name' => 'total_open_bug_reports', 'date' => $date, 'period' => 'daily'],
            ['metric_type' => 'count', 'value' => $openBugReports]
        );
    }
}
