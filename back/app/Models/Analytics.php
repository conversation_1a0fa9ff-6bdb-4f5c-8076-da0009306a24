<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Analytics extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_type',
        'event_category',
        'event_action',
        'event_label',
        'event_value',
        'user_id',
        'company_id',
        'session_id',
        'ip_address',
        'user_agent',
        'page_url',
        'referrer',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'event_value' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    // Scopes for common queries
    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    // Static methods for tracking common events
    public static function trackPageView($userId = null, $companyId = null, $pageUrl = null, $metadata = [])
    {
        return self::create([
            'event_type' => 'page_view',
            'event_category' => 'navigation',
            'event_action' => 'view',
            'user_id' => $userId,
            'company_id' => $companyId,
            'page_url' => $pageUrl,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'metadata' => $metadata,
        ]);
    }

    public static function trackUserAction($action, $userId = null, $companyId = null, $metadata = [])
    {
        return self::create([
            'event_type' => 'user_action',
            'event_category' => 'interaction',
            'event_action' => $action,
            'user_id' => $userId,
            'company_id' => $companyId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'metadata' => $metadata,
        ]);
    }

    public static function trackAppointment($action, $userId = null, $companyId = null, $appointmentId = null)
    {
        return self::create([
            'event_type' => 'appointment',
            'event_category' => 'booking',
            'event_action' => $action,
            'user_id' => $userId,
            'company_id' => $companyId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'metadata' => ['appointment_id' => $appointmentId],
        ]);
    }
}
