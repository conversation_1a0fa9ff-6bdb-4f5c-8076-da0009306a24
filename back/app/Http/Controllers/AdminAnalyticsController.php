<?php

namespace App\Http\Controllers;

use App\Models\Analytics;
use App\Models\AccessLog;
use App\Models\SystemMetric;
use App\Models\SubscriptionAnalytics;
use App\Models\BugReport;
use App\Models\User;
use App\Models\Company;
use App\Models\CompanyProfessional;
use App\Models\Appointment;
use App\Models\Order;
use App\Models\CompanyUser;
use App\Services\GoogleAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminAnalyticsController extends Controller
{
    protected $googleAnalytics;

    public function __construct(GoogleAnalyticsService $googleAnalytics)
    {
        $this->googleAnalytics = $googleAnalytics;
    }

    /**
     * Get dashboard overview statistics
     */
    public function getDashboardOverview(): JsonResponse
    {
        $today = Carbon::today();
        $lastMonth = Carbon::now()->subMonth();
        $lastWeek = Carbon::now()->subWeek();

        $data = [
            'total_professionals' => CompanyProfessional::where('status', 'active')->count(),
            'total_companies' => Company::count(),
            'total_users' => User::where('type', 'user')->count(),
            'total_appointments' => Appointment::count(),
            'total_revenue' => Order::where('status', 'finished')->sum('total'),

            // Today's stats
            'today' => [
                'new_users' => User::whereDate('created_at', $today)->count(),
                'new_appointments' => Appointment::whereDate('created_at', $today)->count(),
                'revenue' => Order::whereDate('finished_at', $today)->sum('total'),
                'active_users' => AccessLog::whereDate('created_at', $today)
                    ->distinct('user_id')
                    ->count('user_id'),
            ],

            // This week's stats
            'this_week' => [
                'new_users' => User::where('created_at', '>=', $lastWeek)->count(),
                'new_appointments' => Appointment::where('created_at', '>=', $lastWeek)->count(),
                'revenue' => Order::where('finished_at', '>=', $lastWeek)->sum('total'),
            ],

            // This month's stats
            'this_month' => [
                'new_users' => User::where('created_at', '>=', $lastMonth)->count(),
                'new_appointments' => Appointment::where('created_at', '>=', $lastMonth)->count(),
                'revenue' => Order::where('finished_at', '>=', $lastMonth)->sum('total'),
            ],

            // Bug reports summary
            'bug_reports' => [
                'total' => BugReport::count(),
                'open' => BugReport::where('status', 'open')->count(),
                'in_progress' => BugReport::where('status', 'in_progress')->count(),
                'resolved' => BugReport::where('status', 'resolved')->count(),
                'critical' => BugReport::where('severity', 'critical')->count(),
            ],

            // System health (mock data for now)
            'system_health' => [
                'cpu' => rand(20, 80),
                'memory' => rand(30, 70),
                'disk' => rand(15, 60),
            ],
        ];

        return response()->json($data);
    }

    /**
     * Get user analytics
     */
    public function getUserAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week'); // day, week, month, year
        $startDate = $this->getStartDate($period);

        $userStats = [
            'registrations_over_time' => $this->getRegistrationsOverTime($startDate, $period),
            'user_types_breakdown' => $this->getUserTypesBreakdown(),
            'active_users_over_time' => $this->getActiveUsersOverTime($startDate, $period),
            'user_retention' => $this->getUserRetention(),
        ];

        return response()->json($userStats);
    }

    /**
     * Get appointment analytics
     */
    public function getAppointmentAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week');
        $startDate = $this->getStartDate($period);

        $appointmentStats = [
            'appointments_over_time' => $this->getAppointmentsOverTime($startDate, $period),
            'appointments_by_status' => $this->getAppointmentsByStatus(),
            'top_services' => $this->getTopServices(),
            'appointment_completion_rate' => $this->getAppointmentCompletionRate(),
        ];

        return response()->json($appointmentStats);
    }

    /**
     * Get revenue analytics
     */
    public function getRevenueAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week');
        $startDate = $this->getStartDate($period);

        $revenueStats = [
            'revenue_over_time' => $this->getRevenueOverTime($startDate, $period),
            'revenue_by_plan' => $this->getRevenueByPlan(),
            'subscription_analytics' => $this->getSubscriptionAnalytics($startDate),
            'average_order_value' => $this->getAverageOrderValue(),
        ];

        return response()->json($revenueStats);
    }

    /**
     * Get access analytics
     */
    public function getAccessAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week');
        $startDate = $this->getStartDate($period);

        $accessStats = [
            'daily_access_by_type' => $this->getDailyAccessByType($startDate),
            'page_views_over_time' => $this->getPageViewsOverTime($startDate, $period),
            'most_visited_pages' => $this->getMostVisitedPages($startDate),
            'user_sessions' => $this->getUserSessions($startDate),
        ];

        return response()->json($accessStats);
    }

    /**
     * Get bug reports analytics
     */
    public function getBugReportsAnalytics(): JsonResponse
    {
        $bugStats = [
            'reports_by_severity' => BugReport::select('severity', DB::raw('count(*) as count'))
                ->groupBy('severity')
                ->get(),
            'reports_by_status' => BugReport::select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get(),
            'reports_by_category' => BugReport::select('category', DB::raw('count(*) as count'))
                ->whereNotNull('category')
                ->groupBy('category')
                ->get(),
            'recent_reports' => BugReport::with(['reporter', 'assignee'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(),
        ];

        return response()->json($bugStats);
    }

    /**
     * Get Google Analytics data
     */
    public function getGoogleAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', 'week');
        $startDate = $this->getStartDate($period)->toDateString();
        $endDate = Carbon::now()->toDateString();

        $data = [
            'basic_analytics' => $this->googleAnalytics->getBasicAnalytics($startDate, $endDate),
            'page_views_over_time' => $this->googleAnalytics->getPageViewsOverTime($startDate, $endDate),
            'top_pages' => $this->googleAnalytics->getTopPages($startDate, $endDate),
            'user_demographics' => $this->googleAnalytics->getUserDemographics($startDate, $endDate),
            'is_configured' => $this->googleAnalytics->isConfigured(),
        ];

        return response()->json($data);
    }

    // Helper methods
    private function getStartDate($period)
    {
        return match($period) {
            'day' => Carbon::now()->subDays(7),
            'week' => Carbon::now()->subWeeks(8),
            'month' => Carbon::now()->subMonths(12),
            'year' => Carbon::now()->subYears(5),
            default => Carbon::now()->subWeeks(8),
        };
    }

    private function getRegistrationsOverTime($startDate, $period)
    {
        $groupBy = match($period) {
            'day' => 'DATE(created_at)',
            'week' => 'YEARWEEK(created_at)',
            'month' => 'DATE_FORMAT(created_at, "%Y-%m")',
            'year' => 'YEAR(created_at)',
            default => 'DATE(created_at)',
        };

        return User::select(DB::raw("$groupBy as period"), DB::raw('count(*) as count'))
            ->where('created_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    private function getUserTypesBreakdown()
    {
        return User::select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->get();
    }

    private function getActiveUsersOverTime($startDate, $period)
    {
        $groupBy = match($period) {
            'day' => 'DATE(created_at)',
            'week' => 'YEARWEEK(created_at)',
            'month' => 'DATE_FORMAT(created_at, "%Y-%m")',
            'year' => 'YEAR(created_at)',
            default => 'DATE(created_at)',
        };

        return AccessLog::select(DB::raw("$groupBy as period"), DB::raw('count(distinct user_id) as count'))
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('user_id')
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    private function getUserRetention()
    {
        // Calculate user retention rate (users who logged in within 30 days of registration)
        $totalUsers = User::count();
        $retainedUsers = User::whereExists(function ($query) {
            $query->select(DB::raw(1))
                ->from('access_logs')
                ->whereColumn('access_logs.user_id', 'users.id')
                ->where('access_logs.action', 'login')
                ->whereBetween('access_logs.created_at', [
                    DB::raw('users.created_at'),
                    DB::raw('DATE_ADD(users.created_at, INTERVAL 30 DAY)')
                ]);
        })->count();

        return [
            'total_users' => $totalUsers,
            'retained_users' => $retainedUsers,
            'retention_rate' => $totalUsers > 0 ? round(($retainedUsers / $totalUsers) * 100, 2) : 0,
        ];
    }

    private function getAppointmentsOverTime($startDate, $period)
    {
        $groupBy = match($period) {
            'day' => 'DATE(created_at)',
            'week' => 'YEARWEEK(created_at)',
            'month' => 'DATE_FORMAT(created_at, "%Y-%m")',
            'year' => 'YEAR(created_at)',
            default => 'DATE(created_at)',
        };

        return Appointment::select(DB::raw("$groupBy as period"), DB::raw('count(*) as count'))
            ->where('created_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    private function getAppointmentsByStatus()
    {
        return Appointment::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();
    }

    private function getTopServices()
    {
        return DB::table('appointments')
            ->join('services', 'appointments.service_id', '=', 'services.id')
            ->select('services.name', DB::raw('count(*) as count'), DB::raw('sum(services.price) as revenue'))
            ->groupBy('services.id', 'services.name')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();
    }

    private function getAppointmentCompletionRate()
    {
        $total = Appointment::count();
        $completed = Appointment::where('status', 'finished')->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
        ];
    }

    private function getRevenueOverTime($startDate, $period)
    {
        $groupBy = match($period) {
            'day' => 'DATE(finished_at)',
            'week' => 'YEARWEEK(finished_at)',
            'month' => 'DATE_FORMAT(finished_at, "%Y-%m")',
            'year' => 'YEAR(finished_at)',
            default => 'DATE(finished_at)',
        };

        return Order::select(DB::raw("$groupBy as period"), DB::raw('sum(total) as revenue'))
            ->where('status', 'finished')
            ->where('finished_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    private function getRevenueByPlan()
    {
        return DB::table('subscription_analytics')
            ->join('plans', 'subscription_analytics.plan_id', '=', 'plans.id')
            ->select('plans.name', DB::raw('sum(subscription_analytics.amount) as revenue'))
            ->where('subscription_analytics.payment_status', 'completed')
            ->groupBy('plans.id', 'plans.name')
            ->orderBy('revenue', 'desc')
            ->get();
    }

    private function getSubscriptionAnalytics($startDate)
    {
        return [
            'new_subscriptions' => SubscriptionAnalytics::where('event_type', 'subscription_created')
                ->where('created_at', '>=', $startDate)
                ->count(),
            'cancelled_subscriptions' => SubscriptionAnalytics::where('event_type', 'cancelled')
                ->where('created_at', '>=', $startDate)
                ->count(),
            'upgrades' => SubscriptionAnalytics::where('event_type', 'upgraded')
                ->where('created_at', '>=', $startDate)
                ->count(),
            'total_revenue' => SubscriptionAnalytics::where('payment_status', 'completed')
                ->where('created_at', '>=', $startDate)
                ->sum('amount'),
        ];
    }

    private function getAverageOrderValue()
    {
        return Order::where('status', 'finished')->avg('total') ?? 0;
    }

    private function getDailyAccessByType($startDate)
    {
        return AccessLog::select(
                'user_type',
                DB::raw('DATE(created_at) as date'),
                DB::raw('count(distinct user_id) as unique_users'),
                DB::raw('count(*) as total_actions')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('user_type', 'date')
            ->orderBy('date')
            ->get();
    }

    private function getPageViewsOverTime($startDate, $period)
    {
        $groupBy = match($period) {
            'day' => 'DATE(created_at)',
            'week' => 'YEARWEEK(created_at)',
            'month' => 'DATE_FORMAT(created_at, "%Y-%m")',
            'year' => 'YEAR(created_at)',
            default => 'DATE(created_at)',
        };

        return Analytics::select(DB::raw("$groupBy as period"), DB::raw('count(*) as count'))
            ->where('event_type', 'page_view')
            ->where('created_at', '>=', $startDate)
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    private function getMostVisitedPages($startDate)
    {
        return Analytics::select('page_url', DB::raw('count(*) as views'))
            ->where('event_type', 'page_view')
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('page_url')
            ->groupBy('page_url')
            ->orderBy('views', 'desc')
            ->limit(10)
            ->get();
    }

    private function getUserSessions($startDate)
    {
        return AccessLog::select(
                DB::raw('count(distinct session_id) as total_sessions'),
                DB::raw('avg(response_time) as avg_response_time'),
                DB::raw('count(*) as total_requests')
            )
            ->where('created_at', '>=', $startDate)
            ->first();
    }



    /**
     * Get all professionals for admin management
     */
    public function getAllProfessionals(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);
        $name = $request->get('name');
        $sortBy = $request->get('sort_by', 'name');
        $direction = $request->get('direction', 'asc');

        $query = CompanyProfessional::with(['user', 'company'])
            ->join('users', 'company_professionals.user_id', '=', 'users.id')
            ->join('companies', 'company_professionals.company_id', '=', 'companies.id')
            ->select('company_professionals.*', 'users.name', 'users.email', 'users.phone', 'users.created_at as user_created_at', 'companies.name as company_name');

        if ($name) {
            $query->where('users.name', 'like', '%' . $name . '%');
        }

        $query->orderBy($sortBy === 'name' ? 'users.name' : $sortBy, $direction);

        $professionals = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($professionals);
    }

    /**
     * Get all company users (patients) for admin management
     */
    public function getAllCompanyUsers(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 10);
        $page = $request->get('page', 1);
        $name = $request->get('name');
        $sortBy = $request->get('sort_by', 'name');
        $direction = $request->get('direction', 'asc');

        $query = CompanyUser::with(['user', 'company'])
            ->join('users', 'company_users.user_id', '=', 'users.id')
            ->join('companies', 'company_users.company_id', '=', 'companies.id')
            ->select('company_users.*', 'users.name', 'users.email', 'users.phone', 'users.created_at as user_created_at', 'companies.name as company_name');

        if ($name) {
            $query->where('users.name', 'like', '%' . $name . '%');
        }

        $query->orderBy($sortBy === 'name' ? 'users.name' : $sortBy, $direction);

        $companyUsers = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($companyUsers);
    }
}
