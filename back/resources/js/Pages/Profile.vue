<template>
    <Head title="Perfil" />
    <BaseLayout>
        <div class="py-6 w-full bg-[#eee] h-layout">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 px-4">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h1 class="text-2xl font-semibold text-gray-900">Seu Perfil</h1>
                    <div class="mt-6">
                        <div class="flex items-center space-x-4">
                            <div class="avatar">
                                <div class="w-24 h-24 rounded-full">
                                    <img :src="user.profile_photo_url" alt="Profile Photo" />
                                </div>
                            </div>
                            <div>
                                <h2 class="text-xl font-medium text-gray-900">{{ user.name }}</h2>
                                <p class="text-gray-500">{{ user.email }}</p>
                            </div>
                        </div>

                        <div class="mt-8 border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900">Informações Pessoais</h3>
                            <div class="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Nome</label>
                                    <div class="mt-1 p-2 bg-gray-50 rounded-md">{{ user.name }}</div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Email</label>
                                    <div class="mt-1 p-2 bg-gray-50 rounded-md">{{ user.email }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex justify-end">
                            <button class="btn btn-primary">Editar Perfil</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </BaseLayout>
</template>

<script setup>
import { Head } from '@inertiajs/vue3';
import BaseLayout from '@/Layouts/BaseLayout.vue';

const props = defineProps({
    user: Object,
});
</script>
