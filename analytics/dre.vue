<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">DRE - Income Statement</h1>
          <div class="flex gap-2">
            <button @click="refreshData" class="btn btn-primary btn-sm">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
            <NuxtLink to="/analytics" class="btn btn-ghost btn-sm">
              ← Back to Dashboard
            </NuxtLink>
          </div>
        </div>

        <!-- Filter Controls -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
          <div class="flex flex-wrap gap-4 items-end">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Year</span>
              </label>
              <select v-model="filters.year" class="select select-bordered select-sm">
                <option value="">All Years</option>
                <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Start Date</span>
              </label>
              <input v-model="filters.start_date" type="date" class="input input-bordered input-sm">
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">End Date</span>
              </label>
              <input v-model="filters.end_date" type="date" class="input input-bordered input-sm">
            </div>
            <button @click="applyFilters" class="btn btn-primary btn-sm">Apply</button>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">Clear</button>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-8">
          <span class="loading loading-spinner loading-lg"></span>
        </div>

        <!-- DRE Chart -->
        <div v-else class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">DRE - Revenue, Expenses & Profit</h3>
          <div v-if="chartData.labels?.length" class="h-96">
            <canvas ref="dreChart"></canvas>
          </div>
          <div v-else class="text-center py-8 text-gray-500">
            No data available
          </div>
        </div>

        <!-- Key Metrics -->
        <div v-if="keyMetrics" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="stat">
              <div class="stat-title">Total Revenue</div>
              <div class="stat-value text-success">{{ formatCurrency(keyMetrics.revenue) }}</div>
            </div>
          </div>
          <div class="bg-white rounded-lg shadow p-6">
            <div class="stat">
              <div class="stat-title">Total Expenses</div>
              <div class="stat-value text-error">{{ formatCurrency(keyMetrics.expenses) }}</div>
            </div>
          </div>
          <div class="bg-white rounded-lg shadow p-6">
            <div class="stat">
              <div class="stat-title">Net Profit</div>
              <div class="stat-value" :class="keyMetrics.profit >= 0 ? 'text-success' : 'text-error'">
                {{ formatCurrency(keyMetrics.profit) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Data Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-6 border-b">
            <h3 class="text-lg font-semibold">DRE Data</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Value</th>
                  <th>Period</th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="!dreData.data?.length">
                  <td colspan="3" class="text-center py-8 text-gray-500">
                    No data available
                  </td>
                </tr>
                <tr v-for="item in dreData.data" :key="item.id">
                  <td class="font-medium">{{ item.category }}</td>
                  <td :class="getValueClass(item.value)">{{ formatCurrency(item.value) }}</td>
                  <td>{{ formatDate(item.period) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Upload Section -->
        <div class="bg-white rounded-lg shadow p-6 mt-6">
          <h3 class="text-lg font-semibold mb-4">Upload DRE Data</h3>
          <form @submit.prevent="uploadFile" class="space-y-4">
            <div class="form-control">
              <label class="label">
                <span class="label-text">Period</span>
              </label>
              <input v-model="uploadForm.period" type="date" class="input input-bordered" required>
            </div>
            <div class="form-control">
              <label class="label">
                <span class="label-text">CSV File</span>
              </label>
              <input @change="handleFileChange" type="file" accept=".csv,.txt" class="file-input file-input-bordered" required>
            </div>
            <button type="submit" :disabled="uploading" class="btn btn-primary">
              <span v-if="uploading" class="loading loading-spinner loading-sm mr-2"></span>
              {{ uploading ? 'Uploading...' : 'Upload' }}
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { api } from "~/server/api";
import { useToast } from "vue-toast-notification";
import Chart from 'chart.js/auto';

definePageMeta({
  layout: 'professionals'
});

const toast = useToast();
const loading = ref(false);
const uploading = ref(false);
const dreData = ref({ data: [] });
const chartData = ref({ labels: [], datasets: [] });
const keyMetrics = ref(null);
const availableYears = ref([]);

const filters = ref({
  year: '',
  start_date: '',
  end_date: ''
});

const uploadForm = ref({
  period: '',
  file: null
});

const dreChart = ref(null);
let dreChartInstance = null;

async function loadDreData() {
  try {
    loading.value = true;
    const params = Object.fromEntries(
      Object.entries(filters.value).filter(([_, value]) => value !== '')
    );
    
    const { data } = await api.get('/financial/dre', { params });
    dreData.value = data;
    chartData.value = data.chart_data || { labels: [], datasets: [] };
    
    // Calculate key metrics
    calculateKeyMetrics(data.data);
    
    nextTick(() => {
      createChart();
    });
  } catch (error) {
    console.error(error);
    toast.error('Erro ao carregar dados do DRE');
  } finally {
    loading.value = false;
  }
}

function calculateKeyMetrics(data) {
  if (!data?.length) {
    keyMetrics.value = null;
    return;
  }

  let revenue = 0;
  let expenses = 0;
  let profit = 0;

  data.forEach(item => {
    const value = item.value || 0;
    const category = item.category.toLowerCase();
    
    if (category.includes('receita') && !category.includes('despesa')) {
      revenue += Math.abs(value);
    } else if (category.includes('despesa') || category.includes('custo')) {
      expenses += Math.abs(value);
    } else if (category.includes('lucro') || category.includes('prejuízo')) {
      profit = value;
    }
  });

  keyMetrics.value = {
    revenue,
    expenses,
    profit: profit || (revenue - expenses)
  };
}

function createChart() {
  if (dreChartInstance) {
    dreChartInstance.destroy();
  }

  if (!dreChart.value || !chartData.value.labels?.length) return;

  const ctx = dreChart.value.getContext('2d');
  
  dreChartInstance = new Chart(ctx, {
    type: 'bar',
    data: chartData.value,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return formatCurrency(value);
            }
          }
        }
      },
      plugins: {
        legend: {
          position: 'top'
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.dataset.label || '';
              const value = formatCurrency(context.parsed.y);
              return `${label}: ${value}`;
            }
          }
        }
      }
    }
  });
}

function applyFilters() {
  loadDreData();
}

function clearFilters() {
  filters.value = {
    year: '',
    start_date: '',
    end_date: ''
  };
  loadDreData();
}

function refreshData() {
  loadDreData();
}

function handleFileChange(event) {
  uploadForm.value.file = event.target.files[0];
}

async function uploadFile() {
  if (!uploadForm.value.file || !uploadForm.value.period) {
    toast.error('Please fill in all fields');
    return;
  }

  try {
    uploading.value = true;
    const formData = new FormData();
    formData.append('file', uploadForm.value.file);
    formData.append('type', 'dre');
    formData.append('period', uploadForm.value.period);

    await api.post('/financial/upload-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    toast.success('File uploaded successfully!');
    uploadForm.value = { period: '', file: null };
    
    // Reset file input
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
    
    // Refresh data
    setTimeout(() => {
      loadDreData();
    }, 1000);
  } catch (error) {
    console.error(error);
    toast.error('Failed to upload file');
  } finally {
    uploading.value = false;
  }
}

function getValueClass(value: number) {
  if (value > 0) return 'text-success font-semibold';
  if (value < 0) return 'text-error font-semibold';
  return '';
}

function formatCurrency(value: number) {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

function formatDate(dateString: string) {
  if (!dateString) return '-';
  return new Intl.DateTimeFormat('pt-BR').format(new Date(dateString));
}

onMounted(() => {
  loadDreData();
});

onUnmounted(() => {
  if (dreChartInstance) {
    dreChartInstance.destroy();
  }
});
</script>
