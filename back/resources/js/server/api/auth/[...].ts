// import CredentialsProvider from "next-auth/providers/credentials";
// import { Nuxt<PERSON>uth<PERSON>andler } from "#auth";

// export default NuxtAuthHandler({
//   // A secret string you define, to ensure correct encryption
//   secret: "hashit",
//   providers: [
//     // @ts-expect-error You need to use .default here for it to work during SSR. May be fixed via Vite at some point
//     CredentialsProvider.default({
//       name: "Credentials",
//       credentials: {
//         email: {
//           label: "Email",
//           type: "text",
//           placeholder: "(hint: <EMAIL>)",
//         },
//         password: {
//           label: "Password",
//           type: "password",
//           placeholder: "(hint: hunter2)",
//         },
//       },
//     }),
//   ],
//   pages: {
//     signIn: "/login",
//     signOut: "/logout",
//     newUser: "/register",
//   },
// });
