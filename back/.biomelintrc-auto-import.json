{"javascript": {"globals": ["Cash", "Cha<PERSON>", "ChatItem", "Component", "ComponentPublicInstance", "ComputedRef", "Date", "DirectiveBinding", "EffectScope", "ExtractDefaultPropTypes", "ExtractPropTypes", "ExtractPublicPropTypes", "Finance", "Injection<PERSON>ey", "ItemOrder", "MaybeRef", "MaybeRefOrGetter", "Message", "NewMessage", "Order", "Product", "Professional", "PropType", "Ref", "Schedule", "Service", "VNode", "<PERSON><PERSON><PERSON>", "WritableComputedRef", "[alias]", "[import-names]", "anos", "axios", "computed", "createApp", "customRef", "defineAsyncComponent", "defineComponent", "effectScope", "formatPrice", "getContrastColor", "getCurrentInstance", "getCurrentScope", "getPhoto", "h", "handleTime", "inject", "isProxy", "isReactive", "is<PERSON><PERSON><PERSON>ly", "isRef", "mark<PERSON>aw", "meses", "newOrder", "nextTick", "onActivated", "onBeforeMount", "onBeforeUnmount", "onBeforeUpdate", "onDeactivated", "onErrorCaptured", "onMounted", "onRenderTracked", "onRenderTriggered", "onScopeDispose", "onServerPrefetch", "onUnmounted", "onUpdated", "onWatcherCleanup", "provide", "reactive", "readonly", "ref", "resolveComponent", "shallowReactive", "shallowReadonly", "shallowRef", "toRaw", "toRef", "toRefs", "toValue", "triggerRef", "unref", "useAttrs", "useCssModule", "useCssVars", "useId", "useModel", "useSlots", "useTemplateRef", "watch", "watchEffect", "watchPostEffect", "watchSyncEffect"]}}